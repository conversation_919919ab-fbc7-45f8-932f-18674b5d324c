import {
  CLASSES_TYPE,
  DURATION_TYPE,
  IN_PERSON_TYPE,
  ONLINE_CLASSES_TYPE,
} from "@/constant/Enums";

const GROUP_INPERSON = [
  {
    uniqueId: 11,
    isPopular: false,
    isLimitedDeal: true,
    type: CLASSES_TYPE.IN_PERSON,
    durationType: DURATION_TYPE.WEEK,
    subType: IN_PERSON_TYPE.GROUP,
    title: "1 Week Plan",
    subtitle: "",
    benefits:
      "Join a supportive group environment to enhance your learning experience. Perfect for those who thrive in collaborative settings.",
    description:
      "Weekly in-person group classes designed to help you achieve your goals with the support of peers and expert instructors.",
    plans: [
      {
        planId: "1",
        price: 120,
        singlePrice: 120,
        discount: 0,
        duration: 1,
      },
      {
        planId: "2",
        price: 480,
        singlePrice: 120,
        discount: 0,
        duration: 4,
      },
    ],
  },
  {
    uniqueId: 12,
    isPopular: true,
    title: "4 Week Plan",
    subtitle: "",
    isLimitedDeal: true,
    type: CLASSES_TYPE.IN_PERSON,
    durationType: DURATION_TYPE.WEEK,
    subType: IN_PERSON_TYPE.GROUP,
    benefits:
      "Enjoy flexible scheduling and cost savings with multi-week packages. Ideal for committed learners looking for long-term progress.",
    description:
      "Flexible in-person group classes available in weekly or monthly packages, offering a structured yet adaptable learning experience.",
    plans: [
      {
        planId: "1",
        price: 410,
        singlePrice: 120.5,
        discount: 14.58,
        duration: 4,
      },
    ],
  },
];

const PRIVATE_INPERSON = [
  {
    uniqueId: 21,
    isPopular: true,
    isLimitedDeal: true,
    type: CLASSES_TYPE.IN_PERSON,
    durationType: DURATION_TYPE.HOUR,
    subType: IN_PERSON_TYPE.PRIVATE,
    title: "Single Hour Session",
    subtitle: "",
    benefits:
      "Personalized one-on-one attention tailored to your specific needs and goals. Perfect for fast-paced learning and focused progress.",
    description:
      "Hourly in-person private sessions designed to provide customized guidance and accelerate your learning journey.",
    plans: [
      { planId: "1", price: 16, singlePrice: 16, discount: 0, duration: 1 },
      { planId: "2", price: 160, singlePrice: 16, discount: 0, duration: 10 },
      { planId: "2", price: 320, singlePrice: 16, discount: 0, duration: 20 },
    ],
  },
  {
    uniqueId: 22,
    isPopular: true,
    isLimitedDeal: true,
    type: CLASSES_TYPE.IN_PERSON,
    durationType: DURATION_TYPE.HOUR,
    title: "10 - Hour Bundle",
    subtitle: "",
    subType: IN_PERSON_TYPE.PRIVATE,
    benefits:
      "Flexible scheduling and cost savings with multi-session packages. Ideal for dedicated learners seeking consistent, personalized support.",
    description:
      "Flexible in-person private sessions available in hourly or package deals, offering tailored instruction and long-term progress.",
    plans: [
      {
        planId: "1",
        price: 135,
        singlePrice: 13.5,
        discount: 15.63,
        duration: 10,
      },
    ],
  },
  {
    uniqueId: 23,
    isPopular: true,
    isLimitedDeal: true,
    title: "20 - Hour Bundle",
    subtitle: "",
    type: CLASSES_TYPE.IN_PERSON,
    durationType: DURATION_TYPE.HOUR,
    subType: IN_PERSON_TYPE.PRIVATE,
    benefits:
      "Flexible scheduling and cost savings with multi-session packages. Ideal for dedicated learners seeking consistent, personalized support.",
    description:
      "Flexible in-person private sessions available in hourly or package deals, offering tailored instruction and long-term progress.",
    plans: [
      {
        planId: "1",
        price: 260,
        singlePrice: 13,
        discount: 18.75,
        duration: 20,
      },
    ],
  },
];

const IN_PERSON_PRICING = [
  //  GROUP
  ...GROUP_INPERSON,
  //   PRIVATE
  ...PRIVATE_INPERSON,
];

const QUICK_SESSIONS = [
  {
    uniqueId: 31,
    isPopular: true,
    isLimitedDeal: true,
    title: "30 Mins Class",
    subtitle: "",
    type: CLASSES_TYPE.ONLINE,
    durationType: DURATION_TYPE.THIRTY_MINUTES,
    subType: ONLINE_CLASSES_TYPE.QUICK_SESSIONS,
    benefits:
      "Short, focused online sessions designed to fit into your busy schedule. Perfect for quick learning or refreshing your skills.",
    description:
      "Quick online sessions in minute-based durations, offering flexibility and efficiency for learners on the go.",
    plans: [
      {
        planId: "1",
        price: 7,
        singlePrice: 7,
        discount: 0,
        duration: 1,
      },
      {
        planId: "2",
        price: 70,
        singlePrice: 7,
        discount: 0,
        duration: 10,
      },
    ],
  },
  {
    uniqueId: 32,
    isPopular: true,
    title: "10 Sessions",
    subtitle: "30 Minutes Each",
    isLimitedDeal: true,
    type: CLASSES_TYPE.ONLINE,
    durationType: DURATION_TYPE.THIRTY_MINUTES,
    subType: ONLINE_CLASSES_TYPE.QUICK_SESSIONS,
    benefits:
      "Affordable and time-efficient online sessions for consistent learning. Great for building skills over time without a major time commitment.",
    description:
      "Bundled quick online sessions in minute-based packages, providing a cost-effective way to stay consistent with your learning goals.",
    plans: [
      {
        planId: "1",
        price: 60,
        singlePrice: 6,
        discount: 14.29,
        duration: 10,
      },
    ],
  },
];

const REGULAR_SESSIONS = [
  {
    uniqueId: 41,
    isPopular: true,
    isLimitedDeal: true,
    type: CLASSES_TYPE.ONLINE,
    durationType: DURATION_TYPE.HOUR,
    subType: ONLINE_CLASSES_TYPE.REGULAR_SESSIONS,
    title: "1 hour pack",
    subtitle: "",
    benefits:
      "Structured online sessions designed for consistent learning and skill-building. Perfect for those who prefer a steady, guided approach.",
    description:
      "Hourly online regular sessions offering a structured learning experience with flexible duration options to suit your needs.",
    plans: [
      {
        planId: "1",
        price: 15,
        singlePrice: 15,
        discount: 0,
        duration: 1,
      },
      {
        planId: "2",
        price: 150,
        singlePrice: 15,
        discount: 0,
        duration: 10,
      },
      {
        planId: "3",
        price: 300,
        singlePrice: 15,
        discount: 0,
        duration: 20,
      },
    ],
  },
  {
    uniqueId: 42,
    isPopular: true,
    isLimitedDeal: true,
    title: "10 hour pack",
    subtitle: "",
    type: CLASSES_TYPE.ONLINE,
    durationType: DURATION_TYPE.HOUR,
    subType: ONLINE_CLASSES_TYPE.REGULAR_SESSIONS,
    benefits:
      "Affordable and flexible online sessions for learners who want to progress at their own pace. Ideal for balancing learning with other commitments.",
    description:
      "Flexible hourly online regular sessions with bundled options, providing a cost-effective way to achieve your learning goals.",
    plans: [
      {
        planId: "1",
        price: 125,
        singlePrice: 12.5,
        discount: 16.67,
        duration: 10,
      },
    ],
  },
  {
    uniqueId: 43,
    isPopular: true,
    isLimitedDeal: true,
    type: CLASSES_TYPE.ONLINE,
    title: "20 hours pack",
    subtitle: "",
    durationType: DURATION_TYPE.HOUR,
    subType: ONLINE_CLASSES_TYPE.REGULAR_SESSIONS,
    benefits:
      "Comprehensive online sessions with multiple duration options, offering flexibility and value for dedicated learners.",
    description:
      "Hourly online regular sessions with a variety of plans, designed to provide a structured yet adaptable learning experience.",
    plans: [
      {
        planId: "1",
        price: 240,
        singlePrice: 12,
        discount: 20,
        duration: 20,
      },
    ],
  },
];

const LONG_SESSIONS = [
  {
    uniqueId: 51,
    isPopular: true,
    isLimitedDeal: true,
    type: CLASSES_TYPE.ONLINE,
    durationType: DURATION_TYPE.NINETY_MINUTES,
    subType: ONLINE_CLASSES_TYPE.LONG_SESSIONS,
    title: "90 minutes session",
    subtitle: "",
    benefits:
      "Structured online sessions designed for consistent learning and skill-building. Perfect for those who prefer a steady, guided approach.",
    description:
      "Hourly online regular sessions offering a structured learning experience with flexible duration options to suit your needs.",
    plans: [
      {
        planId: "1",
        price: 22,
        singlePrice: 22,
        discount: 0,
        duration: 1,
      },
      {
        planId: "2",
        price: 220,
        singlePrice: 22,
        discount: 0,
        duration: 10,
      },
      {
        planId: "3",
        price: 440,
        singlePrice: 22,
        discount: 0,
        duration: 20,
      },
    ],
  },
  {
    uniqueId: 52,
    isPopular: true,
    isLimitedDeal: true,
    title: "10 sessions",
    subtitle: "90 minutes each",
    type: CLASSES_TYPE.ONLINE,
    durationType: DURATION_TYPE.NINETY_MINUTES,
    subType: ONLINE_CLASSES_TYPE.LONG_SESSIONS,
    benefits:
      "Affordable and flexible online sessions for learners who want to progress at their own pace. Ideal for balancing learning with other commitments.",
    description:
      "Flexible hourly online regular sessions with bundled options, providing a cost-effective way to achieve your learning goals.",
    plans: [
      {
        planId: "1",
        price: 18.5,
        singlePrice: 185,
        discount: 15.91,
        duration: 10,
      },
    ],
  },
  {
    uniqueId: 53,
    isPopular: true,
    isLimitedDeal: true,
    type: CLASSES_TYPE.ONLINE,
    title: "20 sessions",
    subtitle: "90 minutes each",
    durationType: DURATION_TYPE.NINETY_MINUTES,
    subType: ONLINE_CLASSES_TYPE.LONG_SESSIONS,
    benefits:
      "Comprehensive online sessions with multiple duration options, offering flexibility and value for dedicated learners.",
    description:
      "Hourly online regular sessions with a variety of plans, designed to provide a structured yet adaptable learning experience.",
    plans: [
      {
        planId: "1",
        price: 350,
        singlePrice: 17.5,
        discount: 20.45,
        duration: 20,
      },
    ],
  },
];

const ONLINE_CLASSES_PRICING = [
  //  QUICK SESSION
  ...QUICK_SESSIONS,
  //  REGULAR SESSION
  ...REGULAR_SESSIONS,
  // LONG_SESSIONS
  ...LONG_SESSIONS,
];

const ALL_CLASSES_PRICING = [...IN_PERSON_PRICING, ...ONLINE_CLASSES_PRICING];

const CLASSES_TYPES = [
  {
    id: 1,
    name: "In-Person Classes",
    type: CLASSES_TYPE.IN_PERSON,
    subType: [
      {
        id: 1,
        name: "Group",
        value: IN_PERSON_TYPE.GROUP,
        data: GROUP_INPERSON,
      },
      {
        id: 2,
        name: "Private",
        value: IN_PERSON_TYPE.PRIVATE,
        data: PRIVATE_INPERSON,
      },
    ],
  },
  {
    id: 2,
    name: "Online Classes",
    type: CLASSES_TYPE.ONLINE,
    subType: [
      {
        id: 2,
        name: "Quick Session",
        value: ONLINE_CLASSES_TYPE.QUICK_SESSIONS,
        data: QUICK_SESSIONS,
      },
      {
        id: 3,
        name: "Regular Session",
        value: ONLINE_CLASSES_TYPE.REGULAR_SESSIONS,
        data: REGULAR_SESSIONS,
      },
      {
        id: 3,
        name: "Long Session",
        value: ONLINE_CLASSES_TYPE.LONG_SESSIONS,
        data: LONG_SESSIONS,
      },
    ],
  },
];

export {
  IN_PERSON_PRICING,
  ONLINE_CLASSES_PRICING,
  GROUP_INPERSON,
  PRIVATE_INPERSON,
  QUICK_SESSIONS,
  REGULAR_SESSIONS,
  LONG_SESSIONS,
  CLASSES_TYPES,
  ALL_CLASSES_PRICING,
};
