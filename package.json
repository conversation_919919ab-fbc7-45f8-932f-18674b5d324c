{"name": "patito-feo-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "devMobile": "next dev -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.341.0", "@aws-sdk/client-ses": "^3.341.0", "@aws-sdk/s3-request-presigner": "^3.678.0", "@clerk/nextjs": "^6.8.0", "@emotion/react": "^11.11.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@headlessui/react": "^1.7.15", "@heroicons/react": "1.0.6", "@hookform/resolvers": "^3.1.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.0", "@mui/x-date-pickers": "^7.23.1", "@react-spring/core": "^9.7.3", "@react-spring/web": "^9.7.3", "@tailwindcss/forms": "0.5.2", "@types/react-beautiful-dnd": "^13.1.8", "@types/ua-parser-js": "^0.7.36", "@use-gesture/react": "^10.2.27", "argon2": "^0.30.3", "aws-sdk": "^2.1386.0", "axios": "^1.4.0", "classnames": "^2.3.2", "cookies-next": "^2.1.1", "date-fns": "^2.30.0", "date-fns-tz": "^1.3.7", "egoroof-blowfish": "^4.0.1", "eslint-config-next": "13.4.2", "get-blob-duration": "^1.2.0", "grapheme-splitter": "^1.0.4", "immutability-helper": "^3.1.1", "iron-session": "^6.3.1", "js-cookie": "^3.0.5", "libphonenumber-js": "^1.10.34", "micro": "^10.0.1", "mongoose": "^7.1.1", "next": "^13.5.2", "next-auth": "^4.22.1", "query-string": "^8.1.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-cookie": "^4.1.1", "react-countdown": "^2.3.5", "react-datepicker": "^4.14.0", "react-div-100vh": "^0.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.5", "react-force-graph": "^1.43.0", "react-hook-form": "^7.43.9", "react-infinite-scroll-component": "^6.1.0", "react-phone-number-input": "^3.4.12", "react-query": "^3.39.3", "react-responsive": "^9.0.2", "react-share": "^4.4.1", "react-spring": "^9.7.1", "react-use-gesture": "^9.1.3", "react-visibility-sensor": "^5.1.1", "react-whatsapp": "^0.3.0", "resize-observer-polyfill": "^1.5.1", "sharp": "^0.32.5", "stripe": "^17.5.0", "three-spritetext": "^1.8.1", "ua-parser-js": "^1.0.35", "uuid": "^10.0.0", "validator": "^13.9.0", "video-react": "^0.16.0", "zod": "^3.21.4"}, "devDependencies": {"@types/react-datepicker": "4.4.2", "autoprefixer": "^10.4.14", "eslint-config-prettier": "^8.8.0", "postcss": "^8.4.23", "tailwindcss": "^3.3.2", "typescript": "^5.0.4"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}