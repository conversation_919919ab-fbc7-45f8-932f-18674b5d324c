<svg width="331" height="440" viewBox="0 0 331 440" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#a)" fill="#00847A">
    <path d="M241.222 386.707c-8.408 14.959-5.535 37.689.778 37.689 6.321 0 10.212-22.477.83-37.721a.927.927 0 0 0-1.608.032"/>
    <ellipse cx="265" cy="417.896" rx="8" ry="8.5" transform="rotate(-180 265 417.896)"/>
    <ellipse cx="219" cy="417.896" rx="8" ry="8.5" transform="rotate(-180 219 417.896)"/>
    <circle cx="226" cy="398.396" r="3" transform="rotate(-180 226 398.396)"/>
    <circle cx="217" cy="396.396" r="3" transform="rotate(-180 217 396.396)"/>
    <circle cx="208" cy="399.396" r="3" transform="rotate(-180 208 399.396)"/>
    <circle cx="202" cy="406.396" r="3" transform="rotate(-180 202 406.396)"/>
    <circle cx="198" cy="415.396" r="3" transform="rotate(-180 198 415.396)"/>
    <circle cx="199" cy="424.396" r="3" transform="rotate(-180 199 424.396)"/>
    <circle cx="3" cy="3" r="3" transform="matrix(1 0 0 -1 256 401.396)"/>
    <circle cx="3" cy="3" r="3" transform="matrix(1 0 0 -1 265 399.396)"/>
    <circle cx="3" cy="3" r="3" transform="matrix(1 0 0 -1 274 402.396)"/>
    <circle cx="3" cy="3" r="3" transform="matrix(1 0 0 -1 280 409.396)"/>
    <circle cx="3" cy="3" r="3" transform="matrix(1 0 0 -1 284 418.396)"/>
    <circle cx="3" cy="3" r="3" transform="matrix(1 0 0 -1 283 427.396)"/>
    <circle cx="175" cy="433.396" r="4" transform="rotate(-180 175 433.396)"/>
    <circle cx="177" cy="419.396" r="4" transform="rotate(-180 177 419.396)"/>
    <circle cx="184" cy="407.396" r="4" transform="rotate(-180 184 407.396)"/>
    <circle cx="191" cy="395.396" r="4" transform="rotate(-180 191 395.396)"/>
    <circle cx="201" cy="385.396" r="4" transform="rotate(-180 201 385.396)"/>
    <circle cx="214" cy="379.396" r="4" transform="rotate(-180 214 379.396)"/>
    <circle cx="227" cy="375.396" r="4" transform="rotate(-180 227 375.396)"/>
    <circle cx="242" cy="373.396" r="4" transform="rotate(-180 242 373.396)"/>
    <circle cx="4" cy="4" r="4" transform="matrix(1 0 0 -1 305 437.396)"/>
    <circle cx="4" cy="4" r="4" transform="matrix(1 0 0 -1 303 423.396)"/>
    <circle cx="4" cy="4" r="4" transform="matrix(1 0 0 -1 296 411.396)"/>
    <circle cx="4" cy="4" r="4" transform="matrix(1 0 0 -1 289 399.396)"/>
    <circle cx="4" cy="4" r="4" transform="matrix(1 0 0 -1 279 389.396)"/>
    <circle cx="4" cy="4" r="4" transform="matrix(1 0 0 -1 266 383.396)"/>
    <circle cx="4" cy="4" r="4" transform="matrix(1 0 0 -1 253 379.396)"/>
    <circle cx="4" cy="4" r="4" transform="matrix(1 0 0 -1 238 377.396)"/>
    <path d="M257 439.896c16.209-9.554 25.292-9.734 41.5 0zm-69.5.5c16.209-9.554 25.292-9.734 41.5 0zm63.5-.5c-4.5-8.5-13-9-16.5 0z"/>
    <g clip-path="url(#b)">
      <path d="M252.656 220c-3.338 0-3.303 3.507-.206 10.205a.207.207 0 0 0 .383-.011c2.427-6.224 3.153-10.194-.177-10.194m-.264 33c3.337 0 3.303-3.507.205-10.206a.207.207 0 0 0-.382.012c-2.427 6.224-3.153 10.194.177 10.194M236.5 235.326c0 3.386 3.456 3.351 10.058.208a.212.212 0 0 0-.011-.388c-6.134-2.463-10.047-3.199-10.047.18m32 .268c0-3.387-3.456-3.352-10.058-.209a.213.213 0 0 0 .011.389c6.134 2.462 10.047 3.199 10.047-.18m-27.073-11.051c-2.36 2.395.108 4.85 6.967 7.364.172.063.335-.112.263-.282-2.622-6.143-4.875-9.471-7.23-7.082m22.441 23.148c2.409-2.445-.215-4.953-7.408-7.523q-.007-.001-.006.007c2.7 6.419 5.004 9.961 7.414 7.516m-22.628-.722c2.36 2.395 4.78-.109 7.258-7.069.062-.174-.111-.34-.279-.266-6.054 2.659-9.334 4.946-6.979 7.335m22.814-22.769c-2.36-2.394-4.779.11-7.257 7.069-.062.174.111.341.278.267 6.054-2.66 9.334-4.946 6.979-7.336M252.5 239.428c1.883 0 3.41-1.549 3.41-3.46s-1.527-3.46-3.41-3.46-3.41 1.549-3.41 3.46 1.527 3.46 3.41 3.46"/>
    </g>
    <g clip-path="url(#c)">
      <path d="M95.131 392.124c-2.816 0-2.787 2.975-.173 8.659a.175.175 0 0 0 .323-.009c2.048-5.281 2.66-8.65-.15-8.65m-.222 28c2.816 0 2.787-2.975.173-8.659a.175.175 0 0 0-.323.009c-2.048 5.281-2.66 8.65.15 8.65M81.5 405.128c0 2.873 2.916 2.843 8.487.177a.181.181 0 0 0-.01-.33c-5.175-2.089-8.477-2.714-8.477.153m27 .227c0-2.873-2.916-2.844-8.487-.177-.14.067-.134.272.009.329 5.176 2.09 8.478 2.715 8.478-.152m-22.843-9.376c-1.991 2.032.091 4.115 5.879 6.248.144.054.283-.095.221-.239-2.212-5.212-4.113-8.036-6.1-6.009m18.934 19.64c2.034-2.074-.181-4.202-6.25-6.383q-.006 0-.005.006c2.278 5.446 4.222 8.452 6.255 6.377m-19.091-.612c1.99 2.032 4.032-.093 6.123-5.998.052-.148-.093-.289-.235-.226-5.108 2.257-7.875 4.197-5.889 6.224m19.25-19.319c-1.991-2.032-4.033.093-6.124 5.998-.052.148.094.288.235.226 5.108-2.257 7.876-4.197 5.889-6.224M95 408.608c1.589 0 2.877-1.314 2.877-2.936s-1.288-2.935-2.877-2.935-2.877 1.314-2.877 2.935c0 1.622 1.288 2.936 2.877 2.936"/>
    </g>
    <circle cx="20.5" cy="402.124" r="2"/>
    <circle cx="54.5" cy="432.124" r="2"/>
  </g>
  <defs>
    <clipPath id="a">
      <rect x=".5" width="330.5" height="440" rx="12" fill="#fff"/>
    </clipPath>
    <clipPath id="b">
      <path fill="#fff" d="M236.5 220h32v33h-32z"/>
    </clipPath>
    <clipPath id="c">
      <path fill="#fff" d="M81.5 392.124h27v28h-27z"/>
    </clipPath>
  </defs>
</svg>
