import { ENVIRONMENT } from "@/constant/Enums";
import {
  S3,
  PutObjectCommand,
  GetObjectCommand,
  HeadObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

const NEXT_PUBLIC_ENVIRONMENT = process.env.NEXT_PUBLIC_ENVIRONMENT;
const BUCKET_NAME = process.env.NEXT_PUBLIC_AWS_BUCKET_NAME;

const s3 = new S3({
  region: process.env.NEXT_PUBLIC_AWS_DEFAULT_REGION,
  credentials: {
    accessKeyId: process.env.NEXT_PUBLIC_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.NEXT_PUBLIC_AWS_SECRET_ACCESS_KEY,
  },
});

const environment =
  NEXT_PUBLIC_ENVIRONMENT === "development" ||
  NEXT_PUBLIC_ENVIRONMENT === "staging"
    ? ENVIRONMENT.SANDBOX
    : ENVIRONMENT.PUBLISHED;

const awsDownload = async (id) => {
  let response = {
    isError: false,
    message: "",
    data: null,
  };

  try {
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: id,
    });
    // Generate signed URL that expires in 5 minutes
    const url = await getSignedUrl(s3, command, { expiresIn: 60 * 5 });
    response.data = url;
    return response;
  } catch (error) {
    console.error(`Something went wrong in awsDownload due to `, error);
    response.isError = true;
    response.message = error.message;
    return response;
  }
};

const deleteAwsFile = async (key: string) => {
  let response = {
    isError: false,
    message: "",
    data: null,
  };

  try {
    const params = {
      Bucket: BUCKET_NAME,
      Key: key,
    };
    const file = await s3.deleteObject(params);
    console.log("file", file);
    response.data = file;
    return response;
  } catch (error) {
    console.error("Error deleting file:", error);
    response.isError = true;
    response.message = "Something went wrong while deleting file";
    return response;
  }
};

const getAwsFileInfo = async (id: string) => {
  try {
    const input = {
      Bucket: BUCKET_NAME,
      Key: `${environment}/${id}/${id}.mp4`,
    };
    const command = new HeadObjectCommand(input);
    const fileDetails = await s3.send(command);
    return fileDetails;
  } catch (error) {
    console.error("Error fetching file details:", error);
    return null;
  }
};

const getSignedUrlToUpload = async (key: string, name: string) => {
  let response = {
    isError: false,
    message: "",
    data: null,
  };

  try {
    const command = new PutObjectCommand({
      Bucket: process.env.NEXT_PUBLIC_AWS_BUCKET_NAME,
      Key: key,
      ContentType: "text/plain",
      Metadata: {
        name,
      },
    });
    const url = await getSignedUrl(s3, command, { expiresIn: 60 * 5 });
    response.data = url;
    return response;
  } catch (error) {
    console.error("Something went wrong in getSignedUrlPromise due to ", error);
    response.isError = true;
    response.message = "Something went wrong in getSignedUrlToUpload";
    return response;
  }
};

export {
  awsDownload,
  getAwsFileInfo,
  environment,
  deleteAwsFile,
  getSignedUrlToUpload,
};
