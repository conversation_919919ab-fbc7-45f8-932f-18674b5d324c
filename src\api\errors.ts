export type FieldError = {
  field: string;
  messages: [string];
};

class UserError extends Error {
  statusCode: number;
  fieldErrors: FieldError[];
  constructor(errors: FieldError[], statusCode: number) {
    super();
    this.statusCode = statusCode;
    this.fieldErrors = errors;
  }
}
export class PasswordError extends Error {
  statusCode: number;
  fieldErrors: FieldError[];
  constructor(errors: FieldError[], statusCode: number) {
    super();
    this.statusCode = statusCode;
    this.fieldErrors = errors;
  }
}
export class UserExistsError extends UserError {
  constructor() {
    super([{ field: "email", messages: ["User already exists."] }], 400);
  }
}

export class UserPasswordDoesNotMatchError extends UserError {
  constructor() {
    super(
      [{ field: "email", messages: ["Username or password is incorrect"] }],
      401
    );
  }
}

export class UserNotLoggedInError extends UserError {
  constructor() {
    super([{ field: "email", messages: ["User is not logged in"] }], 401);
  }
}

export class UserNotAuthorizedError extends UserError {
  constructor() {
    super([{ field: "email", messages: ["User not authorized"] }], 403);
  }
}

export class PasswordValidationError extends PasswordError {
  constructor() {
    super([] as FieldError[], 400);
  }
}
export class PasswordCreateError extends PasswordError {}
