import { categories } from "data/categories";
import { Category } from "../mongo";

export const seedCategories = async (isUpsert = true) => {
  try {
    for (let category of categories) {
      if (isUpsert) {
        await Category.findOneAndUpdate(
          { name: category.name },
          { name: category.name },
          { upsert: true }
        );
      } else {
        await Category.create({ name: category.name });
      }
    }
  } catch (error) {
    console.error(`Something went wrong while seeding categories due to`, error);
  }
};
