import { goals } from "data/goals";
import { Goal } from "@/api/mongo";

export const seedGoals = async (isUpsert = true) => {
  try {
    for (let goal of goals) {
      if (isUpsert) {
        await Goal.findOneAndUpdate(
          { name: goal.name },
          { name: goal.name, image: goal.image },
          { upsert: true }
        );
      } else {
        await Goal.create({
          name: goal.name,
          image: goal.image,
        });
      }
    }
  } catch (error) {
    console.error(`Something went wrong while seeding goals due to`, error);
  }
};

export const handleReplaceAllGoals = async () => {
  try {
    await Goal.deleteMany();
    await seedGoals();
  } catch (error) {
    console.error(
      "Something went wrong in handleReplaceAllGoals due to ",
      error
    );
    throw error;
  }
};
