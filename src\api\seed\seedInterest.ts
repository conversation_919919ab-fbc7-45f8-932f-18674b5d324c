import { interest } from "data/interests";
import { Interest, User } from "@/api/mongo";

export const seedInterest = async (isUpsert = true) => {
  try {
    for (let intr of interest) {
      if (isUpsert) {
        await Interest.findOneAndUpdate(
          { name: intr.name },
          { name: intr.name, image: intr.image },
          { upsert: true }
        );
      } else {
        await Interest.create({ name: intr.name, image: intr.image });
      }
    }
  } catch (error) {
    console.error(`Something went wrong while seeding interests due to`, error);
  }
};

export const handleReplaceAllInterests = async () => {
  try {
    await Interest.deleteMany({});
    await User.updateMany({}, { $set: { interest: [], completeLater: true } });
    await seedInterest(false);
  } catch (error) {
    console.error(
      `Something went wrong in handleReplaceAllInterests due to`,
      error
    );
  }
};
