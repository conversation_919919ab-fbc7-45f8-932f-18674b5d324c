import {
  SESClient,
  SendEmailCommand,
  SendEmailCommandInput,
} from "@aws-sdk/client-ses";
import axios from "axios";

const sesclt = new SESClient({
  region: "us-east-1",
  credentials: {
    accessKeyId: process.env.NEXT_PUBLIC_AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.NEXT_PUBLIC_AWS_SECRET_ACCESS_KEY!,
  },
});

const BREVO_API_KEY = process.env.BREVO_API_KEY!;
const SENDER_EMAIL =
  process.env.SENDER_EMAIL! ?? "<EMAIL>";

type SendEmailProps = {
  body: string;
  subject: string;
  to: string[];
  isHtml?: boolean;
};

export const sendEmail = async ({
  body,
  subject,
  to,
  isHtml = true,
}: SendEmailProps) => {
  try {
    const params: SendEmailCommandInput = {
      Destination: {
        CcAddresses: [],
        ToAddresses: to,
      },
      Message: {
        Body: isHtml
          ? {
              Html: {
                Charset: "UTF-8",
                Data: body,
              },
            }
          : {
              Text: {
                Charset: "UTF-8",
                Data: body,
              },
            },
        Subject: {
          Charset: "UTF-8",
          Data: subject,
        },
      },
      Source: SENDER_EMAIL,
      ReplyToAddresses: [SENDER_EMAIL],
    };

    const command = new SendEmailCommand(params);
    await sesclt.send(command);
    console.log("Email sent via SES successfully.");
  } catch (error) {
    console.error("SES failed, falling back to Brevo.", error);
    try {
      const brevoPayload = {
        sender: {
          name: SENDER_EMAIL,
          email: SENDER_EMAIL,
        },
        to: to.map((m) => {
          return {
            email: m,
            name: m.split("@")[0],
          };
        }),
        subject,
        htmlContent: body,
      };

      const response = await axios.post(
        "https://api.brevo.com/v3/smtp/email",
        brevoPayload,
        {
          headers: {
            "Content-Type": "application/json",
            "api-key": BREVO_API_KEY,
          },
        }
      );
      console.log("Email sent via Brevo successfully.", response.data);
    } catch (brevoError) {
      console.error("Brevo email sending failed.", brevoError);
    }
  }
};
