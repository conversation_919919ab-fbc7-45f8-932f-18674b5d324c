import { CURRENCY_ENUM, PAYMENT_MODE } from "@/constant/Enums";
import { User } from "./mongo";

const STRIPE_KEY = process.env.STRIPE_KEY;
const STRIPE_SUCCESS_URL = process.env.STRIPE_SUCCESS_URL;
const STRIPE_CANCEL_URL = process.env.STRIPE_CANCEL_URL;

const stripe = require("stripe")(STRIPE_KEY);

const makeStripePayment = async ({
  price,
  metadata,
  email,
  customerId,
  success_url = "",
  cancel_url = "",
  isPayment = true,
  lineItems = [],
  currency = "",
}) => {
  try {
    const paymentProps = (() => {
      if (isPayment) {
        return {
          invoice_creation: {
            enabled: true,
          },
        };
      } else {
        return {
          currency,
        };
      }
    })();
    const session = await stripe.checkout.sessions.create({
      line_items: lineItems,
      mode: isPayment ? PAYMENT_MODE.PAYMENT : PAYMENT_MODE.SUBSCRIPTION,
      success_url: success_url ?? STRIPE_SUCCESS_URL,
      cancel_url: cancel_url ?? STRIPE_CANCEL_URL,
      metadata,
      customer: customerId,
      ...paymentProps,
    });

    return session;
  } catch (error) {
    console.error("Something went wrong in makeStripePayment due to ", error);
    throw error;
  }
};

const handleUpdateUser = async ({
  stripeCustomerId = null,
  stripeCustomerIdMxn = null,
  userId,
}) => {
  try {
    const payload = {
      stripeCustomerId,
      stripeCustomerIdMxn,
    };
    if (!stripeCustomerId) {
      delete payload.stripeCustomerId;
    }
    if (!stripeCustomerIdMxn) {
      delete payload.stripeCustomerIdMxn;
    }
    const updatedUser = await User.findOneAndUpdate({ _id: userId }, payload, {
      new: true,
    });
    console.log("updated the user", updatedUser);
  } catch (error) {
    console.error("Something went wrong in handleUpdateUser due to ", error);
  }
};

const createStripeCustomer = async ({
  email,
  name,
  isCreatingAccount = false,
  isMexican = false,
  userId,
}) => {
  try {
    console.log("isCreatingAccount", isCreatingAccount);
    if (!isCreatingAccount) {
      const existingUser = await User.findOne({ _id: userId });
      console.log("existingUser", existingUser);
      if (!isMexican && existingUser?.stripeCustomerId) {
        return existingUser.stripeCustomerId;
      }
      if (isMexican && existingUser?.stripeCustomerIdMxn) {
        return existingUser.stripeCustomerIdMxn;
      }
    }
    const mexicanEmail = `MXN-${email}`;

    const existingCustomers = await stripe.customers.list({
      email: isMexican ? mexicanEmail : email,
      limit: 2,
    });

    const customerId = (() => {
      try {
        const customerArray = existingCustomers?.data;
        const mexicanFiltered = customerArray.filter(
          (f) => f.currency === CURRENCY_ENUM.MXN.toLowerCase()
        );
        const usdFiltered = customerArray.filter(
          (f) => f.currency === CURRENCY_ENUM.USD.toLowerCase()
        );
        const chosedArray = isMexican ? mexicanFiltered : usdFiltered;
        if (chosedArray && chosedArray[0] && chosedArray[0]?.id) {
          return chosedArray[0]?.id;
        }
        return null;
      } catch (error) {
        return null;
      }
    })();

    if (customerId) {
      await handleUpdateUser({
        stripeCustomerId: isMexican ? null : customerId,
        stripeCustomerIdMxn: isMexican ? customerId : null,
        userId,
      });
      return customerId;
    } else {
      const customerCreate = {
        email,
        name,
        metadata: { email },
      };
      if (isMexican) {
        customerCreate.email = mexicanEmail;
      }
      try {
        const customer = await stripe.customers.create(customerCreate);
        if (customer?.id) {
          await handleUpdateUser({
            stripeCustomerId: isMexican ? null : customer?.id,
            stripeCustomerIdMxn: isMexican ? customer?.id : null,
            userId,
          });
        }
        return customer?.id;
      } catch (error) {
        console.error("Something went wrong in createCustomer due to", error);
      }
    }
  } catch (error) {
    console.error("Something went wrong in createCustomer due to", error);
    throw error;
  }
};

const getStripePaymentDetails = async (id: string) => {
  try {
    const payment = await stripe.checkout.sessions.retrieve(id);
    return payment;
  } catch (error) {
    console.error(
      "Something went wrong in getStripePaymentDetails due to",
      error
    );
    return error;
  }
};

const getStripeCustomerPortalLink = async (customerId: string) => {
  try {
    const portal = await stripe.billingPortal.sessions.create({
      customer: customerId,
    });
    console.log("portal details", portal);
    return {
      url: portal?.url,
      noCustomer: false,
    };
  } catch (error) {
    const noCustomer =
      error?.raw?.message?.includes("No such customer:") &&
      error?.raw?.statusCode === 400;
    console.error(
      "Something went wrong in getStripeCustomerPortalLink due to",
      error
    );
    return {
      url: "",
      noCustomer,
    };
  }
};

export {
  makeStripePayment,
  getStripePaymentDetails,
  createStripeCustomer,
  getStripeCustomerPortalLink,
  stripe,
};
