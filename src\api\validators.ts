import zod from "zod";
import { isPossiblePhoneNumber } from "libphonenumber-js";
import { Country } from "./mongo";

// export const passwordValidator = zod
//   .string()
//   .min(8, { message: "Password must be at least 8 characters long" })
//   .max(100, { message: "Password must be less than 100 characters long" })
//   .refine(
//     (password) => /(?=.*[a-z].*)/.test(password),
//     "Password must have at least one lowercase letter"
//   )
//   .refine(
//     (password) => /(?=.*[A-Z].*)/.test(password),
//     "Password must have at least one uppercase letter"
//   )
//   .refine(
//     (password) => /(?=.*[0-9].*)/.test(password),
//     "Password must have at least one number"
//   )
//   .refine(
//     (password) => /(?=.*[!@#$%^&*()\-_=+{};:,<.>].*)/.test(password),
//     "Password must have at least one special character !@#$%^&*()-_=+{};:,<.>"
//   );

export const nameValidator = zod
  .string()
  .min(1, { message: "Name is required" });

export const emailValidator = zod.string().email("Invalid email address");

export const phoneValidator = zod
  .string()
  .refine((v) => !v || isPossiblePhoneNumber(v, "US"), "Invalid phone number")
  .optional();

// export const userCreateValidator = zod
//   .object({
//     email: emailValidator,
//     // password: passwordValidator,
//     // confirmPassword: passwordValidator,
//     phone: phoneValidator,
//     lastName: nameValidator,
//     firstName: nameValidator,
//     primaryLanguage: zod.string().min(1, { message: "Language is required" }),
//   })
  // .refine((data) => data.password === data.confirmPassword, {
  //   message: "Passwords do not match",
  //   path: ["confirmPassword"],
  // });

export const userSignInValidator = zod.object({
  email: emailValidator,
  // password: passwordValidator,
});

export const waitlistValidator = zod.object({
  email: emailValidator,
  lname: nameValidator,
  fname: nameValidator,
  language: zod.string(),
});

export const launchWaitlistValidator = zod.object({
  email: emailValidator
});