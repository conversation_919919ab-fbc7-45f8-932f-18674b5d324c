import useOnClickOutside from "@/hooks/useOnClickOutside";
import { TeamDataType } from "@/types";
import { Box, Modal } from "@mui/material";
import React, { useMemo, useRef } from "react";
import Detail from "./Detail";

type TeamExtendedUserProps = React.FC<{
  numberOfCoulmns: number;
  showExpanded: boolean;
  setShowExpanded: React.Dispatch<React.SetStateAction<boolean>>;
  data: TeamDataType;
  heightOfTheCard: number;
  width: number;
  index: number;
}>;

const TeamExtendedUsers: TeamExtendedUserProps = ({
  numberOfCoulmns,
  setShowExpanded,
  data,
  heightOfTheCard,
  showExpanded,
  width,
  index,
}) => {
  if (width < 768) {
    return (
      <ModalUser
        setShowExpanded={setShowExpanded}
        showExpanded={showExpanded}
        data={data}
      />
    );
  }

  return (
    <CardUser
      setShowExpanded={setShowExpanded}
      data={data}
      numberOfCoulmns={numberOfCoulmns}
      heightOfTheCard={heightOfTheCard}
      index={index}
    />
  );
};

export default TeamExtendedUsers;

const SPACING = 40;

const ModalUser = ({ setShowExpanded, showExpanded, data }) => {
  return (
    <Modal
      open={showExpanded}
      onClose={() => {
        setShowExpanded(false);
      }}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        p: 4,
      }}
    >
      <Box
        sx={{
          zIndex: 100,
          display: "flex",
          flexDirection: "column",
          border: "3px solid rgba(20, 167, 156, 1)",
          borderRadius: 5,
          p: 2,
          background: "#fff",
          gap: 2,
          maxHeight: "95vh",
        }}
      >
        <Detail isModal setShowExpanded={setShowExpanded} data={data} />
      </Box>
    </Modal>
  );
};

const CardUser = ({
  setShowExpanded,
  data,
  numberOfCoulmns,
  heightOfTheCard,
  index,
}) => {
  const ref = useRef();
  useOnClickOutside(ref, () => {
    setShowExpanded(false);
  });

  const top = useMemo(() => {
    const rowIndex = Math.floor(index / numberOfCoulmns) + 1;
    const rowgap = SPACING * rowIndex;
    const handledHeight = heightOfTheCard * (rowIndex - 1);
    const value = handledHeight + rowgap;
    return `${value}px`;
  }, [heightOfTheCard, numberOfCoulmns, index, SPACING]);

  return (
    <Box
      ref={ref}
      sx={{
        marginLeft: `${SPACING}px`,
        zIndex: 100,
        display: "flex",
        flexDirection: "row",
        position: "absolute",
        border: "3px solid rgba(20, 167, 156, 1)",
        borderRadius: 5,
        p: 2,
        background: "#fff",
        gap: 2,
        top,
        height: heightOfTheCard,
      }}
    >
      <Detail setShowExpanded={setShowExpanded} data={data} />
    </Box>
  );
};
