import { Box, Grid, Typography } from "@mui/material";
import Image from "next/image";
import React, { useMemo, useState } from "react";
import { useSpring, animated } from "@react-spring/web";
import TeamExtendedUsers from "./TeamExtendedUser";
import { TeamDataType } from "@/types";

type TeamUserProps = React.FC<{
  data: TeamDataType;
  numberOfCoulmns: number;
  index: number;
  width: number;
  heightOfTheCard: number;
}>;

const linearGradient =
  "linear-gradient(180deg, rgba(20, 167, 156, 0) 36.06%, rgba(20, 167, 156, 0.690361) 58.1%, #14A79C 98.51%)";

const TeamUser: TeamUserProps = ({
  data,
  numberOfCoulmns,
  index,
  heightOfTheCard,
  width,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showExpanded, setShowExpanded] = useState(false);

  const image = useMemo(() => {
    return isHovered ? data.img2 : data.img1;
  }, [isHovered, data]);

  const overlayProps = useSpring({
    from: { opacity: 0, transform: "translateY(50px)" },
    to: {
      opacity: isHovered ? 1 : 0,
      transform: isHovered ? "translateY(0)" : "translateY(50px)",
    },
    config: { tension: 280, friction: 60 },
  });

  const imageProps = useSpring({
    from: { opacity: 0.8, scale: 1 },
    to: {
      opacity: isHovered ? 1 : 0.8,
      scale: isHovered ? 1.05 : 1,
    },
    config: { tension: 200, friction: 20 },
  });

  return (
    <>
      {showExpanded && (
        <TeamExtendedUsers
          numberOfCoulmns={numberOfCoulmns}
          setShowExpanded={setShowExpanded}
          data={data}
          heightOfTheCard={heightOfTheCard}
          index={index}
          width={width}
          showExpanded={showExpanded}
        />
      )}
      <Grid item xs={12} sm={6} md={3} lg={3}>
        <Box
          onClick={() => {
            setShowExpanded(true);
          }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          sx={{
            width: "100%",
            border: "5px solid #14A79C",
            borderRadius: 4,
            position: "relative",
            height: {
              xs: 400,
              sm: 300,
            },
            overflow: "hidden",
            cursor: "pointer",
          }}
        >
          {isHovered && (
            <animated.div
              style={{
                ...overlayProps,
                position: "absolute",
                height: "100%",
                width: "100%",
                background: linearGradient,
                zIndex: 1,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "end",
              }}
            >
              <Typography color="#fff" fontWeight="800" mb={0}>
                {data.name}
              </Typography>
              <Typography color="#fff">{data.designation}</Typography>
            </animated.div>
          )}
          <animated.div
            style={{
              ...imageProps,
              transform: imageProps.scale.to((s) => `scale(${s})`),
              height: "100%",
              width: "100%",
              position: "absolute",
            }}
          >
            <Image
              src={image}
              objectFit="cover"
              style={{ zIndex: 0 }}
              fill
              alt={data.name}
            />
          </animated.div>
        </Box>
      </Grid>
    </>
  );
};

export default TeamUser;
