import React, { useState } from "react";
import StackedCard from "../StackedCard";
import useWindowDimensions from "@/hooks/useWindowDimension";
import { Box, Modal } from "@mui/material";
import Skeleton from "@mui/material/Skeleton";

const WhyVideo = () => {
  const { width } = useWindowDimensions();
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <>
      {!isExpanded && (
        <StackedCard
          isRight
          sx={{
            width: {
              xs: "100%",
              md: 500,
            },
            position: "relative",
          }}
        >
          {width < 600 && (
            <ExpandButton
              onClick={() => {
                setIsExpanded(true);
              }}
            />
          )}
          <VideoCard />
        </StackedCard>
      )}
      <Modal
        open={isExpanded}
        onClose={() => {
          setIsExpanded(false);
        }}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          p: 4,
        }}
      >
        <Box
          sx={{
            position: "relative",
            border: "none",
            outline: "none",
            borderRadius: 5,
            overflow: "hidden",
          }}
        >
          <CloseButton
            onClick={() => {
              setIsExpanded(false);
            }}
          />
          <VideoCard />
        </Box>
      </Modal>
    </>
  );
};

const CloseButton = (props) => {
  return (
    <Box
      {...props}
      sx={{
        height: 30,
        width: 30,
        borderRadius: 15,
        background: "rgba(0, 0, 0, 0.58)",
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        position: "absolute",
        top: 10,
        right: 10,
        cursor: "pointer",
        zIndex: 100,
      }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 13.68L15.48 17.16C15.7 17.38 15.98 17.49 16.32 17.49C16.66 17.49 16.94 17.38 17.16 17.16C17.38 16.94 17.49 16.66 17.49 16.32C17.49 15.98 17.38 15.7 17.16 15.48L13.68 12L17.16 8.52C17.38 8.3 17.49 8.02 17.49 7.68C17.49 7.34 17.38 7.06 17.16 6.84C16.94 6.62 16.66 6.51 16.32 6.51C15.98 6.51 15.7 6.62 15.48 6.84L12 10.32L8.52 6.84C8.3 6.62 8.02 6.51 7.68 6.51C7.34 6.51 7.06 6.62 6.84 6.84C6.62 7.06 6.51 7.34 6.51 7.68C6.51 8.02 6.62 8.3 6.84 8.52L10.32 12L6.84 15.48C6.62 15.7 6.51 15.98 6.51 16.32C6.51 16.66 6.62 16.94 6.84 17.16C7.06 17.38 7.34 17.49 7.68 17.49C8.02 17.49 8.3 17.38 8.52 17.16L12 13.68ZM12 24C10.34 24 8.78 23.6848 7.32 23.0544C5.86 22.424 4.59 21.5692 3.51 20.49C2.43 19.4108 1.5752 18.1408 0.945601 16.68C0.316001 15.2192 0.000801518 13.6592 1.51899e-06 12C-0.00079848 10.3408 0.314401 8.7808 0.945601 7.32C1.5768 5.8592 2.4316 4.5892 3.51 3.51C4.5884 2.4308 5.8584 1.576 7.32 0.9456C8.7816 0.3152 10.3416 0 12 0C13.6584 0 15.2184 0.3152 16.68 0.9456C18.1416 1.576 19.4116 2.4308 20.49 3.51C21.5684 4.5892 22.4236 5.8592 23.0556 7.32C23.6876 8.7808 24.0024 10.3408 24 12C23.9976 13.6592 23.6824 15.2192 23.0544 16.68C22.4264 18.1408 21.5716 19.4108 20.49 20.49C19.4084 21.5692 18.1384 22.4244 16.68 23.0556C15.2216 23.6868 13.6616 24.0016 12 24ZM12 21.6C14.68 21.6 16.95 20.67 18.81 18.81C20.67 16.95 21.6 14.68 21.6 12C21.6 9.32 20.67 7.05 18.81 5.19C16.95 3.33 14.68 2.4 12 2.4C9.31999 2.4 7.05 3.33 5.19 5.19C3.33 7.05 2.4 9.32 2.4 12C2.4 14.68 3.33 16.95 5.19 18.81C7.05 20.67 9.31999 21.6 12 21.6Z"
          fill="white"
        />
      </svg>
    </Box>
  );
};

const ExpandButton = (props) => {
  return (
    <Box
      {...props}
      sx={{
        borderRadius: 1,
        background: "rgba(0, 0, 0, 0.6)",
        p: 1,
        position: "absolute",
        height: 30,
        width: 30,
        top: 5,
        right: 5,
        zIndex: 100,
        cursor: "pointer",
      }}
    >
      <svg
        width="18"
        height="19"
        viewBox="0 0 18 19"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5.83594 6.5L0.835938 1.5M0.835938 1.5V5.5M0.835938 1.5H4.83594M11.8359 6.5L16.8359 1.5M16.8359 1.5V5.5M16.8359 1.5H12.8359M5.83594 12.5L0.835938 17.5M0.835938 17.5V13.5M0.835938 17.5H4.83594M11.8359 12.5L16.8359 17.5M16.8359 17.5V13.5M16.8359 17.5H12.8359"
          stroke="white"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </Box>
  );
};

const VideoCard = () => {
  return (
    <video
      src="/images/about/whyPatito/video.mp4"
      height="100%"
      width="100%"
      autoPlay
      loop
      controls
      playsInline
      disablePictureInPicture
      controlsList="nofullscreen nodownload"
      // style={{ objectFit: "cover" }}
    >
      <source src="/images/about/whyPatito/video.mp4" type="video/mp4" />
    </video>
  );
};

export const WhyVideoLoading = () => {
  return (
    <StackedCard
      isRight
      sx={{
        height: "350px",
        width: {
          xs: "100%",
          md: 500,
        },
        position: "relative",
      }}
    >
      <Skeleton variant="rectangular" width="100%" height="100%" />
    </StackedCard>
  );
};

export default WhyVideo;
