import React from "react";
import { SwipeableDrawer, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

export const BaseDrawer = ({ isOpen, onClose, children, width = "33%" }) => {
  return (
    <SwipeableDrawer
      open={isOpen}
      onClose={onClose}
      onOpen={onClose}
      sx={{ width: width }}
      ModalProps={{
        keepMounted: false,
      }}
    >
      <IconButton
        sx={{
          position: "absolute",
          top: "10px",
          right: "10px",
          zIndex: 2,
        }}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>
      {children}
    </SwipeableDrawer>
  );
};
