import React, { useState } from "react";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import CardMedia from "@mui/material/CardMedia";
import Chip from "@mui/material/Chip";
import IconButton from "@mui/material/IconButton";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import { useSpring, animated } from "react-spring";
import Link from "next/link";
import Image from "next/image";

const BlogPostCard = ({ post }) => {
  return (
    <Card
      sx={{
        flexDirection: { xs: "column", sm: "row" },
        marginTop: 2,
        marginBottom: 2,
        boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
        transition: "all 0.3s",
        "&:hover": {
          transform: "scale(1.02)",
          boxShadow: "0 6px 8px rgba(0,0,0,0.1)",
        },
      }}
    >
      {post?.image && (
        <>
          <CardMedia
            component="img"
            height="140"
            image={post.image}
            alt={post.title}
          />
          <CardContent sx={{ flexGrow: 1 }}>
            <Typography variant="h5" gutterBottom>
              {post.title}
            </Typography>
            <Typography variant="subtitle1" color="textSecondary">
              {post.author} - {post.date}
            </Typography>
            <Typography variant="body1" sx={{ my: 2 }}>
              {post.description}
            </Typography>
          </CardContent>
        </>
      )}
    </Card>
  );
};

export default BlogPostCard;
