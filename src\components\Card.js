import React, { useState, useRef } from "react";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import Box from "@mui/material/Box";
import { useSpring, animated, useInView } from "react-spring";
import Link from "next/link";
import { Button, CardActions } from "@mui/material";
import Image from "next/image";

export default function ResponsiveCard({
  color,
  content,
  image,
  objectPosition,
  index,
}) {
  const { eng, esp } = content;
  const [ref, springs] = useInView(
    () => ({
      from: { opacity: 0, transform: "translateY(50px)" },
      to: { opacity: 1, transform: "translateY(0px)" },
      config: { tension: 80, friction: 40 },
      delay: index * 2000,
      once: true,
    }),
    { rootMargin: "-10% 0px" }
  );

  //   const [isHovered, setIsHovered] = useState(false);
  //   const [hoverStyle, setHoverStyle] = useState({});

  //   const handleMouseEnter = () => {
  //     setIsHovered(true);
  //     setHoverStyle({ boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.5)" });
  //   };

  //   const handleMouseLeave = () => {
  //     setIsHovered(false);
  //     setHoverStyle({});
  //   };

  //   const handleTouchStart = () => {
  //     setIsHovered(true);
  //     setHoverStyle({ boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.5)" });
  //   };

  //   const handleTouchEnd = () => {
  //     setIsHovered(false);
  //     setHoverStyle({});
  //   };

  return (
    <Grid item xs={12} sm={6} md={4} sx={{}}>
      <animated.div style={springs} ref={ref}>
        <Card
          style={{ borderRadius: "15px" }}
          sx={{
            width: "100%",
            padding: 0,
            backgroundColor: color,
            boxShadow: 3,
            // ...hoverStyle,
            position: "relative",
          }}
          className="rounded-[0]"
        >
          <Box
            sx={{
              width: "100%",
              height: { xs: "273px", md: "300px" },
              overflow: "hidden",
              position: "relative",
            }}
          >
            <Image
              src={image}
              alt="card-image"
              fill
              style={{
                borderRadius: "5px",
                objectFit: "cover",
                objectPosition,
              }}
            />
          </Box>

          <CardContent
            sx={{
              margin: {
                xs: "15px auto 15px auto",
                sm: "15px auto 15px auto",
                md: "15px auto 15px auto",
              },
              textAlign: "center",
              color: "black",
            }}
          >
            <Box
              display="flex"
              flexDirection="column"
              justifyContent="flex-start"
              alignItems="center"
              height={{ xs: "200px", sm: "150px", md: "130px", lg: "180px" }}
            >
              <Typography
                variant="h2"
                component="div"
                textAlign="center"
                sx={{
                  fontSize: { xs: "32px", sm: "20px", md: "20px", lg: "32px" },

                  fontWeight: { xs: "500", sm: "500" },
                  userSelect: "none",
                  paddingBottom: { xs: "20px", md: "0px" },
                  paddingTop: { md: "10px" },
                  width: { xs: "220px", md: "280px" },
                  height: { md: "158px" },
                }}
              >
                {eng.title}
              </Typography>
              <Typography
                variant="h5"
                component="div"
                sx={{
                  textAlign: "center",
                  userSelect: "none",
                  textAlign: "center",
                  width: { xs: "300px", sm: "250px", md: "200px", lg: "270px" },
                  height: { xs: "180px", md: "300px", lg: "362px" },
                  fontWeight: "400",
                  fontSize: {
                    xs: "20px",
                    md: "12px",
                    lg: "20px",
                  },
                }}
              >
                {eng.text}
              </Typography>
            </Box>
            <CardActions
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                padding: "0px 20px",
                marginTop: { xs: "20px", md: "0px" },
              }}
            >
              <Link href="/classes" passHref className="no-underline">
                <Button
                  aria-label={`Learn more about ${eng.title}`}
                  sx={{
                    backgroundColor: "#02B199",
                  }}
                >
                  Learn More
                </Button>
              </Link>
            </CardActions>
          </CardContent>
        </Card>
      </animated.div>
    </Grid>
  );
}
