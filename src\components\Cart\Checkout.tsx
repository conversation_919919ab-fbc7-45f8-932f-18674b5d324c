import React from "react";
import { Box, Typography, Button } from "@mui/material";
import { InfoOutlined } from "@mui/icons-material";

import InputCoupon from "./InputCoupon";

const checkoutContainer = {
  // padding: "45px 20px",
  padding: "2rem",
  borderRadius: "22px",
  border: "1px solid #D9D9D9",
};

const checkoutDividerStyles = {
  border: "1px solid #F0F0F0",
  marginBottom: "15px",
};
const feeAmountStyles = {
  fontWeight: "500",
  fontSize: "14px",
};
const feeLabelStyles = {
  fontWeight: "500",
  fontSize: "14px",
  color: "#6D6D6D",
};
const FeesContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
  marginBottom: "15px",
};
const totalContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: "15px",
};
const grandTotalLabelStyles = { fontWeight: "500", fontSize: "15px" };
const grandTotalStyles = { fontWeight: "700", fontSize: "24px" };
const buttonStyles = {
  width: "100%",
  backgroundColor: "#14A79C",
  color: "white",
  textTransform: "capitalize",
  fontSize: "16px",
  fontWeight: "500",
};
const iconStyles = { fontSize: "20px", marginRight: "8px" };

const Checkout = ({
  total,
  handlePayment,
  isMaking,
  discount,
  finalAmount,
  currenyNotSame = false,
  currencySymbol = "$",
}) => {
  return (
    <Box sx={checkoutContainer} aria-disabled={currenyNotSame}>
      {/* <InputCoupon /> */}
      <Box sx={FeesContainerStyles}>
        <Typography sx={feeLabelStyles}>Amount</Typography>
        {!currenyNotSame && (
          <Typography sx={feeAmountStyles}>
            {currencySymbol}
            {total}
          </Typography>
        )}
      </Box>
      <Box sx={FeesContainerStyles}>
        <Typography sx={feeLabelStyles}>Discount</Typography>
        <Typography sx={feeAmountStyles}>
          {currencySymbol}
          {discount}
        </Typography>
      </Box>
      <hr style={checkoutDividerStyles} />
      <Box sx={totalContainerStyles}>
        <Typography sx={grandTotalLabelStyles}>Total Amount</Typography>
        {!currenyNotSame && (
          <Typography sx={grandTotalStyles}>
            {currencySymbol}
            {finalAmount}
          </Typography>
        )}
      </Box>
      <Button
        sx={buttonStyles}
        disabled={isMaking}
        onClick={() => {
          handlePayment();
        }}
      >
        Checkout
      </Button>
      {currenyNotSame && (
        <Typography color="red" textAlign="center" fontSize={14} mt={2}>
          <InfoOutlined style={{ fontSize: 14 }} /> Multiple currencies
          detected.Keep only one to continue
        </Typography>
      )}
    </Box>
  );
};

export default Checkout;
