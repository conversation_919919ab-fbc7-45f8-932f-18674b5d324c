import React from "react";

const EditDeleteStyle = {
  color: "#000",
  background: "#fff",
  border: "none",
  cursor: "pointer",
};

const EditButton = (props) => {
  return (
    <button {...props} style={EditDeleteStyle}>
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M11.5 2.9997L14.5 5.9997M9.5 16.9997H17.5M1.5 12.9997L0.5 16.9997L4.5 15.9997L16.086 4.4137C16.4609 4.03864 16.6716 3.53003 16.6716 2.9997C16.6716 2.46937 16.4609 1.96075 16.086 1.5857L15.914 1.4137C15.5389 1.03876 15.0303 0.828125 14.5 0.828125C13.9697 0.828125 13.4611 1.03876 13.086 1.4137L1.5 12.9997Z"
          stroke="black"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>
  );
};

export default EditButton;
