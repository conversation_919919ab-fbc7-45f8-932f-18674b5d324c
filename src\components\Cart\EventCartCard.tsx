import { CartType, EventOnType, EventSchemaType } from "@/api/mongoTypes";
import { Box, Card, Typography } from "@mui/material";
import React from "react";
import DateTime from "../Create/event/DateTime";
import Image from "next/image";
import Tag from "../Tag";
import { EVENT_MODE_TYPE } from "@/constant/Enums";
import useCart from "@/hooks/useCart";
import { getLocalCarts, getPriceSymbol } from "@/utils/classes";
import { useCartCountContext } from "@/contexts/CartCountContext";
import { useSnackbar } from "@/hooks/useSnackbar";

const EditDeleteStyle = {
  color: "#000",
  background: "#fff",
  border: "none",
  cursor: "pointer",
};

const cardContainerStyles = {
  padding: "20px 30px",
  border: "1px solid rgba(217, 217, 217, 1)",
  boxShadow: "0",
  marginBottom: "25px",
  borderRadius: "10px",
};

const totalContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "end",
  mt: 3,
};
const totalLabelStyles = {
  color: "#AEAEAE",
  fontWeight: "500",
  fontSize: "24px",
  marginRight: "15px",
  textTransform: "capitalize",
};
const totalPriceStyles = {
  fontWeight: "500",
  fontSize: "24px",
  textTransform: "capitalize",
};

type EventCartCardProps = React.FC<{
  data: CartType;
  handleDeleteSuccess: () => void;
  isLoggedIn: boolean;
}>;
const EventCartCard: EventCartCardProps = ({
  data,
  handleDeleteSuccess,
  isLoggedIn,
}) => {
  const { handleDelete, isDeletingCart: isDeleting } = useCart({
    onDeleteSuccess: () => {
      handleDeleteSuccess();
    },
    onAddingSuccess: () => {},
  });

  const eventData = (() => {
    if (typeof data.eventId !== "object" || !("_id" in data.eventId)) {
      return null;
    }
    return data.eventId as EventSchemaType;
  })();

  if (!eventData) {
    return null;
  }

  const isOnline = eventData.mode === EVENT_MODE_TYPE.ONLINE;
  const eventOnData = data.eventOn as EventOnType;

  console.log({
    data,
    eventData,
  });

  return (
    <Card sx={cardContainerStyles}>
      <Box width="100%" display="flex" flexDirection="row" gap={4}>
        <Box width="35%">
          <Box sx={{ position: "relative", height: 100, width: "100%" }}>
            <Image src={eventData.images[0].url} fill alt={eventData.title} />
          </Box>
        </Box>
        <Box width="60%">
          <Typography fontWeight="700" fontSize={16}>
            {eventData.title}
          </Typography>
          <Box mb={10}>
            <ModeFlag isOnline={isOnline} />
          </Box>
          <Box display="flex" flexDirection="row" alignItems="center">
            {eventData?.categories?.map((m) => (
              <Tag text={m} key={m} />
            ))}
          </Box>
        </Box>
        <Box
          width="15%"
          display="flex"
          flexDirection="row"
          justifyContent="end"
          alignItems="baseline"
        >
          <button
            style={EditDeleteStyle}
            disabled={isDeleting}
            aria-disabled={isDeleting}
            onClick={() => {
              handleDelete({ id: data._id });
            }}
          >
            <svg
              width="20"
              height="22"
              viewBox="0 0 20 22"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2.00033 21.6663V2.99965H0.666992V1.66631H6.00033V0.639648H14.0003V1.66631H19.3337V2.99965H18.0003V21.6663H2.00033ZM3.33366 20.333H16.667V2.99965H3.33366V20.333ZM7.07766 17.6663H8.41099V5.66631H7.07766V17.6663ZM11.5897 17.6663H12.923V5.66631H11.5897V17.6663Z"
                fill="black"
              />
            </svg>
          </button>
        </Box>
      </Box>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        mt={5}
      >
        <Box>
          <DateTime
            dateTime={eventOnData?.startDateTime}
            timezone={String(eventOnData?.timezone)}
            isStart
          />
        </Box>
        <Box>
          <DateTime
            isStart={false}
            timezone={String(eventOnData?.timezone)}
            dateTime={eventOnData?.endDateTime}
          />
        </Box>
        <Box sx={totalContainerStyles}>
          <Typography sx={totalLabelStyles}>Total: </Typography>
          <Typography sx={totalPriceStyles}>
            {getPriceSymbol({
              currency: eventData.currency,
            })}
            {eventData.price}
          </Typography>
        </Box>
      </Box>
    </Card>
  );
};

type ModeFlagProps = React.FC<{
  isOnline: boolean;
}>;
const ModeFlag: ModeFlagProps = ({ isOnline }) => {
  return (
    <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
      <span
        style={{
          height: 6,
          width: 6,
          borderRadius: 3,
          background: "#F9B238",
        }}
      />
      <Typography sx={{ fontSize: 12, color: "#6D6D6D" }}>
        {isOnline ? "Online Event" : "Offline Event"}
      </Typography>
    </Box>
  );
};

export default EventCartCard;
