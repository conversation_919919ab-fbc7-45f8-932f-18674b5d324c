import * as React from "react";
import { Box, <PERSON><PERSON><PERSON>, But<PERSON> } from "@mui/material";

const formInputContainerStyles = {
  display: "flex",
  alignItems: "center",
  marginBottom: 2,
  border: "1px solid #F0F0F0",
  marginBottom: "15px",
};

const buttonStyles = {
  height: "40px",
  textTransform: "capitalize",
  backgroundColor: "#F5FFFE",
  borderTopRightRadius: "5px", // Only adjust the top-right corner
  borderTopLeftRadius: "0px", // Keep other corners square
  borderBottomRightRadius: "5px",
  borderBottomLeftRadius: "0px",
  color: "#14A79C",
  "&:hover": {
    backgroundColor: "#14A79C",
    color: "#FFFFFF",
  },
};

const textFieldStyles = {
  width: "100%",
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "#ffffff", // Default border color
      borderRadius: "5px 0px 0px 5px", // Remove border radius
    },
    "&:hover fieldset": {
      borderColor: "#14A79C", // Border color on hover
    },
    "&.Mui-focused fieldset": {
      borderColor: "#14A79C", // Border color when focused
      borderWidth: 2, // Optional: Thicker border when focused
    },
  },
};
export default function InputCoupon() {
  const [coupon, setcoupon] = React.useState("");

  const handleSubmit = (event) => {
    event.preventDefault();
    try {
      setTimeout(() => {
        console.log(`checking coupon: ${coupon}`);
      }, 500);
    } catch (error) {
      console.log(`coupon failed: ${coupon}`);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <Box sx={formInputContainerStyles}>
        <TextField
          size="small"
          sx={textFieldStyles}
          placeholder="Coupon Code"
          type="text"
          value={coupon}
          onChange={(event) => setcoupon(event.target.value)}
        ></TextField>
        <Button variant="outlined" type="submit" size="small" sx={buttonStyles}>
          Apply
        </Button>
      </Box>
    </form>
  );
}
