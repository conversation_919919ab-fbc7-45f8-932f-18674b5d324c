import React, { useState } from "react";
import { Card, Typography, Box } from "@mui/material";
import {
  getPlansTitle,
  getPricingTitle,
  getSubTypeName,
  getTypeName,
} from "@/utils/format";
import { CartType } from "@/api/mongoTypes";
import { formatDate } from "@/utils/dateTime";
import { getPlanFor } from "@/utils/classes";
import useCart from "@/hooks/useCart";
import PricingType from "../classes/PricingType";
import CheckoutModal from "../CheckoutModal/CheckoutClass/CheckoutModal";

const cardContainerStyles = {
  padding: "20px 30px",
  border: "1px solid rgba(217, 217, 217, 1)",
  boxShadow: "0",
  marginBottom: "25px",
  borderRadius: "10px",
};
const cardTitleStyles = {
  textTransform: "capitalize",
  marginBottom: 1,
  fontWeight: "500",
  fontSize: "24px",
};
const cardSubtitleStyles = {
  textTransform: "capitalize",
  marginBottom: "22px",
  fontWeight: "400",
  fontSize: "16px",
  color: "#64748B",
};
const formControlLabelStyles = {
  flexGrow: 1,
  boxShadow: "0",
  textTransform: "capitalize",
};
const iconStyles = {
  color: "#14A79C",
  fontSize: "15px",
  transform: "scaleX(-1)",
  marginRight: "8px",
};
const radioBoxContainerStyles = {
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  alignItems: "left",
  backgroundColor: "#F9F9F9",
  borderRadius: "3px",
  padding: "6px 9px 6px 9px",
  borderBottom: "1px solid #f0f0f0",
};
const radioGroupStyles = { marginBottom: "25px" };
const packagePriceStyles = {
  color: "#14A79C",
  fontWeight: "500",
  fontSize: "16px",
  marginLeft: "0px",
};
const iconPriceContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
};
const rateAndSavingsContainerStyles = {
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  paddingLeft: "28px",
};
const rateStyles = {
  marginLeft: 1,
  marginBottom: "15px",
  textTransform: "capitalize",
};
const savingsStyles = {
  borderRadius: "5px",
  backgroundColor: "#F9B238",
  color: "white",
  fontSize: "10px",
  fontWeight: "500",
  maxWidth: "130px",
  marginBottom: "10px",
  textTransform: "capitalize",
};

const RemoveButtonStyles = {
  fontSize: "16px",
  fontWeight: "400",
  color: "#F9B238",
};
const titleContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
};

const quantityAndTotalContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
};
const totalContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "end",
  mt: 3,
};
const totalLabelStyles = {
  color: "#AEAEAE",
  fontWeight: "500",
  fontSize: "24px",
  marginRight: "15px",
  textTransform: "capitalize",
};
const totalPriceStyles = {
  fontWeight: "500",
  fontSize: "24px",
  textTransform: "capitalize",
};

const EditDeleteStyle = {
  color: "#000",
  background: "#fff",
  border: "none",
  cursor: "pointer",
};

type PlanSelectorProps = React.FC<{
  data: CartType;
  handleDeleteSuccess: () => void;
  handleUpdateSuccess: (data: CartType) => void;
  isLoggedIn: boolean;
}>;

const PlanSelector: PlanSelectorProps = ({
  data,
  handleDeleteSuccess,
  handleUpdateSuccess,
  isLoggedIn,
}) => {
  const { handleDelete, isDeletingCart: isDeleting } = useCart({
    onDeleteSuccess: () => {
      handleDeleteSuccess();
    },
    onAddingSuccess: () => {},
  });

  const [isModalOpen, setIsModalOpen] = useState(false);
  const classData = (() => {
    if (typeof data.classesId !== "object" || !("plans" in data.classesId)) {
      return null;
    }
    return data.classesId;
  })();
  const title = getPricingTitle({
    data: classData,
  });

  const classesPlans = classData.plans;
  const cartPlans = data.plans;

  const totalCount = (() => {
    let count = 0;
    cartPlans.map((m) => {
      const plan = classesPlans.find(
        (f) => String(f.planId) === String(m.planId)
      );
      count = +count + plan.price;
      // getDicountedPrice({
      //   discount: plan.discount ?? 0,
      //   price: plan.price ?? 0,
      // });
    });
    return count;
  })();

  return (
    <>
      <Card sx={cardContainerStyles}>
        <Box
          sx={{
            marginBottom: "1rem",
            pb: 4,
            borderBottom: "1px solid #b3b3b373",
          }}
        >
          <Box sx={titleContainerStyles}>
            <Typography sx={cardTitleStyles}>{title}</Typography>
            <Box display="flex" flexDirection="row" gap={3}>
              <button
                style={EditDeleteStyle}
                onClick={() => {
                  setIsModalOpen(true);
                }}
              >
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 18 18"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M11.5 2.9997L14.5 5.9997M9.5 16.9997H17.5M1.5 12.9997L0.5 16.9997L4.5 15.9997L16.086 4.4137C16.4609 4.03864 16.6716 3.53003 16.6716 2.9997C16.6716 2.46937 16.4609 1.96075 16.086 1.5857L15.914 1.4137C15.5389 1.03876 15.0303 0.828125 14.5 0.828125C13.9697 0.828125 13.4611 1.03876 13.086 1.4137L1.5 12.9997Z"
                    stroke="black"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
              <button
                style={EditDeleteStyle}
                disabled={isDeleting}
                aria-disabled={isDeleting}
                onClick={() => {
                  handleDelete({ id: data._id });
                }}
              >
                <svg
                  width="20"
                  height="22"
                  viewBox="0 0 20 22"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M2.00033 21.6663V2.99965H0.666992V1.66631H6.00033V0.639648H14.0003V1.66631H19.3337V2.99965H18.0003V21.6663H2.00033ZM3.33366 20.333H16.667V2.99965H3.33366V20.333ZM7.07766 17.6663H8.41099V5.66631H7.07766V17.6663ZM11.5897 17.6663H12.923V5.66631H11.5897V17.6663Z"
                    fill="black"
                  />
                </svg>
              </button>
            </Box>
          </Box>

          <Box display="flex" flexDirection="row" gap={5} mt={4}>
            <PricingType text={getTypeName(classData?.type)} />
            <PricingType text={getSubTypeName(classData?.subType)} />
          </Box>
        </Box>

        <Box>
          {cartPlans?.map((option, i) => (
            <CartSinglePlan
              classesPlans={classesPlans}
              data={option}
              key={i}
              classData={classData}
              planTitle={`Plan ${i + 1}`}
            />
          ))}
        </Box>

        <Box sx={totalContainerStyles}>
          <Typography sx={totalLabelStyles}>Total: </Typography>
          <Typography sx={totalPriceStyles}>${totalCount}</Typography>
        </Box>
      </Card>
      {isModalOpen && (
        <CheckoutModal
          pricingData={classData}
          open={isModalOpen}
          setOpen={setIsModalOpen}
          isUpdate={data}
          isLoggedIn={isLoggedIn}
          handleUpdateSuccess={handleUpdateSuccess}
        />
      )}
    </>
  );
};

export default PlanSelector;

const CartSinglePlan = ({ planTitle, data, classesPlans, classData }) => {
  const plan = classesPlans.find(
    (f) => String(f.planId) === String(data.planId)
  );

  const planProps = {
    duration: plan.duration,
    type: classData.type,
    subType: classData.subType,
  };

  const planTitleText = getPlansTitle(planProps).split(":")[0];

  return (
    <Box sx={{ width: "100%", borderBottom: "1px solid #b3b3b373", py: 2 }}>
      <Typography sx={{ fontWeight: "bolder", mb: 2 }}>{planTitle}</Typography>
      <Box
        display="flex"
        flexDirection="row"
        justifyContent="space-between"
        alignItems="center"
        width="100%"
        mb={2}
      >
        <LabelValue
          label="Plan type"
          value={planTitleText}
          sx={{ width: "80%" }}
        />
        <LabelValue
          label="Price"
          value={`$${plan?.price}`}
          sx={{ width: "20%" }}
        />
      </Box>
      <Box
        display="flex"
        justifyContent="space-between"
        flexDirection="row"
        alignItems="center"
        width="100%"
        mb={2}
      >
        <LabelValue
          label="Start Date"
          value={
            data.isDateEnabled ? formatDate({ date: data.startDate }) : "-"
          }
          sx={{ width: "80%" }}
        />
        <LabelValue
          label="For"
          value={getPlanFor(data.planFor)}
          sx={{ width: "20%" }}
        />
      </Box>
    </Box>
  );
};

const LabelValue = ({ label, value, sx }) => {
  return (
    <Box sx={sx}>
      <Typography sx={{ color: "rgba(109, 109, 109, 1)", fontSize: 12 }}>
        {label}
      </Typography>
      <Typography
        sx={{
          color: "rgba(0, 0, 0, 1)",
          fontWeight: "500",
          fontSize: 13,
          width: "30%",
        }}
      >
        {value}
      </Typography>
    </Box>
  );
};
