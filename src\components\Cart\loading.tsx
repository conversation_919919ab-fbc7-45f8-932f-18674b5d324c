import React from "react";
import Skeleton from "@mui/material/Skeleton";
import { Box, Container, Grid } from "@mui/material";

const containerStyles = { marginTop: "30px", height: "100%" };

const CartLoading = () => {
  return (
    <Container maxWidth="lg" sx={containerStyles}>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        mb={10}
      >
        <Skeleton
          variant="rectangular"
          sx={{ marginTop: "1rem" }}
          width="40%"
          height={30}
        />
      </Box>
      <Grid container spacing={9}>
        <Grid item xs={12} md={7}>
          {new Array(2).fill("").map((m, i) => (
            <PlanSkeleton key={i} />
          ))}
        </Grid>
        <CheckoutSkeleton />
      </Grid>
    </Container>
  );
};

const CheckoutSkeleton = () => {
  return (
    <Grid item xs={12} md={5}>
      <Skeleton
        variant="rectangular"
        sx={{ marginTop: "1rem" }}
        width="100%"
        height={50}
      />

      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
      >
        <Skeleton
          variant="rectangular"
          sx={{ marginTop: "1rem" }}
          width="30%"
          height={20}
        />
        <Skeleton
          variant="rectangular"
          sx={{ marginTop: "1rem" }}
          width="50%"
          height={20}
        />
      </Box>

      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
      >
        <Skeleton
          variant="rectangular"
          sx={{ marginTop: "1rem" }}
          width="30%"
          height={20}
        />
        <Skeleton
          variant="rectangular"
          sx={{ marginTop: "1rem" }}
          width="50%"
          height={20}
        />
      </Box>
      <Skeleton
        variant="rectangular"
        sx={{ marginTop: "1rem" }}
        width="100%"
        height={60}
      />
    </Grid>
  );
};

const PlanSkeleton = () => {
  return (
    <Box>
      <Skeleton variant="rectangular" width="20%" height={20} />
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
      >
        <Box width="50%">
          <Skeleton
            variant="rectangular"
            sx={{ marginTop: "1rem" }}
            width="30%"
            height={20}
          />
          <Skeleton
            variant="rectangular"
            sx={{ marginTop: "1rem" }}
            width="70%"
            height={10}
          />
        </Box>
        <Box
          width="50%"
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="end"
          gap={2}
        >
          <Skeleton
            variant="rectangular"
            sx={{ marginTop: "1rem" }}
            width={50}
            height={50}
          />
          <Skeleton
            variant="rectangular"
            sx={{ marginTop: "1rem" }}
            width={50}
            height={50}
          />
        </Box>
      </Box>
      <Skeleton
        variant="rectangular"
        sx={{ marginTop: "1rem" }}
        width="100%"
        height={80}
      />
      <Skeleton
        variant="rectangular"
        sx={{ marginTop: "1rem" }}
        width="100%"
        height={80}
      />
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="end"
      >
        <Skeleton
          variant="rectangular"
          sx={{ marginTop: "1rem" }}
          width="30%"
          height={80}
        />
      </Box>
    </Box>
  );
};

export default CartLoading;
