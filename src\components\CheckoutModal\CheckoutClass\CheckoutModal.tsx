import { Box, Modal } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { useSnackbar } from "@/hooks/useSnackbar";
import { CartType, ClassesPricingType } from "@/api/mongoTypes";
import { CURRENCY_ENUM, PLAN_FOR } from "@/constant/Enums";
import { localPlanType } from "@/types";
import { getPricingTitle, getSubTypeName, getTypeName } from "@/utils/format";
import usePayment from "@/hooks/usePayment";
import { validateEmail } from "@/utils/validate";
import useCart from "@/hooks/useCart";
import { useUserContext } from "@/contexts/UserContext";
import { getLocalBuyClassDetaills, getLocalCarts } from "@/utils/classes";
import { formatDate, getDateAsPerUTC, getEndOfDayUTC } from "@/utils/dateTime";
import Header from "./Header";
import SinglePlan from "./SinglePlan";
import CheckoutModalFooter from "../CheckoutModalFooter";

const INITIAL_STATE: localPlanType = {
  planId: "1",
  startDate: new Date(),
  endDate: new Date(),
  planFor: PLAN_FOR.MYSELF,
  emailId: "",
  isDateEnabled: false,
};

type CheckoutModalProps = React.FC<{
  pricingData: ClassesPricingType;
  isLoggedIn: boolean;
  open: boolean;
  redirectToSignup?: () => void;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isUpdate?: CartType;
  handleUpdateSuccess?: (data: CartType) => void;
}>;

type priceDetailsType = {
  amount: number;
  discount: number;
  planId: string;
  index: number;
};

const CheckoutModal: CheckoutModalProps = ({
  open,
  setOpen,
  pricingData,
  isUpdate,
  handleUpdateSuccess,
  isLoggedIn,
  redirectToSignup,
}) => {
  const { showSnackbar } = useSnackbar();
  const { isMaking, makePayment } = usePayment();
  const handleClose = () => setOpen(false);
  const [plans, setPlans] = useState(
    isUpdate?.plans ? isUpdate?.plans : [INITIAL_STATE]
  );
  const [priceDetails, setPriceDetails] = useState<priceDetailsType[]>([]);
  const [buttonDisabled, setButtonDisabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { dbUser } = useUserContext();

  const { handleAddToCart, isAddingToCart, isUpdatingCart, handleUpdateCart } =
    useCart({
      onDeleteSuccess: () => {},
      onAddingSuccess: () => {
        setOpen(false);
      },
    });

  const title = getPricingTitle({
    data: pricingData,
  });

  console.log("plans", plans);

  useEffect(() => {
    const validatePlan = () => {
      if (plans.length > 0) {
        let emailValid = true;
        let dateValid = true;
        let plansValid = true;

        const emails = plans.filter((f) => f.planFor === PLAN_FOR.SOMEONE);
        if (emails.length > 0) {
          const isDbEmail = emails.find((s) => s.emailId === dbUser?.email);
          if (isDbEmail) {
            showSnackbar("You cannot use your email for this action.", {
              type: "error",
            });
            emailValid = false;
          }
          if (emailValid) {
            const everyEmailGood = emails.every((f) =>
              validateEmail(f.emailId)
            );
            emailValid = everyEmailGood;
          }
        }

        const isPlansValid = (plans: localPlanType[]) => {
          if (plans.length <= 1) {
            return true;
          }
          const getPlanString = (plan) => {
            return `${String(plan.planId)}_${formatDate({
              date: plan.startDate,
            })}_${plan.isDateEnabled}_${plan.planFor}`;
          };

          const isDuplicatePlanExists = plans.some((f) => {
            const dup = plans.filter(
              (j) => getPlanString(j) === getPlanString(f)
            );
            if (dup.length > 1) {
              return true;
            }
            return false;
          });

          return !isDuplicatePlanExists;
        };
        plansValid = isPlansValid(plans);
        const condition = !plansValid || !dateValid || !emailValid;

        setButtonDisabled(condition);
      }
    };

    validatePlan();
  }, [plans]);

  const finalAmount = useMemo(() => {
    if (isUpdate) {
      const classPlans = (() => {
        if (
          (isUpdate?.classesId && typeof isUpdate?.classesId !== "object") ||
          !(isUpdate?.classesId && "plans" in isUpdate?.classesId)
        ) {
          return [];
        }
        return isUpdate.classesId.plans;
      })();

      const boughtPlans = plans;

      const totalPrice = boughtPlans?.reduce((total, boughtPlan) => {
        const matchingPlan = classPlans.find(
          (plan) => String(plan.planId) === String(boughtPlan.planId)
        );
        return matchingPlan ? total + matchingPlan.price : total;
      }, 0);
      return totalPrice;
    }
    return priceDetails.reduce(
      (accumulator, currentValue) => +currentValue.amount + accumulator,
      0
    );
  }, [priceDetails, isUpdate]);

  useEffect(() => {
    if (pricingData && finalAmount > 0) {
      const wasClassBuying = getLocalBuyClassDetaills();
      if (wasClassBuying?.uniqueId) {
        const idSame = wasClassBuying?.uniqueId === pricingData.uniqueId;
        if (idSame) {
          handleBuyNow();
          localStorage.removeItem("BUY_CLASS");
        }
      }
    }
  }, [pricingData, finalAmount]);

  const handleBuyNow = async () => {
    const showErrorMessage = () => {
      setIsLoading(false);
      showSnackbar("Failed to add to the cart", {
        type: "error",
      });
    };

    const plansWithUTC = plans.map((m) => ({
      ...m,
      startDate: getDateAsPerUTC(m.startDate, true),
      endDate: getEndOfDayUTC(m.endDate, true),
    }));

    try {
      const payload = {
        cartId: [],
        eventId: [],
        classesDetails: [
          {
            classInfo: pricingData,
            plans: plansWithUTC,
          },
        ],
        clubsDetails: [],
        price: finalAmount,
        productData: [
          {
            name: title,
            description: "",
            images: ["https://www.patitofeo.com/patitoB.png"],
            price: finalAmount,
          },
        ],
        eventsPriceDetails: [],
        currency: CURRENCY_ENUM.USD,
      };
      await makePayment(payload);
    } catch (error) {
      console.error("Something went wrong in the handleBuyNow due to ", error);
      showErrorMessage();
    }
  };

  useEffect(() => {
    if (isUpdate?.plans) {
      setPlans(isUpdate?.plans);
    }
  }, [isUpdate]);

  const handle = () => {
    if (isUpdate && !isLoggedIn) {
      const payload = {
        _id: isUpdate._id,
        classesId: pricingData,
        eventId: null,
        plans,
      };
      const cartItems = getLocalCarts();
      const newCarts = cartItems.map((m) => {
        if (m._id === isUpdate._id) {
          return payload;
        }
        return m;
      });
      localStorage.setItem("CART", JSON.stringify(newCarts));
      showSnackbar("Updated cart successfully", {
        type: "success",
      });
      handleUpdateSuccess(payload as CartType);
      handleClose();
    } else if (isUpdate) {
      handleUpdateCart({
        id: isUpdate?._id,
        plans,
        classesId: pricingData,
        onUpdateSuccess: (res) => {
          handleUpdateSuccess(res);
          handleClose();
        },
      });
    } else {
      handleAddToCart({
        classesId: pricingData,
        eventId: null,
        plans,
        onSuccess: () => {
          handleClose();
        },
      });
    }
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: {
            xs: "100%",
            sm: "100%",
            md: 900,
          },
          bgcolor: "background.paper",
          boxShadow: 24,
          borderRadius: 2,
        }}
      >
        <Box
          sx={{
            p: 6,
            width: "100%",
            maxHeight: "90vh",
            position: "relative",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Header
            pricingData={pricingData}
            handleClose={handleClose}
            onClickAdd={() => {
              setPlans((prev) => [...prev, INITIAL_STATE]);
            }}
            onClickSub={() => {
              const arr = [...plans];
              arr.pop();
              setPlans(arr);

              const priceDetailsArr = [...priceDetails];
              priceDetailsArr.pop();
              setPriceDetails(priceDetailsArr);
            }}
            count={plans.length}
          />

          <Box
            sx={{
              overflow: "auto",
              flex: 1,
            }}
          >
            <Box>
              {plans.map((m, i) => (
                <SinglePlan
                  setPlans={setPlans}
                  length={plans.length}
                  plan={m}
                  pricingData={pricingData}
                  key={i}
                  index={i}
                  isUpdate={isUpdate}
                  priceDetails={priceDetails}
                  setPriceDetails={setPriceDetails}
                />
              ))}
            </Box>
          </Box>

          <CheckoutModalFooter
            buttonDisabled={buttonDisabled}
            isLoading={isLoading || isAddingToCart || isUpdatingCart}
            isMaking={isMaking}
            isUpdate={!!isUpdate}
            handleBuyNow={() => {
              if (isLoggedIn) {
                handleBuyNow();
              } else {
                localStorage.setItem("BUY_CLASS", JSON.stringify(pricingData));
                redirectToSignup();
              }
            }}
            handleAddTocart={() => {
              handle();
            }}
            total={finalAmount}
            currency={CURRENCY_ENUM.USD}
          />
        </Box>
      </Box>
    </Modal>
  );
};

export default CheckoutModal;
