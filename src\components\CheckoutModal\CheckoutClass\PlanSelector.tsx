import React, { useState } from "react";
import { Card, Typography, Box } from "@mui/material";
import PricingType from "../../classes/PricingType";
import {
  getPlansTitle,
  getPricingTitle,
  getSubTypeName,
  getTypeName,
} from "@/utils/format";
import { CartType } from "@/api/mongoTypes";
import { formatDate } from "@/utils/dateTime";
import { getPlanFor } from "@/utils/classes";
import CheckoutModal from "./CheckoutModal";
import useCart from "@/hooks/useCart";
import EditButton from "@/components/Cart/EditButton";
import DeleteButton from "@/components/Cart/DeleteButton";

const cardContainerStyles = {
  padding: "20px 30px",
  border: "1px solid rgba(217, 217, 217, 1)",
  boxShadow: "0",
  marginBottom: "25px",
  borderRadius: "10px",
};
const cardTitleStyles = {
  textTransform: "capitalize",
  marginBottom: 1,
  fontWeight: "500",
  fontSize: "24px",
};

const titleContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
};

const totalContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "end",
  mt: 3,
};
const totalLabelStyles = {
  color: "#AEAEAE",
  fontWeight: "500",
  fontSize: "24px",
  marginRight: "15px",
  textTransform: "capitalize",
};
const totalPriceStyles = {
  fontWeight: "500",
  fontSize: "24px",
  textTransform: "capitalize",
};

type PlanSelectorProps = React.FC<{
  data: CartType;
  handleDeleteSuccess: () => void;
  handleUpdateSuccess: (data: CartType) => void;
  isLoggedIn: boolean;
}>;

const PlanSelector: PlanSelectorProps = ({
  data,
  handleDeleteSuccess,
  handleUpdateSuccess,
  isLoggedIn,
}) => {
  const { handleDelete, isDeletingCart: isDeleting } = useCart({
    onDeleteSuccess: () => {
      handleDeleteSuccess();
    },
    onAddingSuccess: () => {},
  });

  const [isModalOpen, setIsModalOpen] = useState(false);
  const classData = (() => {
    if (typeof data.classesId !== "object" || !("plans" in data.classesId)) {
      return null;
    }
    return data.classesId;
  })();
  const title = getPricingTitle({
    data: classData,
  });

  const classesPlans = classData.plans;
  const cartPlans = data.plans;

  const totalCount = (() => {
    let count = 0;
    cartPlans.map((m) => {
      const plan = classesPlans.find(
        (f) => String(f.planId) === String(m.planId)
      );
      count = +count + plan.price;
      // getDicountedPrice({
      //   discount: plan.discount ?? 0,
      //   price: plan.price ?? 0,
      // });
    });
    return count;
  })();

  return (
    <>
      <Card sx={cardContainerStyles}>
        <Box
          sx={{
            marginBottom: "1rem",
            pb: 4,
            borderBottom: "1px solid #b3b3b373",
          }}
        >
          <Box sx={titleContainerStyles}>
            <Typography sx={cardTitleStyles}>{title}</Typography>
            <Box display="flex" flexDirection="row" gap={3}>
              <EditButton
                onClick={() => {
                  setIsModalOpen(true);
                }}
              />
              <DeleteButton
                disabled={isDeleting}
                aria-disabled={isDeleting}
                onClick={() => {
                  handleDelete({ id: data._id });
                }}
              />
            </Box>
          </Box>

          <Box display="flex" flexDirection="row" gap={5} mt={4}>
            <PricingType text={getTypeName(classData?.type)} />
            <PricingType text={getSubTypeName(classData?.subType)} />
          </Box>
        </Box>

        <Box>
          {cartPlans?.map((option, i) => (
            <CartSinglePlan
              classesPlans={classesPlans}
              data={option}
              key={i}
              classData={classData}
              planTitle={`Plan ${i + 1}`}
            />
          ))}
        </Box>

        <Box sx={totalContainerStyles}>
          <Typography sx={totalLabelStyles}>Total: </Typography>
          <Typography sx={totalPriceStyles}>${totalCount}</Typography>
        </Box>
      </Card>
      {isModalOpen && (
        <CheckoutModal
          pricingData={classData}
          open={isModalOpen}
          setOpen={setIsModalOpen}
          isUpdate={data}
          isLoggedIn={isLoggedIn}
          handleUpdateSuccess={handleUpdateSuccess}
        />
      )}
    </>
  );
};

export default PlanSelector;

const CartSinglePlan = ({ planTitle, data, classesPlans, classData }) => {
  const plan = classesPlans.find(
    (f) => String(f.planId) === String(data.planId)
  );

  const planProps = {
    duration: plan.duration,
    type: classData.type,
    subType: classData.subType,
  };

  const planTitleText = getPlansTitle(planProps).split(":")[0];

  return (
    <Box sx={{ width: "100%", borderBottom: "1px solid #b3b3b373", py: 2 }}>
      <Typography sx={{ fontWeight: "bolder", mb: 2 }}>{planTitle}</Typography>
      <Box
        display="flex"
        flexDirection="row"
        justifyContent="space-between"
        alignItems="center"
        width="100%"
        mb={2}
      >
        <LabelValue
          label="Plan type"
          value={planTitleText}
          sx={{ width: "80%" }}
        />
        <LabelValue
          label="Price"
          value={`$${plan?.price}`}
          sx={{ width: "20%" }}
        />
      </Box>
      <Box
        display="flex"
        justifyContent="space-between"
        flexDirection="row"
        alignItems="center"
        width="100%"
        mb={2}
      >
        <LabelValue
          label="Start Date"
          value={
            data.isDateEnabled ? formatDate({ date: data.startDate }) : "-"
          }
          sx={{ width: "80%" }}
        />
        <LabelValue
          label="For"
          value={getPlanFor(data.planFor)}
          sx={{ width: "20%" }}
        />
      </Box>
    </Box>
  );
};

const LabelValue = ({ label, value, sx }) => {
  return (
    <Box sx={sx}>
      <Typography sx={{ color: "rgba(109, 109, 109, 1)", fontSize: 12 }}>
        {label}
      </Typography>
      <Typography
        sx={{
          color: "rgba(0, 0, 0, 1)",
          fontWeight: "500",
          fontSize: 13,
          width: "30%",
        }}
      >
        {value}
      </Typography>
    </Box>
  );
};
