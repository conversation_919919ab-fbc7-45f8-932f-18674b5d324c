import {
  Box,
  FormControlLabel,
  Grid,
  RadioGroup,
  Switch,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import dayjs from "dayjs";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { CartType, ClassesPricingType } from "@/api/mongoTypes";
import { DURATION_TYPE, PLAN_FOR } from "@/constant/Enums";
import { localPlanType } from "@/types";
import { validateEmail } from "@/utils/validate";
import SectionLabel from "../SectionLabel";
import PlanForCard from "../PlanForCard";
import StartEndDateSelector from "./StartEndDateSelector";
import { addDays } from "date-fns";
import SinglePlanOption from "./SinglePlanOption";
import { getWeekOptions } from "@/utils/dateTime";

const radioGroupStyles = { marginBottom: "25px" };
const pickerStyles = { width: "100%" };

type priceDetailsType = {
  amount: number;
  discount: number;
  planId: string;
  index: number;
};
type SinglePlanProps = React.FC<{
  plan: localPlanType;
  index: number;
  length: number;
  setPlans: React.Dispatch<React.SetStateAction<localPlanType[]>>;
  pricingData: ClassesPricingType;
  isUpdate: CartType;
  priceDetails: priceDetailsType[];
  setPriceDetails: React.Dispatch<React.SetStateAction<priceDetailsType[]>>;
}>;
const SinglePlan: SinglePlanProps = ({
  index,
  plan,
  setPlans,
  pricingData,
  isUpdate,
  priceDetails,
  setPriceDetails,
  length,
}) => {
  const [isMySelf, setIsMySelf] = useState(true);
  const [selectedOption, setSelectedOption] = useState("");
  const [recipent, setRecipent] = useState("");
  const [startDate, setStartDate] = useState(dayjs(new Date()));
  const [endDate, setEndDate] = useState(dayjs(new Date()));
  const [enableStartDate, setEnableStartDate] = useState(false);
  const [clicked, setClicked] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(pricingData.plans[0]);

  const updatePlanProperty = (
    index: number,
    property: string,
    value: string | boolean | Date
  ) => {
    setPlans((prevPlans) =>
      prevPlans.map((plan, i) =>
        i === index ? { ...plan, [property]: value } : plan
      )
    );
  };

  const handleOptionChange = (event) => {
    const value = event.target.value;
    setSelectedOption(value);
  };

  const handlePriceDetails = ({ planId, amount, discount, index }) => {
    const prev = priceDetails;
    const exists = prev.some((m) => m.index === index);
    const newPriceDetails = (() => {
      if (exists) {
        const newDetails = prev.map((m, i) => {
          if (m.index === index) {
            return {
              ...m,
              amount,
              discount,
              index,
            };
          }
          return m;
        });
        return newDetails;
      } else {
        return [
          ...prev,
          {
            planId,
            amount,
            discount,
            index,
          },
        ];
      }
    })();
    setPriceDetails(newPriceDetails);
  };

  useEffect(() => {
    const handlePlanChange = (planId) => {
      const defaultPlan = planId
        ? String(planId)
        : String(pricingData.plans[0].planId);

      const pricingDetails = pricingData.plans.find(
        (f) => String(f.planId) === defaultPlan
      );

      setIsMySelf(plan.planFor === PLAN_FOR.MYSELF);
      setRecipent(plan.emailId);
      setStartDate(dayjs(plan.startDate));
      setEnableStartDate(plan.isDateEnabled);
      setSelectedOption(defaultPlan);

      updatePlanProperty(index, "planId", defaultPlan);
      handlePriceDetails({
        amount: pricingDetails.price,
        discount: pricingDetails.discount,
        planId: defaultPlan,
        index,
      });
    };

    if (!clicked) {
      if (isUpdate && plan?.planId) {
        handlePlanChange(String(plan?.planId));
      } else {
        handlePlanChange(null);
      }
    }
  }, [plan.planId, isUpdate, pricingData, index, clicked]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.checked;
    setEnableStartDate(value);
    const weekdata = getWeekOptions({
      showStartEndDate: true,
    })[0];
    setStartDate(dayjs(weekdata.startDate));
    setEndDate(dayjs(weekdata.endDate));
    setPlans((prevPlans) =>
      prevPlans.map((plan, i) =>
        i === index
          ? ({
              ...plan,
              isDateEnabled: value,
              endDate: weekdata.endDate,
              startDate: weekdata.startDate,
            } as any)
          : plan
      )
    );
  };

  return (
    <Box
      sx={{
        borderBottom: "1px solid silver",
        py: 2,
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
      >
        <Typography
          sx={{
            fontWeight: "700",
            mb: 1,
            fontSize: 16,
          }}
        >
          Plan {index + 1}
        </Typography>
        {length !== 1 && (
          <svg
            width="20"
            height="20"
            onClick={() => {
              setPlans((prevPlans) => prevPlans.filter((f, i) => i !== index));
              setPriceDetails((prev) => prev.filter((f, i) => i !== index));
            }}
            style={{ cursor: "pointer" }}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10 19C14.9706 19 19 14.9706 19 10C19 5.02944 14.9706 1 10 1C5.02944 1 1 5.02944 1 10C1 14.9706 5.02944 19 10 19Z"
              stroke="#EC221F"
            />
            <path d="M5.5 10H14.5" stroke="#EC221F" />
          </svg>
        )}
      </Box>

      <SectionLabel text="Select Plan Type" />

      <RadioGroup
        value={selectedOption}
        onChange={handleOptionChange}
        sx={radioGroupStyles}
      >
        <Grid container spacing={2}>
          {pricingData.plans.map((option, i) => (
            // {options.map((option, i) => (
            <SinglePlanOption
              key={i}
              option={option}
              selectedOption={selectedOption}
              pricingData={pricingData}
              onClick={() => {
                setSelectedPlan(option);
                setClicked(true);
                const selectedPlanId = String(option?.planId);
                // updatePlanProperty(index, "planId", selectedPlanId);
                handlePriceDetails({
                  planId: selectedPlanId,
                  amount: option.price,
                  discount: option.discount,
                  index,
                });
                const duration = option.duration;
                const durationLogic = duration === 1 ? 0 : (duration - 1) * 7;
                const endWeekThursday = addDays(
                  startDate.toDate(),
                  3 + durationLogic
                );
                setPlans((prevPlans) =>
                  prevPlans.map((plan, i) =>
                    i === index
                      ? ({
                          ...plan,
                          planId: selectedPlanId,
                          endDate: endWeekThursday,
                          startDate: startDate,
                        } as any)
                      : plan
                  )
                );
                setStartDate(dayjs(startDate));
                setEndDate(dayjs(endWeekThursday));
              }}
            />
          ))}
        </Grid>
      </RadioGroup>

      <SectionLabel text="Select a start date" />

      <FormControlLabel
        control={<Switch checked={enableStartDate} onChange={handleChange} />}
        label="Select a start date"
      />

      {enableStartDate && (
        <Grid container spacing={2} marginTop={2} marginBottom={4}>
          <Grid item xs={12} md={6}>
            {pricingData.durationType === DURATION_TYPE.WEEK ? (
              <StartEndDateSelector
                startDate={startDate}
                endDate={endDate}
                onSelect={(val) => {
                  const startDate = val.startDate;
                  const endDate = val.endDate;
                  const convertedStartDateISO = startDate.toISOString();
                  const convertedEndDateISO = endDate.toISOString();
                  // console.log({
                  //   startDate,
                  //   endDate,
                  //   convertedStartDateISO,
                  //   convertedEndDateISO,
                  // });
                  // updatePlanProperty(index, "startDate", convertedStartDateISO);
                  // updatePlanProperty(index, "endDate", convertedEndDateISO);
                  // console.log("selectedPlan", selectedPlan);

                  const duration = selectedPlan.duration;
                  const durationLogic = duration === 1 ? 0 : (duration - 1) * 7;
                  const endWeekThursday = addDays(startDate, 3 + durationLogic);
                  setPlans((prevPlans) =>
                    prevPlans.map((plan, i) =>
                      i === index
                        ? {
                            ...plan,
                            startDate: startDate,
                            endDate: endWeekThursday,
                          }
                        : plan
                    )
                  );
                  setStartDate(dayjs(startDate));
                  setEndDate(dayjs(endWeekThursday));
                }}
              />
            ) : (
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DatePicker
                  sx={pickerStyles}
                  label="Start Date"
                  value={dayjs(startDate)}
                  disablePast={isUpdate ? false : true}
                  onChange={(newValue) => {
                    const convertedDate = dayjs(newValue);
                    setStartDate(convertedDate);
                    updatePlanProperty(
                      index,
                      "startDate",
                      convertedDate.toISOString()
                    );
                  }}
                />
              </LocalizationProvider>
            )}
          </Grid>
        </Grid>
      )}

      <Box sx={{ marginBottom: 2, marginTop: 2 }}>
        <SectionLabel text="Who is this plan for? " />
      </Box>

      <PlanForCard
        title="Myself"
        isActive={isMySelf}
        description="The plan will be added to your account"
        onClick={() => {
          setIsMySelf(true);

          updatePlanProperty(index, "planFor", PLAN_FOR.MYSELF);
        }}
      />
      <PlanForCard
        title="Someone Else"
        isActive={!isMySelf}
        description="Gift this plan to a friend or family member."
        onClick={() => {
          setIsMySelf(false);
          updatePlanProperty(index, "planFor", PLAN_FOR.SOMEONE);
        }}
      />

      {!isMySelf && (
        <TextField
          label={
            <>
              Recipients Email id<span style={{ color: "red" }}>*</span>
            </>
          }
          variant="outlined"
          value={recipent}
          error={validateEmail(recipent) === false}
          onChange={(e) => {
            const recipentValue = e.target.value;
            setRecipent(recipentValue);
            updatePlanProperty(index, "emailId", recipentValue);
          }}
          fullWidth
        />
      )}
    </Box>
  );
};

export default SinglePlan;
