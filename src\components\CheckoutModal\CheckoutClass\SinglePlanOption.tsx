import { ClassesPricingPlan, ClassesPricingType } from "@/api/mongoTypes";
import { getDuration, getPlansTitle } from "@/utils/format";
import React, { useRef } from "react";
import {
  Box,
  Chip,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  Switch,
  TextField,
  Typography,
} from "@mui/material";

const radioBoxContainerStyles = {
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  alignItems: "left",
  backgroundColor: "#F9F9F9",
  borderRadius: "3px",
  padding: "6px 9px 6px 9px",
  border: "1px solid #f0f0f0",
  cursor: "pointer",
};

const packagePriceStyles = {
  fontWeight: "500",
  fontSize: "16px",
  marginLeft: "0px",
};

const formControlLabelStyles = {
  flexGrow: 1,
  boxShadow: "0",
  textTransform: "capitalize",
};

const iconPriceContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
};

const rateAndSavingsContainerStyles = {
  display: "flex",
  flexDirection: "row",
  paddingLeft: "28px",
  alignItems: "center",
  gap: 2,
};

const rateStyles = {
  marginLeft: 1,
  marginBottom: "15px",
  textTransform: "capitalize",
};

const savingsStyles = {
  borderRadius: "5px",
  backgroundColor: "#F9B238",
  color: "white",
  fontSize: "10px",
  fontWeight: "500",
  maxWidth: "130px",
  marginBottom: "10px",
  textTransform: "capitalize",
};

type SinglePlanOptionProps = React.FC<{
  option: ClassesPricingPlan;
  selectedOption: string;
  pricingData: ClassesPricingType;
  onClick: () => void;
}>;
const SinglePlanOption: SinglePlanOptionProps = ({
  option,
  selectedOption,
  pricingData,
  onClick,
}) => {
  const ref = useRef<HTMLLabelElement>(null);
  const isActive = String(option.planId) === selectedOption;

  const title = getPlansTitle({
    duration: option.duration,
    type: pricingData.type,
    subType: pricingData.subType,
    durationType: pricingData.durationType,
  });

  const durationText = getDuration(pricingData.durationType);

  const singlePrice = (() => {
    // if (option?.singlePrice) {
    //   return option?.singlePrice;
    // }
    return option.price / option.duration;
  })();

  return (
    <Grid
      item
      xs={12}
      md={6}
      key={option.planId}
      onClick={() => {
        onClick();
        if (ref.current) {
          ref.current.click();
        }
      }}
    >
      <Box
        sx={{
          ...radioBoxContainerStyles,
          border: isActive && "1px solid black",
        }}
      >
        <Box sx={iconPriceContainerStyles}>
          <FormControlLabel
            ref={ref}
            value={option.planId}
            control={<Radio />}
            label={title}
            sx={formControlLabelStyles}
          />
          <Typography
            sx={{ ...packagePriceStyles, opacity: !isActive ? 0.5 : 1 }}
          >
            ${option.price}
          </Typography>
        </Box>
        <Box
          sx={{
            ...rateAndSavingsContainerStyles,
            opacity: !isActive ? 0.5 : 1,
          }}
        >
          <Typography variant="body2" color="textSecondary" sx={rateStyles}>
            ${singlePrice.toFixed(2)} / {durationText}
          </Typography>
          {option.discount > 0 && (
            <Chip
              label={`Save ${option.discount}%`}
              size="small"
              sx={savingsStyles}
            />
          )}
        </Box>
      </Box>
    </Grid>
  );
};

export default SinglePlanOption;
