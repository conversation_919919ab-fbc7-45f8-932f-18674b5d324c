import { Box, Typography } from "@mui/material";
import Image from "next/image";
import React, { useState } from "react";
import Close from "../Close";
import Rating from "@/components/classes/OnlineClub/Rating";
import Tag from "@/components/Tag";
import { ClubType, UserType } from "@/api/mongoTypes";
import { getClubImage, getPriceSymbol } from "@/utils/classes";
import CreatorDetails from "@/components/classes/OnlineClub/CreatorDetails";

type HeaderProps = React.FC<{
  handleClose: () => void;
  data: ClubType;
}>;
const Header: HeaderProps = ({ handleClose, data }) => {
  const [isViewMore, setIsViewMore] = useState(false);
  const imageUrl = getClubImage(data);
  const creatorDetails = data.teachers.map((m) => m as UserType);

  return (
    <Box
      sx={{
        borderBottom: "1px solid rgba(245, 245, 245, 1)",
        pb: "1rem",
        mb: "1rem",
      }}
    >
      <Box
        width="100%"
        display="flex"
        flexDirection="row"
        justifyContent="space-between"
        gap={2}
      >
        <Box sx={{ width: "15%", position: "relative", height: 90 }}>
          <Image
            alt="beruh"
            src={imageUrl}
            quality={75}
            objectFit="cover"
            priority
            style={{ borderRadius: 10 }}
            fill
          />
        </Box>
        <Box display="flex" flexDirection="column" sx={{ width: "85%" }}>
          <Box
            display="flex"
            flexDirection="row"
            justifyContent="space-between"
          >
            <Typography fontSize={22} sx={{ mb: 0, fontWeight: "bolder" }}>
              {data.title}
            </Typography>
            <Box>
              <Close onClick={handleClose} />
            </Box>
          </Box>

          {isViewMore && (
            <Box width="100%">
              <Box display="flex" flexDirection="row" alignItems="center">
                <span
                  style={{
                    height: 6,
                    width: 6,
                    borderRadius: 3,
                    background: "rgba(249, 178, 56, 1)",
                  }}
                />
                &nbsp;
                <Typography fontSize={14} color="rgba(109, 109, 109, 1)">
                  ONLINE
                </Typography>
              </Box>

              <CreatorDetails creatorDetails={creatorDetails} />

              <Box
                display="flex"
                flexDirection="row"
                alignItems="center"
                mt={5}
              >
                {data.categories?.map((m) => (
                  <Tag text={m} key={m} />
                ))}
              </Box>

              <Box
                display="flex"
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between"
              >
                <Box mt={5} display="flex" flexDirection="column">
                  <Box
                    display="flex"
                    flexDirection="row"
                    alignItems="center"
                    mb={2}
                  >
                    <Rating />
                    &nbsp;
                    <Typography fontSize={12}>(5 rating)</Typography>
                  </Box>
                  <Typography color="rgba(129, 129, 129, 1)" fontSize={12}>
                    10 Students enrolled
                  </Typography>
                </Box>

                <Box display="flex" flexDirection="row">
                  <Typography
                    color="rgba(174, 174, 174, 1)"
                    fontSize={18}
                    fontWeight={800}
                  >
                    Total :
                  </Typography>
                  &nbsp; &nbsp;
                  <Typography
                    color="rgba(0, 0, 0, 1)"
                    fontSize={18}
                    fontWeight={800}
                  >
                    {getPriceSymbol({ currency: data.currency })}
                    {String(data.price)}
                  </Typography>
                </Box>
              </Box>
            </Box>
          )}
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="end"
            onClick={() => {
              setIsViewMore((prev) => !prev);
            }}
          >
            <Typography
              sx={{
                fontSize: "0.75rem",
                color: "#14A79C",
                fontWeight: "700",
                cursor: "pointer",
              }}
            >
              View More &nbsp;
            </Typography>
            <Box
              style={{
                transform: isViewMore ? "rotate(180deg)" : "",
              }}
            >
              <svg
                width="14"
                height="9"
                viewBox="0 0 14 9"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M13 1.5L7 7.5L1 1.5"
                  stroke="#14A79C"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Header;
