import { Box, Typography } from "@mui/material";
import React from "react";
import CustomButton from "../CustomButton";
import { CURRENCY_ENUM } from "@/constant/Enums";
import { getPriceSymbol } from "@/utils/classes";

type CheckoutModalFooterProps = React.FC<{
  buttonDisabled: boolean;
  isUpdate: boolean;
  isLoading: boolean;
  isMaking: boolean;
  isClubs?: boolean;
  handleAddTocart: () => void;
  handleBuyNow: () => void;
  total: number;
  handleSubscription?: () => void;
  showSubscribe?: boolean;
  currency: CURRENCY_ENUM;
}>;
const CheckoutModalFooter: CheckoutModalFooterProps = ({
  buttonDisabled = false,
  isLoading = false,
  handleAddTocart,
  handleBuyNow,
  total = 298,
  isMaking = false,
  isUpdate = false,
  showSubscribe = false,
  handleSubscription = () => {},
  currency,
}) => {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        p: 3,
        alignItems: "center",
        justifyContent: "center",
        width: "100%",
        background: "#fff",
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        justifyContent="space-between"
        width="100%"
      >
        <span>Total:</span>
        <span>
          <Typography>
            <b>
              {getPriceSymbol({ currency })}
              {total}
            </b>
          </Typography>
        </span>
      </Box>
      <Box
        display="flex"
        flexDirection="row"
        justifyContent={showSubscribe ? "center" : "space-between"}
        width="100%"
        p={2}
        gap={2}
      >
        {showSubscribe ? (
          <CustomButton
            disabled={buttonDisabled}
            aria-disabled={buttonDisabled}
            text="Subscribe"
            sx={{
              width: "100%",
              mt: 4,
            }}
            onClick={() => {
              handleSubscription();
            }}
          />
        ) : (
          <>
            <CustomButton
              text={isUpdate ? "Update" : "Add to Cart"}
              colortype="secondary"
              disabled={isLoading || buttonDisabled}
              aria-disabled={isLoading || buttonDisabled}
              onClick={() => {
                handleAddTocart();
              }}
              sx={{
                width: "100%",
                mt: 4,
              }}
            />
            {!isUpdate && (
              <CustomButton
                disabled={isMaking || buttonDisabled}
                aria-disabled={isMaking || buttonDisabled}
                text="Buy Now"
                sx={{
                  width: "100%",
                  mt: 4,
                }}
                onClick={() => {
                  handleBuyNow();
                }}
              />
            )}
          </>
        )}
      </Box>
    </Box>
  );
};

export default CheckoutModalFooter;
