import { Box, Radio, Typography } from "@mui/material";
import React from "react";

const PlanForCard = ({ title, description, onClick, isActive }) => {
  return (
    <Box
      onClick={onClick}
      display="flex"
      flexDirection="row"
      sx={{ border: "1px solid silver", mb: 2, p: 2, cursor: "pointer" }}
    >
      <Box>
        <Radio checked={isActive} />
      </Box>
      <Box display="flex" flexDirection="column">
        <Typography sx={{ fontWeight: "500", fontSize: 15 }}>
          {title}
        </Typography>
        <Typography
          sx={{
            fontSize: 13,
            color: "rgba(109, 109, 109, 1)",
          }}
        >
          {description}
        </Typography>
      </Box>
    </Box>
  );
};

export default PlanForCard;
