import React, { useState } from "react";
import { Typography, Box } from "@mui/material";

const buttonStyle = {
  minWidth: 36,
  padding: 0,
  display: "flex",
  justifyContent: "center",
  backgroundColor: "#f9f9f9",
  borderRadius: "3px",
  cursor: "pointer",
};

export default function QuantitySelector({ count, onClickAdd, onClickSub }) {
  const handleIncrement = () => {
    onClickAdd();
  };
  const handleDecrement = () => {
    if (count > 1) {
      onClickSub();
    }
  };

  return (
    <Box
      display="flex"
      alignItems="center"
      justifyContent="center"
      sx={{
        gap: 1,
        padding: 1,
        borderRadius: 1,

        width: "fit-content",
      }}
    >
      <Box
        onClick={handleDecrement}
        sx={buttonStyle}
        aria-disabled={count === 1}
      >
        <Typography variant="h6" color="primary">
          -
        </Typography>
      </Box>

      <Typography variant="body1" sx={{ width: 20, textAlign: "center" }}>
        {count}
      </Typography>

      <Box onClick={handleIncrement} sx={buttonStyle}>
        <Typography variant="h6" color="primary">
          +
        </Typography>
      </Box>
    </Box>
  );
}
