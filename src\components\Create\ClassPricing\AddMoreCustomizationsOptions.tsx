import { Box, Button, Typography } from "@mui/material";
import React from "react";

const AddMoreCustomizationsOptions = ({ onClickAdd }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      my={4}
    >
      <Typography color="#696969" fontSize={15} fontWeight={500}>
        Add more Customization options
      </Typography>
      <Button
        onClick={onClickAdd}
        sx={{
          px: 4,
          py: 1,
          border: "1px solid #14A79C",
          color: "#14A79C",
          background: "#fff",
          width: "max-content",
        }}
      >
        <Typography fontSize="0.8rem" fontWeight={500}>
          Add
        </Typography>
      </Button>
    </Box>
  );
};

export default AddMoreCustomizationsOptions;
