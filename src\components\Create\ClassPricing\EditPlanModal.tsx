import { <PERSON>, Button, Modal, Switch, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import ModalHeader from "../modal/ModalHeader";
import CustomButton from "@/components/CustomButton";
import PopularTag from "@/components/PricingCard/PopularTag";
import LimitedDealTag from "@/components/PricingCard/LimitedDealTag";
import PlanRow from "./PlanRow";
import { ClassesPricingPlan, ClassesPricingType } from "@/api/mongoTypes";
import axiosInstance from "@/utils/interceptor";
import { useSnackbar } from "@/hooks/useSnackbar";
import {
  getNumberOfDuration,
  getPricingTitle,
  getSubTypeName,
  getTypeName,
} from "@/utils/format";
import AddMoreCustomizationsOptions from "./AddMoreCustomizationsOptions";
import { v4 as uuidv4 } from "uuid";

type EditPlanModalProps = React.FC<{
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  data: ClassesPricingType;
  setData: React.Dispatch<React.SetStateAction<ClassesPricingType[]>>;
}>;

const EditPlanModal: EditPlanModalProps = ({
  open,
  setOpen,
  data,
  setData,
}) => {
  const [isPopular, setIsPopular] = useState(false);
  const { showSnackbar } = useSnackbar();
  const [isLimitedDeal, setIsLimitedDeal] = useState(false);
  const [customization, setCustomization] = useState<ClassesPricingPlan[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  const handleClose = () => setOpen(false);

  const title = getPricingTitle({
    data: data,
    needSubtitle: false,
  });

  useEffect(() => {
    if (data) {
      setIsPopular(data.isPopular);
      setIsLimitedDeal(data.isLimitedDeal);
      setCustomization(data.plans);
    }
  }, [data]);

  const validateError = () => {
    const isSomeDurationEmpty =
      customization.filter((f) => f.duration === 0).length > 0;
    if (isSomeDurationEmpty) {
      const text = getNumberOfDuration({
        type: data.type,
        subType: data.subType,
      });
      return `${text} cannot be empty`;
    }
    const isSomePriceEmpty =
      customization.filter((f) => f.price === 0).length > 0;
    if (isSomePriceEmpty) {
      return "Price cannot be empty";
    }
    const hasDuplicateDurations = (plans: ClassesPricingPlan[]): boolean => {
      const seenDurations = new Set<number>();
      for (const plan of plans) {
        if (seenDurations.has(plan.duration)) {
          return true;
        }
        seenDurations.add(plan.duration);
      }
      return false;
    };
    if (hasDuplicateDurations(customization)) {
      return "Duration cannot be duplicate";
    }
    return "";
  };

  const handleSave = async () => {
    const handleError = () => {
      setIsSaving(false);
      showSnackbar("Failed to save the class", {
        type: "error",
      });
    };
    const validatedError = validateError();
    if (validatedError) {
      showSnackbar(validatedError, {
        type: "error",
      });
      return;
    }
    try {
      setIsSaving(true);
      const { data: respData } = await axiosInstance.post("classes/update", {
        _id: data._id,
        plans: customization,
        isPopular,
        isLimitedDeal,
      });
      if (respData.success) {
        showSnackbar("Saved the class successfully", {
          type: "success",
        });
        setData((prev) => {
          return prev.map((m) => {
            if (m._id === respData.data._id) {
              return respData.data;
            }
            return m;
          });
        });
        handleClose();
      } else {
        handleError();
      }
      setIsSaving(false);
    } catch (error) {
      console.error(`Something went wrong in handleSave due to ${error}`);
      handleError();
    }
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: {
            xs: "100%",
            sm: "100%",
            md: 900,
          },
          bgcolor: "background.paper",
          boxShadow: 24,
          borderRadius: 2,
        }}
      >
        <ModalHeader
          title={
            <Box display="flex" flexDirection="row" alignItems="center">
              <Typography fontWeight={900}>{getTypeName(data.type)}</Typography>
              <Typography fontWeight={600}>
                &nbsp;{getSubTypeName(data.subType)}
              </Typography>
              <Typography color="#999999"> &nbsp;(English)</Typography>
            </Box>
          }
          close={handleClose}
        />
        <Box p={4}>
          <Typography fontSize="1.15rem" fontWeight={800} mb={6}>
            {title}
          </Typography>

          <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
            <Typography fontWeight={600} fontSize={14}>
              Display
            </Typography>
            <PopularTag />
            <Switch
              checked={isPopular}
              onChange={(e) => {
                const val = e.target.checked;
                setIsPopular(val);
              }}
            />
          </Box>

          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            gap={2}
            mb={5}
          >
            <Typography fontWeight={600} fontSize={14}>
              Display
            </Typography>
            <LimitedDealTag width="auto" />
            <Switch
              checked={isLimitedDeal}
              onChange={(e) => {
                const val = e.target.checked;
                setIsLimitedDeal(val);
              }}
            />
          </Box>

          {customization.map((m, i) => (
            <>
              <PlanRow
                plan={m}
                data={data}
                setData={setCustomization}
                isEdit
                index={i}
                handleDelete={() => {
                  setCustomization((prev) => {
                    return prev
                      .map((m, j) => {
                        if (i !== j) {
                          return m;
                        }
                        return null;
                      })
                      .filter((f) => f);
                  });
                }}
              />
              {i === 0 && (
                <>
                  <AddMoreCustomizationsOptions
                    onClickAdd={() => {
                      setCustomization((prev) => [
                        ...prev,
                        {
                          singlePrice: 0,
                          price: 0,
                          discount: 0,
                          // price: 100,
                          // discount: 10,
                          duration: 1,
                          planId: `${uuidv4()}`,
                        },
                      ]);
                    }}
                  />
                  <Typography color="#B3B3B3" fontSize={14} mb={4}>
                    No Customizing option
                  </Typography>
                </>
              )}
            </>
          ))}

          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            gap={5}
            mt={4}
          >
            <CustomButton
              disabled={isSaving}
              onClick={handleClose}
              sx={{
                width: "50%",
              }}
              text="Cancel"
            />
            <CustomButton
              disabled={isSaving}
              onClick={handleSave}
              colortype="secondary"
              text="Save"
              sx={{
                width: "50%",
              }}
            />
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default EditPlanModal;
