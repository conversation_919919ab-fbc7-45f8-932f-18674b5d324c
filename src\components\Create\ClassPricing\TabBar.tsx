import {
  CLASSES_TYPE,
  IN_PERSON_CLASSES_TABS,
  IN_PERSON_TYPE,
  ONLINE_CLASSES_TABS,
  ONLINE_CLASSES_TYPE,
} from "@/constant/Enums";
import { Box, Typography } from "@mui/material";
import React from "react";

type TabBarProps = React.FC<{
  classType: CLASSES_TYPE;
  setClassType: React.Dispatch<React.SetStateAction<CLASSES_TYPE>>;
  classSubType: IN_PERSON_TYPE | ONLINE_CLASSES_TYPE;
  setClassSubType: React.Dispatch<
    React.SetStateAction<IN_PERSON_TYPE | ONLINE_CLASSES_TYPE>
  >;
}>;
const TabBar: TabBarProps = ({
  classType,
  setClassType,
  classSubType,
  setClassSubType,
}) => {
  const bottomTabsList =
    classType === CLASSES_TYPE.IN_PERSON
      ? IN_PERSON_CLASSES_TABS
      : ONLINE_CLASSES_TABS;

  return (
    <Box width="100%" display="flex" flexDirection="column" mt={5}>
      <Box
        width="100%"
        display="flex"
        flexDirection="row"
        sx={{
          borderBottom: "1px solid #B3B3B3",
        }}
      >
        <Tab
          text="In-Person Classes"
          isActive={classType === CLASSES_TYPE.IN_PERSON}
          onClick={() => {
            setClassType(CLASSES_TYPE.IN_PERSON);
            setClassSubType(IN_PERSON_TYPE.GROUP);
          }}
        />
        <Tab
          text="Online Classes"
          isActive={classType === CLASSES_TYPE.ONLINE}
          onClick={() => {
            setClassType(CLASSES_TYPE.ONLINE);
            setClassSubType(ONLINE_CLASSES_TYPE.QUICK_SESSIONS);
          }}
        />
      </Box>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        mt={6}
        justifyContent="start"
      >
        {bottomTabsList.map((m, i) => (
          <BottomTab
            key={i}
            text={m.name}
            isActive={m.value === classSubType}
            onClick={() => {
              setClassSubType(m.value);
            }}
          />
        ))}
      </Box>
    </Box>
  );
};

const BottomTab = ({ text, isActive, onClick }) => {
  return (
    <Box
      onClick={onClick}
      px={4}
      py={2}
      sx={{
        color: isActive ? "#fff" : "#B3B3B3",
        backgroundColor: isActive ? "#14A79C" : "#fff",
        cursor: "pointer",
      }}
    >
      <Typography fontSize="1rem" fontWeight={isActive ? 600 : 500}>
        {text} Class
      </Typography>
    </Box>
  );
};

const Tab = ({ text, isActive, onClick }) => {
  return (
    <Box
      onClick={onClick}
      px={4}
      py={2}
      sx={{
        color: isActive ? "#000" : "#B3B3B3",
        borderBottom: isActive ? "2px solid #14A79C" : "#fff",
        cursor: "pointer",
      }}
    >
      <Typography fontSize="1rem" fontWeight={isActive ? 900 : 500}>
        {text}
      </Typography>
    </Box>
  );
};

export default TabBar;
