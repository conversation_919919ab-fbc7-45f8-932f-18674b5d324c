import React from "react";
import { Box, TextField } from "@mui/material";
import SelectFileCard from "./SelectFileCard";
import { useSnackbar } from "@/hooks/useSnackbar";

const CreatorCard = ({
  firstName,
  setFirstName,
  lastName,
  setLastName,
  email,
  setEmail,
  setAvatarFile,
  avatarFile,
  style = {},
}) => {
  const { showSnackbar } = useSnackbar();

  const handleSelectFile = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    try {
      if (event.target.files && event.target.files.length > 0) {
        const selectedFile = event.target.files[0];
        setAvatarFile(selectedFile);
      }
    } catch (error) {
      console.error(`omething went wrong in handleSelectFile due to `, error);
      showSnackbar("Something went wrong in selecting file", {
        type: "error",
      });
    }
  };

  return (
    <Box
      sx={{
        borderRadius: 5,
        border: "2px dotted rgba(162, 162, 162, 1)",
        padding: 10,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        ...style,
      }}
    >
      <Box
        display="flex"
        sx={{
          gap: 5,
          marginBottom: 5,
          width: "100%",
          flexDirection: {
            md: "row",
            sm: "column",
          },
        }}
      >
        <TextField
          placeholder="Enter First Name"
          variant="outlined"
          fullWidth
          sx={{
            width: {
              md: "50%",
              sm: "100%",
            },
          }}
          type="text"
          value={firstName}
          onChange={(e) => setFirstName(e.target.value)}
        />
        <TextField
          placeholder="Enter last Name"
          variant="outlined"
          fullWidth
          sx={{
            width: {
              md: "50%",
              sm: "100%",
            },
          }}
          type="text"
          value={lastName}
          onChange={(e) => setLastName(e.target.value)}
        />
      </Box>

      <Box sx={{ width: "100%" }}>
        <TextField
          placeholder="Enter email"
          variant="outlined"
          fullWidth
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
      </Box>

      <p style={{ textAlign: "center", fontWeight: "500" }}>Profile Image </p>

      <SelectFileCard
        setFile={setAvatarFile}
        file={avatarFile}
        isImage
        text="Upload Image"
        onSelect={handleSelectFile}
      />
    </Box>
  );
};

export default CreatorCard;
