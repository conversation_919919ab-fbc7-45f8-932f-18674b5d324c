import Image from "next/image";
import { useState } from "react";

const SelectAvatar = ({ profileImageUrl, firstName, size = 30 }) => {
  const [imageFailed, setImageFailed] = useState(false);
  const borderRadius = size / 2;

  if (profileImageUrl && !imageFailed) {
    return (
      <Image
        style={{ borderRadius }}
        height={size}
        width={size}
        alt={firstName}
        src={profileImageUrl}
        onError={() => setImageFailed(true)}
      />
    );
  } else {
    return (
      <span
        style={{
          borderRadius,
          height: size,
          width: size,
          background: "rgba(204, 204, 204, 1)",
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8 0C9.06087 0 10.0783 0.421427 10.8284 1.17157C11.5786 1.92172 12 2.93913 12 4C12 5.06087 11.5786 6.07828 10.8284 6.82843C10.0783 7.57857 9.06087 8 8 8C6.93913 8 5.92172 7.57857 5.17157 6.82843C4.42143 6.07828 4 5.06087 4 4C4 2.93913 4.42143 1.92172 5.17157 1.17157C5.92172 0.421427 6.93913 0 8 0ZM8 16C8 16 16 16 16 14C16 11.6 12.1 9 8 9C3.9 9 0 11.6 0 14C0 16 8 16 8 16Z"
            fill="white"
          />
        </svg>
      </span>
    );
  }
};

export default SelectAvatar;
