import UploadIcon from "@/constant/icons/UploadIcon";
import { Box, Card, Typography } from "@mui/material";
import Image from "next/image";
import React, { useMemo, useRef } from "react";

type SelectFileCardProps = React.FC<{
  text: string;
  isImage: boolean;
  file: File;
  isMultiple?: boolean;
  setFile: React.Dispatch<React.SetStateAction<File>>;
  onSelect: (e: React.ChangeEvent<HTMLInputElement>) => void;
}>;
const SelectFileCard: SelectFileCardProps = ({
  text,
  isImage = true,
  file,
  onSelect,
  setFile,
  isMultiple = false,
}) => {
  const height = useMemo(() => {
    if (!file) {
      return 120;
    } else {
      return 250;
    }
  }, [file, isImage]);

  const maxWidth = useMemo(() => {
    if (!file) {
      return 100;
    } else {
      return 350;
    }
  }, [file, isImage]);
  // const maxWidth = isImage ? 100 : !file ? 100 : 300;

  const fileInputRef = useRef(null);

  const handleCardClick = () => {
    fileInputRef.current.click();
  };

  return (
    <Box
      height={height}
      width={maxWidth}
      maxWidth={maxWidth}
      sx={{ cursor: "pointer", position: "relative" }}
    >
      {!file ? (
        <Card
          onClick={handleCardClick}
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            width: "100%",
            height: "100%",
            borderRadius: 2,
            padding: 2,
            border: "1px solid #DBDBDB",
            boxShadow: "none",
          }}
        >
          <input
            type="file"
            hidden
            ref={fileInputRef}
            multiple={isMultiple}
            accept={isImage ? "image/*" : "video/*"}
            onChange={onSelect}
          />
          <UploadIcon height={16} width={16} />
          <Typography
            sx={{
              fontSize: 14,
              textAlign: "center",
              textWrap: "wrap",
              paddingRight: 10,
              paddingLeft: 10,
              marginTop: 5,
              color: "#9C9C9C",
            }}
          >
            {text}
          </Typography>
        </Card>
      ) : (
        <div
          style={{
            width: "100%",
            height: "100%",
            background: "#000",
            borderRadius: 15,
          }}
        >
          {isImage ? (
            <>
              <Remove
                onClick={() => {
                  setFile(null);
                }}
              />
              <Image src={URL.createObjectURL(file)} alt="imahe" fill />
            </>
          ) : (
            <>
              <Remove
                onClick={() => {
                  setFile(null);
                }}
              />
              <video
                muted
                style={{
                  borderRadius: 10,
                }}
                height="100%"
                width="100%"
                src={URL.createObjectURL(file)}
              />
            </>
          )}
        </div>
      )}
    </Box>
  );
};

export const Remove = ({ ...props }) => {
  return (
    <span
      {...props}
      style={{
        position: "absolute",
        height: 30,
        width: 30,
        display: "flex",
        flexDirection: "column",
        background: "#fff",
        borderRadius: 15,
        alignItems: "center",
        justifyContent: "center",
        top: 10,
        right: 10,
        zIndex: 100,
      }}
    >
      <svg
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0.757999 11.243L6.001 5.99999L11.244 11.243M11.244 0.756989L6 5.99999L0.757999 0.756989"
          stroke="black"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </span>
  );
};

export default SelectFileCard;
