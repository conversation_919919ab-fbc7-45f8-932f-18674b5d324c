import { Box, Typography } from "@mui/material";
import React, { useState } from "react";
import SelectFileCard, { Remove } from "../SelectFileCard";
import Image from "next/image";

const imageDisclaimerLabelStyles = {
  color: "#787878",
  fontWeight: "500",
  fontsize: "14px",
  marginBottom: "33px",
  marginTop: "20px",
};

const ImageSection = ({
  images,
  setImages,
  setDeletedImageKeys,
  showSnackbar,
  isMultiple = false,
}) => {
  const [file, setFile] = useState(null);

  const handleSelectFile = async (event) => {
    try {
      if (event.target.files && event.target.files.length > 0) {
        const selectedFiles = event.target.files;
        setImages((prev) =>
          isMultiple
            ? [...prev, ...Array.from(selectedFiles)]
            : [selectedFiles[0]]
        );
      }
    } catch (error) {
      console.error("Something went wrong in handleSelectFile due to ", error);
      showSnackbar("Something went wrong in selecting file", {
        type: "error",
      });
    }
  };

  return (
    <>
      <Box
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <SelectFileCard
          file={file}
          isImage
          isMultiple={isMultiple}
          onSelect={handleSelectFile}
          setFile={setFile}
          text="Upload Image"
        />
        <Typography sx={imageDisclaimerLabelStyles}>
          File must be JPEG or PNG and under 1MB
        </Typography>
      </Box>
      <Box
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          gap: 2,
        }}
      >
        {images.map((m, i) => (
          <div
            key={i}
            style={{
              height: 150,
              width: 200,
              background: "#000",
              borderRadius: 15,
              position: "relative",
              marginBottom: 20,
              overflow: "hidden",
            }}
          >
            <Remove
              onClick={() => {
                if (m?.url) {
                  setDeletedImageKeys((prev) => [...prev, m.id]);
                }
                setImages((prev) => {
                  const filteredImages = prev.filter(
                    (fi, index) => index !== i
                  );
                  return filteredImages;
                });
              }}
            />
            <Image
              src={(() => {
                if (m?.name) {
                  return URL.createObjectURL(m);
                }
                return m.url;
              })()}
              alt="image"
              fill
            />
          </div>
        ))}
      </Box>
    </>
  );
};

export default ImageSection;
