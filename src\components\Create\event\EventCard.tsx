import { <PERSON>, <PERSON><PERSON>, Card, Chip, Typography } from "@mui/material";
import Image from "next/image";
import React, { useMemo, useState } from "react";
import ModeStatus from "./ModeStatus";
import LocationInfo from "./LocationInfo";
import DateTime from "./DateTime";
import Tag from "@/components/Tag";
import { EventOnType, EventSchemaType } from "@/api/mongoTypes";
import { CURRENCY_ENUM, EVENT_MODE_TYPE } from "@/constant/Enums";
import {
  dateAsPerTimeZone,
  formatDate,
  formatTime,
  getDateAsPerUTC,
} from "@/utils/dateTime";
import CustomButton, { getBgColor } from "@/components/CustomButton";
import { useSnackbar } from "@/hooks/useSnackbar";
import useCart from "@/hooks/useCart";
import usePayment from "@/hooks/usePayment";
import { NextRouter } from "next/router";
import {
  getEventOnDetails,
  getPriceSymbol,
  getRemainingSeats,
} from "@/utils/classes";
import { getLanguageName, getProficiencyEn } from "@/utils/common";
import axiosInstance from "@/utils/interceptor";
import { EventSchemaWithSingleEvent, MaybeObjectId } from "@/types";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import ShoppingBagIcon from "@mui/icons-material/ShoppingBag";
import { format, isSameDay } from "date-fns";

type EventCardProps = React.FC<{
  data: EventSchemaWithSingleEvent;
  onClick?: () => void;
  redirectToSignup?: () => void;
  isLoggedIn?: boolean;
  isAdmin?: boolean;
  onDelete?: () => void;
}>;

const EventCard: EventCardProps = ({
  data,
  onClick,
  isLoggedIn,
  redirectToSignup,
  isAdmin = false,
  onDelete = () => {},
}) => {
  const { showSnackbar } = useSnackbar();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    const totalCount = Object.values(data.boughtCount).reduce(
      (sum, val) => sum + val,
      0
    );

    if (totalCount > 0) {
      showSnackbar(
        `You cannot delete this event as it has been booked by ${totalCount} users`,
        {
          type: "error",
        }
      );
      return;
    }

    const handleError = () => {
      setIsDeleting(true);
      showSnackbar("Failed to delete event", {
        type: "error",
      });
    };

    try {
      const { data: deletedData, status } = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/event/delete/${data._id}`
      );
      if (status === 409) {
        showSnackbar(deletedData.message, {
          type: "error",
        });
        return;
      }
      if (deletedData.success && deletedData?.data) {
        showSnackbar("Deleted the event successfully", {
          type: "success",
        });
        onDelete && onDelete();
      } else {
        handleError();
      }
      setIsDeleting(false);
    } catch (error) {
      console.log("erropr", error);
      handleError();
    }
  };

  const proficiency = getProficiencyEn({
    data: data?.proficiencyLevel,
  });

  return (
    <Card
      onClick={() => {
        if (onClick) {
          onClick();
        }
      }}
      sx={{
        height: 450,
        // height: isAdmin ? 450 : 350,
        width: {
          xs: "100%",
          md: 350,
          sm: 275,
        },
        borderRadius: 5,
        p: 4,
        boxShadow: "0px 2px 10px 0px #00000029",
        flexDirection: "column",
        display: "flex",
        justifyContent: "space-between",
        // border: isExpired ? "1px solid red" : "",
        position: "relative",
      }}
    >
      {/* {isExpired && (
        <Box
          sx={{
            background: "red",
            color: "#fff",
            padding: "5px 10px",
            borderRadius: 10,
            position: "absolute",
            top: 5,
            right: 5,
            zIndex: 100,
            fontSize: 12,
          }}
        >
          <Typography fontSize={12}>Expired</Typography>
        </Box>
      )} */}

      <Box
        width="100%"
        height={150}
        sx={{ position: "relative", overflow: "hidden", borderRadius: 3 }}
      >
        <Image
          fill
          alt="beruh"
          src={data?.images[0]?.url ?? ""}
          quality={75}
          objectFit="cover"
          priority
        />

        {isAdmin ? (
          <>
            {proficiency && (
              <Chip
                label={proficiency}
                sx={{
                  position: "absolute",
                  top: 5,
                  left: 5,
                  fontSize: 12,
                  textTransform: "uppercase",
                  padding: 0.2,
                  borderRadius: 1,
                  height: "auto",
                  background: "rgba(0, 0, 0, 0.52)",
                  color: "#fff",
                  "& .MuiChip-label": {
                    fontSize: 10,
                    fontWeight: "normal",
                    color: "#fff",
                    padding: "0.2rem",
                  },
                }}
              />
            )}
            <Box
              display="flex"
              flexDirection="row"
              alignItems="center"
              justifyContent="space-between"
              width="100%"
              p={1}
              sx={{
                position: "absolute",
                bottom: 2,
              }}
            >
              <Box
                sx={{
                  background: "rgba(0, 0, 0, 0.52)",
                  color: "#fff",
                  p: 2,
                  borderRadius: 2,
                }}
              >
                <Typography sx={{ fontSize: 12 }}>
                  {getPriceSymbol({
                    currency: data.currency,
                  })}
                  {data.price}
                  <span style={{ fontSize: 10, fontWeight: 400 }}>
                    &nbsp;onwards
                  </span>
                </Typography>
              </Box>
            </Box>
          </>
        ) : (
          <>
            <Typography
              sx={{
                position: "absolute",
                top: 5,
                left: 5,
                fontSize: 12,
                color: "#fff",
              }}
            >
              Created at : {formatTime({ date: data.createdAt })},
              {formatDate({ date: data.createdAt })}
            </Typography>
            {proficiency && (
              <Chip
                label={proficiency}
                sx={{
                  position: "absolute",
                  bottom: 10,
                  right: 10,
                  fontSize: 12,
                  textTransform: "uppercase",
                  padding: 0.2,
                  borderRadius: 1,
                  height: "auto",

                  color: "#6D6D6D",
                  background: "#FFFFFF",
                  "& .MuiChip-label": {
                    fontSize: 12, // Adjust Typography font size
                    fontWeight: "normal", // Example styling
                    color: "#6D6D6D", // Change text color
                    padding: "0.2rem",
                  },
                }}
              />
            )}
          </>
        )}
      </Box>

      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
      >
        <ModeStatus isOnline={data.mode === EVENT_MODE_TYPE.ONLINE} />
        {data?.location && <LocationInfo location={data.location} />}
      </Box>
      <Typography sx={{ fontWeight: "bolder", fontSize: 16 }}>
        {data.title}
      </Typography>
      <Typography
        textOverflow="ellipsis"
        sx={{
          fontSize: 15,
          overflow: "hidden",
          color: "#B3B3B3",
          whiteSpace: "nowrap",
          fontWeight: 300,
        }}
      >
        {data.description}
      </Typography>

      <Box display="flex" flexDirection="row" alignItems="center">
        {data?.categories?.map((m) => (
          <Tag text={m} key={m} />
        ))}
      </Box>

      <Box
        display="flex"
        flexDirection="row"
        mt={2}
        gap={2}
        justifyContent="space-between"
      >
        <Box
          sx={{
            background: "#D7F7F5",
            borderRadius: 10,
            px: 2,
            py: 2,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
          }}
        >
          <Typography fontSize="0.75rem" color="#14A79C" mb={0}>
            Target Language:{" "}
            <b style={{ textTransform: "uppercase" }}>
              {getLanguageName(data?.targetLanguage)}
            </b>
          </Typography>
        </Box>
      </Box>

      <EventDateInfo
        isLoggedIn={isLoggedIn}
        eventOn={data?.eventOn}
        data={data}
        redirectToSignup={redirectToSignup}
      />

      {isAdmin ? (
        <CustomButton
          disabled={isDeleting}
          text="Delete"
          colortype="secondary"
          sx={{
            width: "100%",
            background: "red",
            mt: 3,
            "&:hover": {
              background: "red",
            },
          }}
          onClick={(e) => {
            e.stopPropagation();
            handleDelete();
          }}
        />
      ) : (
        <></>
      )}
    </Card>
  );
};

type EventDateInfoProps = React.FC<{
  eventOn: MaybeObjectId<EventOnType>[] & (EventOnType | EventOnType[]);
  data: EventSchemaWithSingleEvent;
  isLoggedIn: boolean;
  redirectToSignup?: () => void;
}>;
const EventDateInfo: EventDateInfoProps = ({
  eventOn,
  data,
  isLoggedIn,
  redirectToSignup,
}) => {
  const isEventsOnArray = Array.isArray(eventOn);

  if (
    isEventsOnArray &&
    eventOn &&
    eventOn[0] &&
    "startDateTime" in eventOn[0]
  ) {
    const eventsOn = eventOn as EventOnType[];
    return (
      <Box display="flex" flexDirection="column">
        {eventsOn.map((m, i) => (
          <DateRow
            eventsOn={m}
            key={i}
            data={data}
            isLoggedIn={isLoggedIn}
            redirectToSignup={redirectToSignup}
          />
        ))}
      </Box>
    );
  }

  const eventsOn = eventOn as EventOnType;
  return (
    <DateRow
      eventsOn={eventsOn}
      data={data}
      isLoggedIn={isLoggedIn}
      redirectToSignup={redirectToSignup}
    />
  );
};

type DateRowProps = React.FC<{
  eventsOn: EventOnType;
  data: EventSchemaWithSingleEvent;
  isLoggedIn: boolean;
  redirectToSignup?: () => void;
}>;
const DateRow: DateRowProps = ({
  eventsOn,
  data,
  isLoggedIn,
  redirectToSignup,
}) => {
  const { startDate, sartTime, endDate, endTime, isStartEndDateSame } =
    useMemo(() => {
      return getEventOnDetails(eventsOn);
    }, [eventsOn]);

  const { isMaking, makePayment } = usePayment();
  const { handleAddToCart, isAddingToCart } = useCart({
    onDeleteSuccess: () => {},
    onAddingSuccess: () => {},
  });
  const { showSnackbar } = useSnackbar();

  const handleBuy = async () => {
    try {
      makePayment({
        cartId: [],
        price: data.price,
        productData: [
          {
            name: data.title,
            description: data.description,
            images: ["https://www.patitofeo.com/patitoB.png"],
            price: data.price,
          },
        ],
        eventId: [data._id],
        clubsDetails: [],
        classesDetails: [],
        eventsPriceDetails: [
          {
            eventInfo: String(data._id),
            price: +data.price,
            currency: data?.currency ?? CURRENCY_ENUM.USD,
            eventOn: eventsOn._id,
          },
        ],
        currency: data?.currency ?? CURRENCY_ENUM.USD,
      });
    } catch (error) {
      console.error("Something went wrong in handleBuy due to ", error);
      showSnackbar("Failed to buy event", {
        type: "error",
      });
    }
  };
  const remaingCount = getRemainingSeats(data, eventsOn);

  return (
    <Card
      sx={{
        display: "flex",
        flexDirection: "row",
        gap: 2,
        width: "100%",
        mt: 2,
        justifyContent: "space-between",
        p: 2,
        boxShadow: "0px 2px 10px 0px #0000000d;",
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        width="75%"
      >
        <Typography fontSize="0.75rem">
          {formatDate({ date: new Date(startDate) })}
          {isStartEndDateSame ? (
            <>
              &nbsp; {sartTime} - {endTime}
            </>
          ) : (
            <>
              &nbsp; {sartTime} - {formatDate({ date: new Date(endDate) })}{" "}
              {endTime}
            </>
          )}
        </Typography>
        {typeof remaingCount === "number" && (
          <Box
            sx={{
              backgroundColor: "#f3b35854",
              padding: 1,
              borderRadius: 10,
              px: 3,
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <Typography fontSize="0.75rem" mb={0}>
              Available Spots:
              <b>&nbsp;{remaingCount}</b>
            </Typography>
          </Box>
        )}
      </Box>
      <Box
        display="flex"
        alignItems="center"
        flexDirection="row"
        gap={2}
        width="25%"
      >
        <Button
          sx={{
            height: 30,
            width: 30,
            minWidth: 30,
            background: getBgColor("primary"),
            "&:hover": {
              background: getBgColor("primary"),
            },
          }}
          disabled={isAddingToCart || isMaking}
          onClick={(e) => {
            e.stopPropagation();
            handleAddToCart({
              eventId: isLoggedIn ? data?._id : data,
              eventOn: eventsOn,
            });
          }}
        >
          <ShoppingCartIcon sx={{ height: 20, width: 20 }} />
        </Button>
        <Button
          disabled={isMaking || isAddingToCart}
          onClick={(e) => {
            e.stopPropagation();
            if (isLoggedIn) {
              handleBuy();
            } else {
              localStorage.setItem("BUY_EVENT", JSON.stringify(data));
              redirectToSignup();
            }
          }}
          sx={{
            height: 30,
            width: 30,
            minWidth: 30,
            background: getBgColor("secondary"),
            "&:hover": {
              background: getBgColor("secondary"),
            },
          }}
        >
          <ShoppingBagIcon sx={{ height: 20, width: 20 }} />
        </Button>
      </Box>
    </Card>
  );
};

export default EventCard;
