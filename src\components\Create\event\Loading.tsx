import React from "react";
import Skeleton from "@mui/material/Skeleton";
import { Container } from "@mui/material";

const EventBodyLoading = () => {
  return (
    <Container
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: "5",
        marginTop: "20px",
        maxWidth: "800px",
        alignItems: "center",
        width: "100%",
        margin: "0 auto",
        padding: { xs: "20px", sm: "0px" },
      }}
    >
      <Skeleton
        variant="rectangular"
        sx={{ marginBottom: 10, marginTop: 20 }}
        width="100%"
        height={60}
      />
      <Container
        sx={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          marginBottom: 10,
        }}
      >
        <Skeleton variant="rectangular" width={100} height={100} />
      </Container>
      <Skeleton
        variant="rectangular"
        sx={{ marginBottom: 10 }}
        width="100%"
        height={100}
      />
      <Skeleton
        variant="rectangular"
        sx={{ marginBottom: 10 }}
        width="100%"
        height={60}
      />
      <Skeleton
        variant="rectangular"
        sx={{ marginBottom: 10 }}
        width="100%"
        height={60}
      />
    </Container>
  );
};

export default EventBodyLoading;
