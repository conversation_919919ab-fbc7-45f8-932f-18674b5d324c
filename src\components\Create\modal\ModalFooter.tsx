import React from "react";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";

const ModalFooter = ({ onClick, title }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="center"
      sx={{
        fontSize: 20,
        fontWeight: 600,
        padding: 3,
        borderTop: "1px solid silver",
      }}
    >
      <Button onClick={onClick} sx={{ width: 300 }}>
        {title}
      </Button>
    </Box>
  );
};

export default ModalFooter;
