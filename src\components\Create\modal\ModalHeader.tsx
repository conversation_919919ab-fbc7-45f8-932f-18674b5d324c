import React from "react";
import Box from "@mui/material/Box";

const ModalHeader = ({ close, title }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      sx={{
        fontSize: 20,
        fontWeight: 600,
        boxShadow: "0px 4px 10px 0px #00000026",
        padding: 3,
      }}
    >
      <div>{title}</div>
      <span style={{ cursor: "pointer" }} onClick={close}>
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7.5 17.385L12 12.885L16.5 17.385L17.385 16.5L12.885 12L17.385 7.5L16.5 6.615L12 11.115L7.5 6.615L6.615 7.5L11.115 12L6.615 16.5L7.5 17.385ZM12.0037 23.25C10.4488 23.25 8.98625 22.955 7.61625 22.365C6.24708 21.7742 5.05583 20.9725 4.0425 19.96C3.02917 18.9475 2.22708 17.7575 1.63625 16.39C1.04542 15.0225 0.75 13.5604 0.75 12.0037C0.75 10.4471 1.04542 8.98458 1.63625 7.61625C2.22625 6.24708 3.02667 5.05583 4.0375 4.0425C5.04833 3.02917 6.23875 2.22708 7.60875 1.63625C8.97875 1.04542 10.4412 0.75 11.9963 0.75C13.5512 0.75 15.0138 1.04542 16.3837 1.63625C17.7529 2.22625 18.9442 3.02708 19.9575 4.03875C20.9708 5.05042 21.7729 6.24083 22.3638 7.61C22.9546 8.97917 23.25 10.4412 23.25 11.9963C23.25 13.5512 22.955 15.0138 22.365 16.3837C21.775 17.7537 20.9733 18.945 19.96 19.9575C18.9467 20.97 17.7567 21.7721 16.39 22.3638C15.0233 22.9554 13.5613 23.2508 12.0037 23.25ZM12 22C14.7917 22 17.1562 21.0312 19.0938 19.0938C21.0312 17.1562 22 14.7917 22 12C22 9.20833 21.0312 6.84375 19.0938 4.90625C17.1562 2.96875 14.7917 2 12 2C9.20833 2 6.84375 2.96875 4.90625 4.90625C2.96875 6.84375 2 9.20833 2 12C2 14.7917 2.96875 17.1562 4.90625 19.0938C6.84375 21.0312 9.20833 22 12 22Z"
            fill="black"
          />
        </svg>
      </span>
    </Box>
  );
};

export default ModalHeader;
