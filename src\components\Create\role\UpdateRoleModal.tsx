import React, { useEffect, useMemo, useState } from "react";
import ModalHeader from "../modal/ModalHeader";
import { ROLE_TYPES } from "@/constant/Enums";
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Modal,
} from "@mui/material";
import { useSnackbar } from "@/hooks/useSnackbar";
import axios from "axios";
import { UserType } from "@/api/mongoTypes";
import { getUserRoles } from "@/utils/format";
import axiosInstance from "@/utils/interceptor";

type UpdateRoleModalProps = React.FC<{
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<UserType>>;
  selectedUser: UserType;
  onUpdate: (user: UserType) => void;
}>;

const UpdateRoleModal: UpdateRoleModalProps = ({
  open,
  setOpen,
  selectedUser,
  onUpdate = (user: UserType) => {},
}) => {
  const { showSnackbar } = useSnackbar();
  const [isSaving, setIsSaving] = useState(false);
  const [updatedRoles, setUpdatedRoles] = useState<ROLE_TYPES[]>([]);

  useEffect(() => {
    if (selectedUser) {
      const serverRoles = getUserRoles(selectedUser);
      setUpdatedRoles(serverRoles);
    }
  }, [selectedUser]);

  const handleClose = () => setOpen(null);

  const handleSaveChanges = async () => {
    const handleError = (message: string = "") => {
      setIsSaving(false);
      showSnackbar(message ?? "Error updating user details", { type: "error" });
    };

    const isCreatorOrAdmin =
      updatedRoles.includes(ROLE_TYPES.CREATOR) ||
      updatedRoles.includes(ROLE_TYPES.ADMIN);
    if (isCreatorOrAdmin) {
      const isPatitoEmail = selectedUser.email.includes("@patitofeo.com");
      if (!isPatitoEmail) {
        showSnackbar("Only patitofeo.com email is allowed for this role", {
          type: "error",
        });
        return;
      }
    }

    if (updatedRoles.length === 0) {
      showSnackbar(`Roles cannot be empty`, { type: "error" });
      return;
    }

    const updateData = {
      id: selectedUser._id,
      updatedRoles,
    };

    try {
      setIsSaving(true);
      const { data, status } = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/user/update-user-role`,
        updateData
      );
      if (status === 409) {
        handleError(data.message);
        return;
      }
      if (data.success) {
        const responseDetails = data.data;
        showSnackbar("User details updated successfully", { type: "success" });
        setIsSaving(false);
        if (responseDetails) {
          onUpdate(responseDetails);
        }
      } else {
        handleError();
      }
    } catch (error) {
      handleError();
    }
  };

  const isButtonDisabled = useMemo(() => {
    const isRoleSame =
      JSON.stringify(updatedRoles) ===
      JSON.stringify(getUserRoles(selectedUser));

    const condition = isRoleSame;
    console.log({
      isRoleSame,
      condition,
    });
    return condition;
  }, [selectedUser, updatedRoles]);

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: {
            xs: "100%",
            sm: "100%",
            md: 900,
          },
          bgcolor: "background.paper",
          boxShadow: 24,
          borderRadius: 2,
        }}
      >
        <ModalHeader close={handleClose} title="Update Role" />
        <Box p={4} aria-disabled={isSaving}>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2, p: 2 }}>
            <Typography>
              <strong>Name:</strong> {selectedUser.firstName}{" "}
              {selectedUser.lastName}
            </Typography>
            <Typography>
              <strong>Email:</strong> {selectedUser.email}
            </Typography>

            <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
              <Typography>
                <strong>Role:</strong>
              </Typography>
              <FormControl sx={{ width: "100%", mt: 1 }}>
                <InputLabel id="role-select-label">Select Role</InputLabel>
                <Select
                  multiple
                  labelId="role-select-label"
                  id="role-select"
                  value={updatedRoles}
                  onChange={(e) => {
                    const value = e.target.value;
                    const lastElement = value[value.length - 1];
                    if (lastElement === ROLE_TYPES.STUDENT) {
                      setUpdatedRoles([ROLE_TYPES.STUDENT]);
                    } else {
                      const arrayOfRoles = value as ROLE_TYPES[];
                      const noStudent = arrayOfRoles.filter(
                        (f) => f !== ROLE_TYPES.STUDENT
                      );
                      setUpdatedRoles(noStudent);
                    }
                  }}
                  label="Select Role"
                >
                  {Object.values(ROLE_TYPES).map((role) => (
                    <MenuItem key={role} value={role}>
                      {role}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Box>
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="center"
            gap={2}
            mt={3}
          >
            <Button
              variant="contained"
              disabled={isButtonDisabled}
              onClick={handleSaveChanges}
              size="medium"
            >
              Save Changes
            </Button>
            <Button onClick={handleClose}>Cancel</Button>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default UpdateRoleModal;
