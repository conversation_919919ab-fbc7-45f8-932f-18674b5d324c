import { Box, Typography } from "@mui/material";
import { useRouter } from "next/router";
import React from "react";

const Header = () => {
  const router = useRouter();
  return (
    <Box
      display="flex"
      flexDirection="row"
      width="100%"
      justifyContent="center"
      sx={{
        position: "relative",
      }}
    >
      <Box sx={{ position: "absolute", left: 10, top: "50%" }}>
        <svg
          style={{ cursor: "pointer" }}
          onClick={() => {
            router.push("/create");
          }}
          width="21"
          height="21"
          viewBox="0 0 21 21"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19.5417 9.20835H5.11379L11.4171 2.90502C11.9209 2.40127 11.9209 1.57461 11.4171 1.07086C11.2976 0.951113 11.1557 0.856114 10.9994 0.791296C10.8432 0.726479 10.6757 0.693115 10.5065 0.693115C10.3373 0.693115 10.1698 0.726479 10.0136 0.791296C9.85732 0.856114 9.71537 0.951113 9.59588 1.07086L1.0838 9.58294C0.964053 9.70244 0.869054 9.84437 0.804236 10.0006C0.739419 10.1569 0.706055 10.3244 0.706055 10.4936C0.706055 10.6627 0.739419 10.8302 0.804236 10.9865C0.869054 11.1428 0.964053 11.2847 1.0838 11.4042L9.59588 19.9163C9.71546 20.0359 9.85743 20.1307 10.0137 20.1954C10.1699 20.2602 10.3374 20.2935 10.5065 20.2935C10.6756 20.2935 10.8431 20.2602 10.9993 20.1954C11.1556 20.1307 11.2975 20.0359 11.4171 19.9163C11.5367 19.7967 11.6316 19.6547 11.6963 19.4985C11.761 19.3422 11.7943 19.1748 11.7943 19.0056C11.7943 18.8365 11.761 18.6691 11.6963 18.5128C11.6316 18.3566 11.5367 18.2146 11.4171 18.095L5.11379 11.7917H19.5417C20.2521 11.7917 20.8334 11.2104 20.8334 10.5C20.8334 9.78961 20.2521 9.20835 19.5417 9.20835Z"
            fill="black"
          />
        </svg>
      </Box>
      <Typography sx={{ fontSize: "1.5rem", fontWeight: 600 }}>
        Class Scheduler
      </Typography>
    </Box>
  );
};

export default Header;
