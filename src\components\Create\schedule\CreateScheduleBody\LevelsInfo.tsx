import { LEVELS } from "@/constant/Enums";
import { Box, Typography } from "@mui/material";
import React from "react";

const LevelsInfo = () => {
  return (
    <>
      <Typography fontWeight={700} mb={2}>
        Student List
      </Typography>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        flexWrap="wrap"
      >
        {LEVELS.map((m, i) => (
          <Box
            key={i}
            display="flex"
            flexDirection="row"
            alignItems="center"
            gap={1}
          >
            <Box
              sx={{
                height: 16,
                width: 16,
                borderRadius: 8,
                background: m.color,
              }}
            />
            <Typography
              fontSize="0.75rem"
              fontWeight="600"
              textTransform="uppercase"
            >
              {m?.name}
            </Typography>
          </Box>
        ))}
      </Box>
    </>
  );
};

export default LevelsInfo;
