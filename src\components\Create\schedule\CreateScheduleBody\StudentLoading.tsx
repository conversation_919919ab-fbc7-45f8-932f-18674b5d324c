import { Box, Skeleton } from "@mui/material";
import React from "react";

const StudentLoading = () => {
  return (
    <Box width="100%" mt={2}>
      {new Array(5).fill("").map((m, i) => (
        <Box key={i} mb={2} sx={{ borderRadius: 2, overflow: "hidden" }}>
          <Skeleton variant="rectangular" width="100%" height={30} />
        </Box>
      ))}
    </Box>
  );
};

export default StudentLoading;
