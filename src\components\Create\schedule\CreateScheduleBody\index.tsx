import { <PERSON>, Button, Container, Typography } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/router";
import React, { useEffect, useMemo, useState } from "react";
import Header from "./Header";
import ClassTypeHeader from "./ClassTypeHeader";
import {
  CLASSES_TYPE,
  IN_PERSON_TYPE,
  LEVEL_TYPES,
  LEVELS,
} from "@/constant/Enums";
import { Maybe, ValueOf } from "@/types";
import PricingTab from "@/components/PricingTab";
import { getLevelColor, getStudentWeekName } from "@/utils/classes";
import ScheduleModal from "../ScheduleModal";
import WeekSelector from "@/components/WeekSelector";
import { useSnackbar } from "@/hooks/useSnackbar";
import { useUserContext } from "@/contexts/UserContext";
import axiosInstance from "@/utils/interceptor";
import { ScheduleType, UserType } from "@/api/mongoTypes";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { v4 as uuidv4 } from "uuid";
import Link from "next/link";
import StudentLoading from "./StudentLoading";
import Loading from "./Loading";
import ScheduleInfo from "./ScheduleInfo";
import LevelsInfo from "./LevelsInfo";
import { getPlanString } from "@/utils/common";
import {
  format,
  addWeeks,
  startOfWeek,
  endOfWeek,
  isSameDay,
  addDays,
  startOfDay,
  parseISO,
  isEqual,
  isAfter,
  isBefore,
} from "date-fns";
import {
  getDateAsPerUTC,
  getEndOfDayUTC,
  getWeekOptions,
} from "@/utils/dateTime";

const tabs = [
  {
    id: 1,
    name: "Group",
    description: "Group classes run Monday to Thursday, 10:00 AM - 1:00 PM",
    value: IN_PERSON_TYPE.GROUP,
  },
  {
    id: 2,
    name: "Private",
    description: "Customized one-on-one in-person sessions available on demand",
    value: IN_PERSON_TYPE.PRIVATE,
  },
];

const getClassSubTypeValue = (id) => {
  return tabs.find((f) => +f.id === +id).value;
};

type CreateScheduleBodyProps = React.FC<{
  scheduleData: Maybe<ScheduleType>;
  teachersList: UserType[];
}>;
const CreateScheduleBody: CreateScheduleBodyProps = ({
  scheduleData,
  teachersList,
}) => {
  const weekOptions = useMemo(() => {
    return getWeekOptions({
      showStartEndDate: false,
    });
  }, []);
  const [classType, setClassType] = useState<CLASSES_TYPE>(
    CLASSES_TYPE.IN_PERSON
  );
  const [classSubType, setClassSubType] = useState(tabs[0].id);

  const { showSnackbar } = useSnackbar();
  const [startDate, setStartDate] = useState(weekOptions[0]?.startDate);
  const [endDate, setEndDate] = useState(weekOptions[0]?.endDate);
  const [isOpen, setIsOpen] = useState(false);
  const { dbUser } = useUserContext();
  const [isUpdating, setIsUpdating] = useState(false);
  const [data, setData] = useState(scheduleData);
  const [isLoading, setIsLoading] = useState(true);

  const [students, setStudents] = useState([]);
  const [isStudentLoading, setIsStudentLoading] = useState(false);
  const [scheduledStudents, setScheduledStudents] = useState([]);
  const [availableStudents, setAvailableStudents] = useState([]);
  const router = useRouter();

  const handleReset = () => {
    setStudents([]);
    setData(null);
    setScheduledStudents([]);
  };

  console.log("weekOptions", weekOptions);

  const scheduledCount = (level) => {
    const count =
      scheduledStudents.filter((f) => f.level === level)?.length ?? 0;
    return count;
  };

  const isStudentScheduled = (student, scheduledList) => {
    console.log({
      student,
      scheduledList,
      isStudentScheduled: true,
    });
    try {
      if (scheduledList.length === 0) {
        return false;
      }

      const getUserId = (user) => {
        if (!user) {
          return null;
        }
        if (typeof user === "string") {
          return user;
        }
        if (user?._id && typeof user?._id === "string") {
          return user?._id;
        }
      };

      const listWithSameUserIdsAndLevel = scheduledList.some((f) => {
        const sameUserId = getUserId(f.userId) === getUserId(student.user);
        const sameLevel = f.level === student.user.level;
        const samePlan =
          getPlanString(f.selectedPlan) === getPlanString(student.currentPlan);
        const sameTransactionId = f.transaction === student._id;
        const condition =
          sameUserId && sameLevel && sameTransactionId && samePlan;

        // console.log({
        //   schedulelItsItem: f,
        //   student: student,
        //   sameUserId,
        //   sameLevel,
        //   samePlan,
        //   sameTransactionId,
        //   condition,
        // });

        return condition;
      });
      if (listWithSameUserIdsAndLevel) {
        return true;
      }
      return false;
    } catch (error) {
      console.error("Something went wrong in isStudentScheduled ", error);
      return false;
    }
  };

  useEffect(() => {
    const getAvailableStudents = () => {
      if (scheduledStudents.length > 0) {
        console.log({
          scheduledStudents,
          students,
        });
        setAvailableStudents([]);
        const list = [];
        students.map((m) => {
          if (!isStudentScheduled(m, scheduledStudents)) {
            console.log("not scheduled", m);
            list.push(m);
          }
        });
        return list;
      } else {
        return students;
      }
    };
    const listOfAvailableStudents = getAvailableStudents();
    console.log("listOfAvailableStudents", listOfAvailableStudents);
    setAvailableStudents(listOfAvailableStudents);
  }, [scheduledStudents, students]);

  const fetchStudents = async (startDate, endDate) => {
    try {
      setIsStudentLoading(true);
      const { data: respData } = await axiosInstance.get(
        `schedule/get-students-classess`,
        {
          params: {
            startDate: getDateAsPerUTC(startDate, true),
            endDate: getEndOfDayUTC(endDate, true),
          },
        }
      );
      if (respData.success) {
        const studentlist = respData.data;
        const studentsWithUniqueId = studentlist.map((m) => ({
          uniqueId: uuidv4(),
          ...m,
        }));
        setStudents(studentsWithUniqueId);
      } else {
        setStudents([]);
      }
      setIsStudentLoading(false);
    } catch (error) {
      setIsStudentLoading(false);
      console.error("Something wenyt wrong in fetchStudents due to ", error);
    }
  };

  const handleData = (data) => {
    setData(data);
    setStartDate(data.startDate);
    setEndDate(data.endDate);
    setScheduledStudents(data.students);
  };

  const handleDateChange = ({ startDate, endDate, fetchSchedule = false }) => {
    if (startDate && endDate) {
      const fetchScheduledData = async () => {
        try {
          setIsLoading(true);
          const { data } = await axiosInstance.post(`schedule/get-by-date`, {
            startDate: getDateAsPerUTC(startDate, true),
            endDate: getEndOfDayUTC(endDate, true),
          });
          console.log("fetchScheduledDatadata", data);
          if (data.success) {
            setData(data.data);
            handleData(data.data);
          } else {
            setData(null);
            setScheduledStudents([]);
          }
          setIsLoading(false);
        } catch (error) {
          setIsLoading(false);
          console.error(
            "Something went wrong in fetchScheduledData due to ",
            error
          );
        }
      };

      if (fetchSchedule) {
        fetchScheduledData();
      }
      fetchStudents(startDate, endDate);
    }
  };

  useEffect(() => {
    if (scheduleData?._id) {
      handleData(scheduleData);
      setIsLoading(false);
      handleDateChange({
        startDate: data.startDate,
        endDate: data.endDate,
        fetchSchedule: false,
      });
    } else {
      const weekdata = getWeekOptions({
        showStartEndDate: true,
      })[0];
      handleDateChange({
        startDate: weekdata.startDate,
        endDate: weekdata.endDate,
        fetchSchedule: true,
      });
    }
  }, [scheduleData]);

  const handleUpdateSchedule = async (studentList, studentInfo) => {
    // setStudents((prev) => {
    //   const list = [];
    //   prev.map((m) => {
    //     if (JSON.stringify(m) !== JSON.stringify(studentInfo)) {
    //       list.push(m);
    //     }
    //   });
    //   return list;
    // });

    const handleError = () => {
      // setStudents((prev) => [...prev, studentInfo]);
      showSnackbar("Something went wrong while updating club", {
        type: "error",
      });
    };

    try {
      setIsUpdating(true);
      const payload = {
        id: data._id,
        students: studentList,
        updateStudents: true,
      };
      const { data: respData } = await axiosInstance.put(
        `schedule/update`,
        payload
      );
      console.log("respData", respData);
      if (respData.success && respData?.data?._id) {
        setScheduledStudents(respData.data.students);
        showSnackbar("Updated club successfully", {
          type: "success",
        });
      } else {
        handleError();
      }
      setIsUpdating(false);
    } catch (error) {
      handleError();
      setIsUpdating(false);
      console.error(
        "Something went wrong in handleUpdateSchedule due to ",
        error
      );
    }
  };

  console.log("index", startDate, endDate);

  return (
    <>
      <Container
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          padding: { xs: "1rem", sm: "2rem", md: "4rem" },
        }}
      >
        <Header />
        <Box px={10} width="100%">
          <ClassTypeHeader />
          <PricingTab
            tabs={tabs}
            active={classSubType}
            onClick={(tabData) => {
              if (tabData.id === 1) {
                setClassSubType(tabData.id);
              }
            }}
            contStyle={{
              justifyContent: "start",
              p: 1,
              mt: 5,
            }}
            tabStyle={{
              fontSize: 10,
              width: "fit-content",
              height: 30,
              padding: "0px 2rem",
            }}
          />
          <Typography fontSize="0.95rem" color="#6D6D6D" my={2}>
            Drag and Drop to the respective proficiency of classes
          </Typography>

          {isLoading ? (
            <Loading />
          ) : (
            <Box
              display="flex"
              flexDirection="column"
              width="100%"
              aria-disabled={isUpdating}
              sx={{
                border: "1px solid #BFBFBF",
                borderRadius: 2,
                p: 2,
              }}
            >
              <Box
                sx={{
                  borderBottom: "1px solid #BFBFBF",
                }}
              >
                <WeekSelector
                  endDate={endDate}
                  setEndDate={setEndDate}
                  setStartDate={setStartDate}
                  weekOptions={weekOptions}
                  startDate={startDate}
                  onChange={({ startDate, endDate }) => {
                    console.log({
                      weekchanger: true,
                      startDate,
                      endDate,
                    });
                    handleDateChange({
                      startDate,
                      endDate,
                      fetchSchedule: true,
                    });
                    // setStartDate(startDate);
                    // setEndDate(endDate);
                  }}
                />
              </Box>
              <DragDropContext
                onDragEnd={(result) => {
                  try {
                    const { destination, source, draggableId } = result;
                    if (!destination) return;
                    const newLevel = destination.droppableId;
                    const student = students.find(
                      (s) => s.uniqueId === draggableId
                    );
                    if (!student) return;

                    if (student.user.level !== newLevel) {
                      showSnackbar("Level should be same", {
                        type: "error",
                      });
                      return;
                    }

                    console.log({
                      student,
                      newLevel,
                      destination,
                      result,
                      draggableId,
                    });

                    const selectedPlanDetail = student.currentPlan;

                    const studentDetails = {
                      transaction: student._id,
                      userId: student.user._id,
                      level: student.user.level,
                      selectedPlan: {
                        planId: selectedPlanDetail.planId,
                        startDate: selectedPlanDetail.startDate,
                        endDate: selectedPlanDetail.endDate,
                        planFor: selectedPlanDetail.planFor,
                        emailId: selectedPlanDetail.emailId,
                        isDateEnabled: selectedPlanDetail.isDateEnabled,
                      },
                      scheduledAt: new Date(),
                    };

                    handleUpdateSchedule(
                      [...scheduledStudents, studentDetails],
                      student
                    );

                    // Proceed to update backend
                    // updateStudentLevel(draggableId, newLevel);
                  } catch (error) {
                    console.error(
                      "Something went wrong in onDragEnd due to ",
                      error
                    );
                  }
                }}
              >
                <Box
                  display="flex"
                  sx={{
                    flexDirection: {
                      xs: "column",
                      sm: "row",
                    },
                    p: 1,
                  }}
                >
                  <Box
                    sx={{
                      width: {
                        xs: "100%",
                        sm: "40%",
                      },
                      p: 3,
                      borderRight: {
                        xs: "none",
                        sm: "1px solid #BFBFBF",
                      },
                    }}
                  >
                    <LevelsInfo />

                    <Box sx={{ overflowY: "auto" }}>
                      {isStudentLoading ? (
                        <StudentLoading />
                      ) : availableStudents.length === 0 &&
                        scheduledStudents.length > 0 ? (
                        <>
                          <Typography
                            textAlign="center"
                            mt={2}
                            color="#727272"
                            fontSize="0.8rem"
                          >
                            All students have been assigned to their respective
                            classes.
                          </Typography>
                        </>
                      ) : (
                        <Droppable droppableId="students" isDropDisabled={true}>
                          {(provided) => {
                            return (
                              <Box
                                ref={provided.innerRef}
                                {...provided.droppableProps}
                              >
                                {availableStudents.map((m, i) => {
                                  const { isWeek4, formattedText, weekNumber } =
                                    getStudentWeekName({
                                      data: m,
                                      selectedMonday: startDate,
                                      selectedThursday: endDate,
                                    });
                                  return (
                                    <Draggable
                                      draggableId={m.uniqueId}
                                      index={i}
                                      key={m.uniqueId}
                                    >
                                      {(provided) => (
                                        <Box
                                          ref={provided.innerRef}
                                          {...provided.draggableProps}
                                          {...provided.dragHandleProps}
                                          display="flex"
                                          justifyContent="space-between"
                                          p={1}
                                          mt={2}
                                          onClick={() => {
                                            if (m?.user?.clerkId) {
                                              router.push(
                                                `/create/student-dashboard/student/${m.user.clerkId}`
                                              );
                                            }
                                          }}
                                          sx={{
                                            background: getLevelColor(
                                              m?.user?.level
                                            ),
                                            borderRadius: 2,
                                            cursor: "pointer",
                                          }}
                                        >
                                          <Typography fontWeight={700}>
                                            {m.user.firstName} {m.user.lastName}
                                          </Typography>
                                          <Typography fontSize={13}>
                                            {isWeek4 && (
                                              <b>Week {weekNumber} </b>
                                            )}
                                            {isWeek4 && "of "}
                                            {formattedText}
                                          </Typography>
                                        </Box>
                                      )}
                                    </Draggable>
                                  );
                                })}
                                {provided.placeholder}
                              </Box>
                            );
                          }}
                        </Droppable>
                      )}
                    </Box>
                    {!(
                      availableStudents.length === 0 &&
                      scheduledStudents.length > 0
                    ) && (
                      <Typography
                        textAlign="center"
                        mt={2}
                        color="#727272"
                        fontSize="0.8rem"
                      >
                        End of list
                      </Typography>
                    )}
                  </Box>
                  <Box
                    sx={{
                      width: {
                        xs: "100%",
                        sm: "60%",
                      },
                      p: 3,
                    }}
                  >
                    <ScheduleInfo />
                    {data?._id ? (
                      <>
                        {data.teachers.length < 3 && (
                          <Box
                            display="flex"
                            flexDirection="row"
                            justifyContent="end"
                          >
                            <Button
                              sx={{ textWrap: "nowrap" }}
                              onClick={() => {
                                setIsOpen(true);
                              }}
                            >
                              <Typography
                                color="#000"
                                fontWeight={600}
                                fontSize="0.85rem"
                                textTransform="lowercase"
                              >
                                Schedule a Class
                              </Typography>
                            </Button>
                          </Box>
                        )}
                        <Box
                          display="flex"
                          flexDirection="row"
                          gap={2}
                          alignItems="center"
                          mt={10}
                          justifyContent="center"
                        >
                          {LEVELS.map((m, i) => {
                            const isLevelExists = data.teachers.some(
                              (s) => s.level === m.value
                            );
                            if (isLevelExists) {
                              return (
                                <Droppable droppableId={m.value} key={m.value}>
                                  {(provided) => (
                                    <Box
                                      ref={provided.innerRef}
                                      {...provided.droppableProps}
                                    >
                                      <Link
                                        href={`/create/schedule/students/${m.value}/${data._id}`}
                                      >
                                        <Box
                                          sx={{
                                            background: m.color,
                                            borderRadius: 2,
                                            display: "flex",
                                            flexDirection: "column",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            height: 100,
                                            p: 3,
                                            cursor: "pointer",
                                            color: "#000",
                                          }}
                                        >
                                          <Typography
                                            fontSize={15}
                                            fontWeight={700}
                                            mb={2}
                                          >
                                            {m.name}
                                          </Typography>
                                          <Typography
                                            fontWeight={600}
                                            fontSize={13}
                                          >
                                            No of Students Enrolled:
                                          </Typography>
                                          <Typography
                                            fontWeight={800}
                                            fontSize={14}
                                          >
                                            {scheduledCount(m.value)}
                                          </Typography>
                                          {provided.placeholder}
                                        </Box>
                                      </Link>
                                    </Box>
                                  )}
                                </Droppable>
                              );
                            }
                            return null;
                          })}
                        </Box>
                      </>
                    ) : (
                      <Box
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                        mt={10}
                        justifyContent="center"
                      >
                        <Typography fontWeight={800} mb={2}>
                          No classess scheduled yet
                        </Typography>
                        <Button
                          sx={{ textWrap: "nowrap" }}
                          onClick={() => {
                            setIsOpen(true);
                          }}
                        >
                          <Typography
                            color="#000"
                            fontWeight={600}
                            fontSize="0.85rem"
                            textTransform="lowercase"
                          >
                            Schedule a Class
                          </Typography>
                        </Button>
                      </Box>
                    )}
                  </Box>
                </Box>
              </DragDropContext>
            </Box>
          )}
        </Box>
      </Container>

      {isOpen && (
        <ScheduleModal
          endDate={endDate}
          classType={classType}
          classSubType={getClassSubTypeValue(classSubType)}
          startDate={startDate}
          open={isOpen}
          setOpen={setIsOpen}
          data={data}
          showAll={data?.teachers?.length < 3}
          teachersList={teachersList}
          onUpdate={(d) => {
            setData(d);
          }}
        />
      )}
    </>
  );
};

export default CreateScheduleBody;
