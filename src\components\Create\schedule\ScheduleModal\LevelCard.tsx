import { Box, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import TeacherSelect from "./TeacherSelect";
import LocationInput from "./LocationInput";
import { ScheduleTeacherType } from "@/types";
import { UserType } from "@/api/mongoTypes";
import { LEVEL_TYPES } from "@/constant/Enums";

type LevelCardProps = React.FC<{
  name: string;
  index: number;
  teachers: ScheduleTeacherType[];
  setTeachers: React.Dispatch<React.SetStateAction<ScheduleTeacherType[]>>;
  teachersList: UserType[];
  data: ScheduleTeacherType;
}>;
const LevelCard: LevelCardProps = ({
  name,
  teachers,
  setTeachers,
  index,
  teachersList,
  data,
}) => {
  return (
    <Box
      mb={4}
      sx={{
        borderRadius: 2,
        border: "1px solid #DDDDDD",
        p: 3,
      }}
    >
      <Typography mb={2} fontSize={17} fontWeight={600}>
        {name}
      </Typography>
      <Box
        display="flex"
        flexDirection="row"
        width="100%"
        justifyContent="space-between"
        gap={4}
      >
        <Box
          display="flex"
          flexDirection="column"
          sx={{
            width: {
              xs: "100%",
              sm: "50%",
            },
          }}
        >
          <Label>Select teacher</Label>
          <TeacherSelect
            teachers={teachers}
            teachersList={teachersList}
            selectedTeacher={data.teacherId}
            onSelect={(val) => {
              setTeachers((prev) => {
                return prev.map((m, i) => {
                  if (i === index) {
                    return {
                      ...m,
                      teacherId: val,
                    };
                  }
                  return m;
                });
              });
            }}
          />
        </Box>
        <Box
          display="flex"
          flexDirection="column"
          sx={{
            width: {
              xs: "100%",
              sm: "50%",
            },
          }}
        >
          <Label>Add location</Label>
          <LocationInput
            value={data.location}
            onChange={(val) => {
              setTeachers((prev) => {
                return prev.map((m, i) => {
                  if (i === index) {
                    return {
                      ...m,
                      location: val,
                    };
                  }
                  return m;
                });
              });
            }}
          />
        </Box>
      </Box>
    </Box>
  );
};

type LabelProps = React.FC<{
  children: React.ReactNode;
}>;
const Label: LabelProps = ({ children }) => {
  return (
    <Typography color="#6D6D6D" fontSize={14} mb={1}>
      {children}
    </Typography>
  );
};

export default LevelCard;
