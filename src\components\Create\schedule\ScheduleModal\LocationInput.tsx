import { Box, TextField } from "@mui/material";
import React from "react";
import FmdGoodOutlinedIcon from "@mui/icons-material/FmdGoodOutlined";

type LocationInputProps = React.FC<{
  value: string;
  onChange: (val: string) => void;
}>;
const LocationInput: LocationInputProps = ({ value, onChange }) => {
  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "flex-end",
        border: "1px solid #CCCCCC",
        borderRadius: 2,
        overflow: "hidden",
        height: 45,
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        height="100%"
        width={50}
        sx={{ background: "#F5F5F5" }}
      >
        <FmdGoodOutlinedIcon />
      </Box>
      <TextField
        id="input-with-sx"
        placeholder="Enter offline location"
        variant="standard"
        value={value}
        onChange={(e) => {
          onChange(e.target.value);
        }}
        sx={{
          m: 0,
          width: "100%",
          height: 45,
          "& .MuiInputBase-input": {
            height: 45,
            padding: 0,
            lineHeight: "45px",
            px: 2,
          },
          //   "& .MuiInputBase-root::before": {
          //     borderBottom: "none", // Remove the default underline
          //   },
          //   "& .MuiInputBase-root::after": {
          //     borderBottom: "none", // Remove the active/focused underline
          //   },

          "& .MuiInputBase-root": {
            "&::before": {
              borderBottom: "none !important", // Force remove default underline
            },
            "&::after": {
              borderBottom: "none !important", // Force remove active/focused underline
            },
            "&:hover:not(.Mui-disabled)::before": {
              borderBottom: "none !important", // Remove hover underline
            },
          },
        }}
      />
    </Box>
  );
};

export default LocationInput;
