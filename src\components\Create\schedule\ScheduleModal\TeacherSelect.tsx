import {
  Box,
  Select,
  MenuItem,
  InputAdornment,
  TextField,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import SearchIcon from "@mui/icons-material/Search";
import PersonIcon from "@mui/icons-material/Person";
import { UserType } from "@/api/mongoTypes";
import { ScheduleTeacherType } from "@/types";
import Link from "next/link";
import { useSnackbar } from "@/hooks/useSnackbar";

type TeacherSelectProps = React.FC<{
  selectedTeacher: string;
  onSelect: (val: string) => void;
  teachersList: UserType[];
  teachers: ScheduleTeacherType[];
}>;

const TeacherSelect: TeacherSelectProps = ({
  selectedTeacher,
  teachersList,
  onSelect,
  teachers,
}) => {
  const { showSnackbar } = useSnackbar();
  const [searchTerm, setSearchTerm] = useState("");

  const filteredTeachersList = (() => {
    const selectedTeachersId = teachers
      .filter((f) => f.teacherId)
      .map((m) => m.teacherId);

    return teachersList.filter((f) => !selectedTeachersId.includes(f._id));
    // return teachersList;
  })();

  const filteredTeachers = filteredTeachersList.filter((teacher) => {
    const first = teacher?.firstName ?? "";
    const last = teacher?.lastName ?? "";
    return (
      first.toLowerCase().includes(searchTerm.toLowerCase()) ||
      last?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "flex-end",
        border: "1px solid #CCCCCC",
        borderRadius: 2,
        overflow: "hidden",
        height: 45,
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        height="100%"
        width={50}
        sx={{ background: "#F5F5F5" }}
      >
        <PersonIcon />
      </Box>
      <Select
        value={selectedTeacher}
        onChange={(e) => onSelect(e.target.value)}
        displayEmpty
        variant="standard"
        disableUnderline
        sx={{
          m: 0,
          width: "100%",
          height: 45,
          "& .MuiSelect-select": {
            height: 45,
            padding: 0,
            lineHeight: "45px",
            px: 2,
          },
          "& .MuiInputBase-root": {
            "&::before": {
              borderBottom: "none !important",
            },
            "&::after": {
              borderBottom: "none !important",
            },
            "&:hover:not(.Mui-disabled)::before": {
              borderBottom: "none !important",
            },
          },
        }}
        MenuProps={{
          PaperProps: {
            sx: {
              maxHeight: 300,
            },
          },
        }}
        renderValue={(selected) => {
          if (!selected) {
            return <em>Select a teacher</em>;
          }
          const selectedTeacherInfo = teachersList.find(
            (teacher) => teacher?._id === selected
          );
          return `${selectedTeacherInfo.firstName} ${
            selectedTeacherInfo?.lastName ?? ""
          }`;
        }}
      >
        {/* Search input in the dropdown */}
        <Box
          sx={{
            p: 1,
            position: "sticky",
            top: 0,
            background: "white",
            zIndex: 1,
          }}
        >
          <TextField
            size="small"
            autoFocus
            placeholder="Search teachers..."
            fullWidth
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={(e) => e.stopPropagation()}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </Box>
        {/* Teacher options */}
        {filteredTeachers.length > 0 ? (
          filteredTeachers.map((teacher) => (
            <MenuItem key={teacher._id} value={teacher._id}>
              {teacher.firstName} {teacher.lastName}
            </MenuItem>
          ))
        ) : teachersList.length === 0 ? (
          <Box sx={{ textAlign: "center" }}>
            <Link
              href="/create/roles-management/"
              onClick={() => {
                showSnackbar(
                  "Assign teacher role to the existing user in the role management",
                  {
                    type: "warning",
                  },
                  60000
                );
              }}
            >
              Create teacher
            </Link>
          </Box>
        ) : (
          <MenuItem disabled>No teachers found</MenuItem>
        )}
      </Select>
    </Box>
  );
};

export default TeacherSelect;
