import Close from "@/components/CheckoutModal/Close";
import { <PERSON>, Button, Modal, Typography } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import LevelCard from "./LevelCard";
import CustomButton from "@/components/CustomButton";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import {
  CLASSES_TYPE,
  IN_PERSON_TYPE,
  LEVEL_TYPES,
  ONLINE_CLASSES_TYPE,
} from "@/constant/Enums";
import { ScheduleTeacherType, ValueOf } from "@/types";
import { ScheduleType, UserType } from "@/api/mongoTypes";
import { getLevelName } from "@/utils/classes";
import { useRouter } from "next/router";
import { formatDate } from "@/utils/dateTime";

type ScheduleModalProps = React.FC<{
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  startDate: Date;
  endDate: Date;
  classType: CLASSES_TYPE;
  classSubType:
    | ValueOf<typeof IN_PERSON_TYPE>
    | ValueOf<typeof ONLINE_CLASSES_TYPE>;
  data: ScheduleType;
  setData?: React.Dispatch<any>;
  teachersList: UserType[];
  level?: LEVEL_TYPES;
  showAll?: boolean;
  onUpdate?: (data) => void;
}>;

const DEFAULT_TEACHERS: ScheduleTeacherType[] = [
  {
    location: "",
    level: LEVEL_TYPES.BEGINNER,
    teacherId: "",
  },
  {
    location: "",
    level: LEVEL_TYPES.INTERMEDIATE,
    teacherId: "",
  },
  {
    location: "",
    level: LEVEL_TYPES.ADVANCED,
    teacherId: "",
  },
];

const getTeacherId = (data) => {
  if (typeof data === "string") {
    return data;
  }
  if ("_id" in data) {
    return data._id;
  }
  return data;
};

const ScheduleModal: ScheduleModalProps = ({
  open,
  setOpen,
  startDate,
  endDate,
  classSubType,
  classType,
  data,
  teachersList,
  setData,
  level,
  showAll = false,
  onUpdate = (data) => {},
}) => {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [teachers, setTeachers] =
    useState<ScheduleTeacherType[]>(DEFAULT_TEACHERS);

  console.log({
    teachers,
    data,
    teachersList,
    endDate,
    startDate,
  });

  useEffect(() => {
    if (data) {
      if (!showAll) {
        setTeachers(
          data.teachers.map((m) => ({
            level: m.level,
            location: m.location,
            teacherId: String(getTeacherId(m.teacherId)),
          }))
        );
      } else {
        const teacherLevels = DEFAULT_TEACHERS.map((m) => {
          const checkteacherWithSameLevel = data.teachers.find(
            (f) => m.level === f.level
          );
          if (checkteacherWithSameLevel) {
            return checkteacherWithSameLevel;
          } else {
            return m;
          }
        });
        setTeachers(teacherLevels as any);
      }
    }
  }, [data, showAll]);

  const validate = (level) => {
    const targetTeachers = level
      ? teachers.filter((t) => t.level === level)
      : teachers;

    // if (targetTeachers.some((t) => !t.teacherId)) {
    //   return "teacher cannot be empty";
    // }

    // if (targetTeachers.some((t) => t.location.length < 5)) {
    //   return "Location needs to be at least 5 characters";
    // }

    if (level) {
      const otherLevelTeachers = teachers.filter((t) => t.level !== level);
      for (const teacher of targetTeachers) {
        if (otherLevelTeachers.some((t) => t.teacherId === teacher.teacherId)) {
          return `Teacher with ID ${teacher.teacherId} is already assigned to another level`;
        }
      }
    } else {
      const filledTeacherIds = targetTeachers
        .map((t) => t.teacherId)
        .filter(Boolean);
      if (new Set(filledTeacherIds).size !== filledTeacherIds.length) {
        return "Duplicate teacher are not allowed";
      }
    }
    // return "";

    let oneFilled = false;

    for (const teacher of teachers) {
      const { location, teacherId } = teacher;

      const hasLocation = location && location.trim().length > 0;
      const hasTeacherId = teacherId && teacherId.trim().length > 0;

      if (hasLocation || hasTeacherId) {
        oneFilled = true;

        if (!hasLocation) return "Location is required if teacher is filled.";
        if (!hasTeacherId) return "Teacher cannot be empty.";
        if (location.trim().length < 5)
          return "Location needs to be at least 5 characters.";
      }
    }

    if (!oneFilled) {
      return "At least one teacher entry must have location and teacher.";
    }
  };

  const handleClose = () => setOpen(false);

  const handleCreateSchedule = async () => {
    try {
      const error = validate("");
      if (error) {
        showSnackbar(error, {
          type: "error",
        });
        return;
      }

      setIsCreating(true);
      const payload = {
        type: classType,
        subType: classSubType,
        startDate,
        endDate,
        // language: "",
        teachers: teachers.filter((f) => f.teacherId),
      };
      const { data: respData } = await axiosInstance.post(
        `schedule/create`,
        payload
      );
      if (respData.success && respData?.data?._id) {
        showSnackbar("Class scheduled successfully", {
          type: "success",
        });
        router.push(`/create/schedule/update/${respData?.data?._id}`);
      } else {
        showSnackbar("Something went wrong while updating club", {
          type: "error",
        });
      }
      setIsCreating(false);
    } catch (error) {
      setIsCreating(false);
      console.error(
        "Something went wrong in handleCreateSchedule due to ",
        error
      );
    }
  };

  const handleUpdateSchedule = async () => {
    const handleError = () => {
      showSnackbar("Something went wrong while updating schedule", {
        type: "error",
      });
    };
    const error = validate(level);
    if (error) {
      showSnackbar(error, {
        type: "error",
      });
      return;
    }
    try {
      setIsUpdating(true);
      const payload = {
        id: data._id,
        teachers: teachers.filter((f) => f.teacherId),
        updateStudents: false,
      };
      const { data: respData } = await axiosInstance.put(
        `schedule/update`,
        payload
      );
      const successCondition = respData.success && !!respData?.data?._id;

      console.log({
        respData,
        successCondition,
      });

      if (successCondition) {
        if (setData) {
          setData((prev) => {
            return {
              ...prev,
              teachers: teachers
                .map((m) => {
                  return {
                    location: m.location,
                    level: m.level,
                    teacherId: teachersList.find((f) => f._id === m.teacherId),
                  };
                })
                .filter((f) => f.teacherId),
            };
          });
        }
        if (onUpdate) {
          onUpdate(respData.data);
        }
        showSnackbar("Updated schedule successfully", {
          type: "success",
        });
        handleClose();
      } else {
        handleError();
      }
      setIsUpdating(false);
    } catch (error) {
      handleError();
      setIsUpdating(false);
      console.error(
        "Something went wrong in handleUpdateSchedule due to ",
        error
      );
    }
  };

  useEffect(() => {
    if (level) {
      setTeachers((prev) => {
        const includesLevel = prev.find((f) => f.level === level);
        if (!includesLevel) {
          return [
            ...prev,
            {
              location: "",
              level,
              teacherId: "",
            },
          ];
        } else {
          return prev;
        }
      });
    }
  }, [level]);

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: {
            xs: "100%",
            sm: "100%",
            md: 900,
          },
          bgcolor: "background.paper",
          boxShadow: 24,
          borderRadius: 2,
        }}
      >
        <Box
          sx={{
            p: 6,
            width: "100%",
            maxHeight: "90vh",
            position: "relative",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            width="100%"
          >
            <Typography fontSize={18} fontWeight={800}>
              Schedule Class
            </Typography>
            <Box sx={{ cursor: "pointer" }}>
              <Close onClick={handleClose} />
            </Box>
          </Box>

          <Typography fontSize="0.9rem" mt={5}>
            These group classes run 10:00 - 1:00pm Monday to thursday
          </Typography>

          <Typography fontWeight={700} fontSize="0.9rem" mb={5}>
            {formatDate({
              returnWeekday: true,
              date: startDate,
            })}
            &nbsp;
            {formatDate({
              returnDay: false,
              date: startDate,
            })}
            &nbsp; - &nbsp;
            {formatDate({
              returnWeekday: true,
              date: endDate,
            })}
            &nbsp;
            {formatDate({
              returnDay: false,
              date: endDate,
            })}
          </Typography>

          {teachers.map((m, i) => {
            if (m.level === level || !level) {
              return (
                <LevelCard
                  index={i}
                  data={m}
                  teachers={teachers}
                  setTeachers={setTeachers}
                  name={getLevelName(m.level)}
                  key={i}
                  teachersList={teachersList}
                />
              );
            }
          })}

          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="center"
            gap={2}
          >
            <CustomButton
              colortype="secondary"
              disabled={isCreating || isUpdating}
              sx={{
                width: "40%",
              }}
              text="Cancel"
              onClick={handleClose}
            />
            <CustomButton
              disabled={isCreating || isUpdating}
              sx={{
                width: "40%",
              }}
              colortype="primary"
              text={data ? "Update" : "Schedule Classess"}
              onClick={() => {
                if (data) {
                  handleUpdateSchedule();
                } else {
                  handleCreateSchedule();
                }
              }}
            />
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default ScheduleModal;
