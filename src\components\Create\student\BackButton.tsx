import { Box, SxProps, Theme } from "@mui/material";
import { useRouter } from "next/router";
import React from "react";

type BackButtonProps = React.FC<{
  sx?: SxProps<Theme>;
}>;
const BackButton: BackButtonProps = ({ sx = {} }) => {
  const router = useRouter();
  return (
    <Box
      onClick={() => {
        router.back();
      }}
      sx={{
        position: "absolute",
        left: 0,
        border: "1px solid silver",
        borderRadius: "50%",
        height: 40,
        width: 40,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        cursor: "pointer",
        ...sx,
      }}
    >
      <svg
        width="9"
        height="14"
        viewBox="0 0 9 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.75015 13.3921C7.86216 13.2803 7.95103 13.1475 8.01167 13.0014C8.0723 12.8552 8.10352 12.6985 8.10352 12.5402C8.10352 12.382 8.0723 12.2253 8.01167 12.0791C7.95103 11.9329 7.86216 11.8001 7.75015 11.6884L3.06181 7.00002L7.75015 2.31169C7.97608 2.08576 8.103 1.77933 8.103 1.45982C8.103 1.1403 7.97608 0.833872 7.75015 0.607941C7.52421 0.38201 7.21779 0.255085 6.89827 0.255085C6.57876 0.255085 6.27233 0.38201 6.0464 0.607941L0.500145 6.15419C0.388128 6.26598 0.299259 6.39876 0.238623 6.54494C0.177987 6.69111 0.146776 6.84781 0.146776 7.00607C0.146776 7.16432 0.177987 7.32102 0.238623 7.4672C0.299259 7.61337 0.388128 7.74615 0.500145 7.85794L6.0464 13.4042C6.50556 13.8634 7.2789 13.8634 7.75015 13.3921Z"
          fill="black"
        />
      </svg>
    </Box>
  );
};

export default BackButton;
