import {
  Card,
  CardHeader,
  Box,
  Typography,
  SxProps,
  Theme,
} from "@mui/material";
import React from "react";

type InfoCardProps = React.FC<{
  icon: React.ReactNode;
  headerText: string;
  children: React.ReactNode;
  headerRight?: React.ReactNode;
  sx?: SxProps<Theme>;
}>;

const InfoCard: InfoCardProps = ({
  icon,
  headerText,
  children,
  headerRight,
  sx = {},
}) => {
  return (
    <Card
      sx={{
        display: "flex",
        flexDirection: "column",
        p: 2,
        border: "1px solid #EDEDED",
        boxShadow: "none",
        borderRadius: 2,
        ...sx,
      }}
    >
      <Box
        sx={{
          borderBottom: "1px solid #F5F5F5",
        }}
        width="100%"
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        pb={2}
      >
        <Box gap={2} display="flex" flexDirection="row" alignItems="center">
          {icon}
          <Typography fontWeight={700} fontSize={14}>
            {headerText}
          </Typography>
        </Box>
        <Box>{headerRight}</Box>
      </Box>
      <Box>{children}</Box>
    </Card>
  );
};

export default InfoCard;
