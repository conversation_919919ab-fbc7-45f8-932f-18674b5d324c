import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import React from "react";

const UpcomingFilter = [
  {
    id: 1,
    name: "Next 7 days",
  },
  {
    id: 2,
    name: "Next 14 days",
  },
  {
    id: 3,
    name: "Whole month",
  },
  {
    id: 4,
    name: "All",
  },
];

const Filter = ({ setSelectedFilter, selectedFilter }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      mb={2}
    >
      <FormControl sx={{ m: 1, minWidth: 120 }} size="small">
        <InputLabel id="demo-select-small-label">Filter</InputLabel>
        <Select
          labelId="demo-select-small-label"
          id="demo-select-small"
          value={selectedFilter}
          label="Filter"
          sx={{
            fontSize: 12,
          }}
          onChange={(e) => {
            setSelectedFilter(e.target.value);
          }}
        >
          {UpcomingFilter.map((m) => (
            <MenuItem sx={{ fontSize: 12 }} value={m.id} key={m.id}>
              {m.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <Typography fontSize={13} color="#989898">
        Sorted by soonest first
      </Typography>
    </Box>
  );
};

export default Filter;
