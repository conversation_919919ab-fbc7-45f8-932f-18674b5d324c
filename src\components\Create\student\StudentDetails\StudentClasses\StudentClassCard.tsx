import PricingType from "@/components/classes/PricingType";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import React, { useMemo } from "react";
import { ClassesIcon } from "../Icons";
import { EventSchemaType } from "@/api/mongoTypes";
import {
  classAndEventModeAndLocation,
  getClassesImage,
  getClassesLevel,
  getClassesLocation,
  getClassStatus,
  getClassTimings,
  getClassTitleAndSubtitle,
  getClassTypeAndSubType,
  getCreatorDetails,
  getStartAndEndDateOfClasses,
  getTeachers,
} from "@/utils/classes";
import { MySingleScheduleResponse } from "@/types";
import { CLASSESS_FETCH_DURATION } from "@/constant/Enums";
import { getUserFullName } from "@/utils/format";
import { formatDate } from "@/utils/dateTime";
import { LocationIcon } from "@/components/Profile/ClassCard/ClassCardModal/Icon";

type StudentClassCardProps = React.FC<{
  isEvent: boolean;
  data: MySingleScheduleResponse;
  active: CLASSESS_FETCH_DURATION;
  cardDate: Date;
}>;
const StudentClassCard: StudentClassCardProps = ({
  isEvent,
  data,
  active,
  cardDate,
}) => {
  const level = useMemo(() => {
    return getClassesLevel({ cartData: data });
  }, [data]);

  const time = useMemo(() => {
    return getClassTimings({ data });
  }, [data]);

  const imgSrc = useMemo(() => {
    return getClassesImage({ purchaseDetails: data });
  }, [data]);

  const { title } = useMemo(() => {
    return getClassTitleAndSubtitle({ cartData: data });
  }, [data]);

  const { type, subType } = useMemo(
    () => getClassTypeAndSubType({ cartData: data }),
    [data]
  );

  const { startDate } = useMemo(() => {
    return getStartAndEndDateOfClasses({
      cartData: data,
      isDashboard: false,
      active,
    });
  }, [data, active]);

  const { isOnline, location } = useMemo(() => {
    return classAndEventModeAndLocation({
      data,
    });
  }, [data]);

  const teachersDetails = useMemo(() => {
    return getTeachers({ data });
  }, [data]);

  const formattedDate = cardDate
    ? formatDate({ date: new Date(cardDate), inText: true })
    : formatDate({ date: startDate, inText: true });

  return (
    <Box
      sx={{
        border: "1px solid #EDEDED",
        borderRadius: 2,
        display: "flex",
        flexDirection: "column",
        p: 2,
        mb: 2,
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
      >
        <Typography fontSize={12} fontWeight={700} color="#14A79C">
          {formattedDate}
        </Typography>
        <Typography fontSize={12} color="#64748B">
          {time}
        </Typography>
      </Box>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        gap={2}
        my={1}
      >
        <Box display="flex" flexDirection="row" gap={2}>
          <Image
            height={20}
            width={20}
            alt="class"
            style={{ borderRadius: 2 }}
            src={imgSrc}
          />
          <Typography fontSize={13} fontWeight={700} color="#000">
            {title}
          </Typography>
        </Box>
        <Typography fontSize={12} color="#B7B7B7">
          {level}
        </Typography>
      </Box>
      <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
        <>
          <span
            style={{
              display: "flex",
              height: 5,
              width: 5,
              borderRadius: 4,
              backgroundColor: "#F9B238",
            }}
          />
          <Typography color="#8C8C8C" sx={{ fontSize: 11 }}>
            {isOnline ? "Online" : "Offline"}
          </Typography>
        </>
        {!isOnline && (
          <>
            <LocationIcon />
            <Typography color="#8C8C8C" sx={{ fontSize: 11 }}>
              {location}
            </Typography>
          </>
        )}
      </Box>
      {!isEvent && teachersDetails?.length > 0 && (
        <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
          <ClassesIcon size={12} color="#3C3C3C" strokeWidth={1} />
          {teachersDetails.map((m, i) => (
            <Typography key={i} color="#3C3C3C" fontSize={10}>
              {getUserFullName(m)}
            </Typography>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default StudentClassCard;
