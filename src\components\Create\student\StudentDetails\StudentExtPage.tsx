import React from "react";
import InfoCard from "./InfoCard";
import { Box, Typography } from "@mui/material";
import Link from "next/link";

type StudentExtPageProps = React.FC<{
  notionUrl: string;
}>;
const StudentExtPage: StudentExtPageProps = ({ notionUrl }) => {
  return (
    <Box
      sx={{
        pl: {
          xs: 0,
          md: 2,
        },
        width: {
          xs: "100%",
          md: "50%",
        },
      }}
    >
      <InfoCard
        icon={<LinkIcon size={18} />}
        headerText=" Link to Student’s Notion page"
      >
        <Link href={notionUrl}>
          <Box
            display="flex"
            alignItems="center"
            flexDirection="row"
            mt={2}
            gap={2}
          >
            <LinkIcon color="#009BC1" />
            <Typography color="#009BC1" fontSize={14}>
              Visit Notion page
            </Typography>
          </Box>
        </Link>
      </InfoCard>
    </Box>
  );
};

const LinkIcon = ({ size = 16, color = "black" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.7294 4.05064C17.1634 2.61064 19.2614 2.58064 20.4224 3.74664C21.5864 4.91464 21.5554 7.02664 20.1194 8.46664L17.6964 10.8996C17.56 11.0413 17.4847 11.2308 17.4866 11.4275C17.4886 11.6242 17.5678 11.8122 17.707 11.951C17.8463 12.0899 18.0345 12.1685 18.2312 12.1699C18.4278 12.1713 18.6172 12.0955 18.7584 11.9586L21.1824 9.52564C23.0934 7.60664 23.3334 4.54364 21.4854 2.68764C19.6354 0.830642 16.5784 1.07264 14.6654 2.99164L9.81944 7.85864C7.90844 9.77764 7.66844 12.8406 9.51644 14.6956C9.58546 14.7674 9.66808 14.8247 9.75949 14.8642C9.85089 14.9037 9.94925 14.9247 10.0488 14.9257C10.1484 14.9268 10.2472 14.9081 10.3394 14.8706C10.4317 14.8331 10.5155 14.7776 10.5861 14.7074C10.6567 14.6371 10.7126 14.5535 10.7505 14.4615C10.7884 14.3694 10.8077 14.2707 10.807 14.1711C10.8064 14.0715 10.786 13.9731 10.7469 13.8815C10.7078 13.7899 10.6509 13.707 10.5794 13.6376C9.41544 12.4696 9.44744 10.3576 10.8824 8.91764L15.7294 4.05064Z"
        fill={color}
      />
      <path
        d="M14.4855 9.63685C14.3449 9.49589 14.1541 9.41654 13.955 9.41626C13.7559 9.41598 13.5649 9.49479 13.424 9.63535C13.283 9.77592 13.2036 9.96672 13.2034 10.1658C13.2031 10.3649 13.2819 10.5559 13.4225 10.6969C14.5865 11.8649 14.5555 13.9759 13.1195 15.4169L8.27246 20.2829C6.83746 21.7229 4.73946 21.7529 3.57846 20.5869C2.41446 19.4189 2.44646 17.3069 3.88146 15.8669L6.30546 13.4339C6.37499 13.3641 6.4301 13.2812 6.46763 13.1902C6.50516 13.0991 6.52438 13.0015 6.5242 12.9029C6.52401 12.8044 6.50442 12.7069 6.46655 12.6159C6.42868 12.525 6.37326 12.4424 6.30346 12.3729C6.23366 12.3033 6.15085 12.2482 6.05976 12.2107C5.96866 12.1732 5.87107 12.1539 5.77254 12.1541C5.67402 12.1543 5.5765 12.1739 5.48555 12.2118C5.3946 12.2496 5.31199 12.3051 5.24246 12.3749L2.81846 14.8079C0.907459 16.7279 0.667459 19.7899 2.51546 21.6459C4.36546 23.5039 7.42246 23.2609 9.33546 21.3419L14.1825 16.4749C16.0935 14.5569 16.3335 11.4919 14.4855 9.63685Z"
        fill={color}
      />
    </svg>
  );
};

export default StudentExtPage;
