import { Box, Typography } from "@mui/material";
import React from "react";

type NameValueProps = React.FC<{
  name: string;
  value: string;
  widthLeft: number;
}>;
const NameValue: NameValueProps = ({ name, value, widthLeft = 50 }) => {
  return (
    <Box display="flex" flexDirection="row" alignItems="center" mt={1}>
      <Typography
        fontSize={13}
        fontWeight={600}
        width={`${widthLeft}%`}
        color="#64748B"
      >
        {name}
      </Typography>
      <Typography
        fontWeight={600}
        fontSize={13}
        width={`${100 - widthLeft}%`}
        color="#000000"
      >
        {value}
      </Typography>
    </Box>
  );
};

export default NameValue;
