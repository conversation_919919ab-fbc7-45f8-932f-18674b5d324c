import { Box, SxProps, Theme } from "@mui/material";
import React from "react";

const commonHeaderStyle: SxProps<Theme> = {
  fontSize: 14,
  textAlign: "center",
  color: "#6D6D6D",
  fontWeight: 600,
};

const StudentListHeader = () => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      sx={{
        width: "100%",
        background: "#F2F2F2",
        p: 1,
        overflow: "hidden",
      }}
    >
      <Box sx={commonHeaderStyle} width="10%">
        Name
      </Box>
      <Box sx={commonHeaderStyle} width="20%">
        Email id
      </Box>
      <Box sx={commonHeaderStyle} width="15%">
        Date
      </Box>
      <Box sx={commonHeaderStyle} width="15%">
        Country
      </Box>
      <Box sx={commonHeaderStyle} width="20%">
        Additional languages
      </Box>
      <Box sx={commonHeaderStyle} width="20%">
        Studying language
      </Box>
    </Box>
  );
};

export default StudentListHeader;
