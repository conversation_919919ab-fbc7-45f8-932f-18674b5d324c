import { UserType } from "@/api/mongoTypes";
import { formatDate, formatTime } from "@/utils/dateTime";
import {
  getUserCountry,
  getUserFullName,
  getUserLanguages,
  getUserStudyingLanguage,
} from "@/utils/format";
import { Box, SxProps, Theme } from "@mui/material";
import Link from "next/link";
import React from "react";

const commonHeaderStyle: SxProps<Theme> = {
  fontSize: 13,
  textAlign: "center",
  color: "#3C3C3C",
};

type StudentListRowProps = React.FC<{
  data: UserType;
}>;
const StudentListRow: StudentListRowProps = ({ data }) => {
  return (
    <Link href={`/create/student-dashboard/student/${data.clerkId}`}>
      <Box
        display="flex"
        flexDirection="row"
        sx={{
          width: "100%",
          py: 2,
          overflow: "hidden",
        }}
      >
        <Box sx={commonHeaderStyle} width="10%">
          {getUserFullName(data)}
        </Box>
        <Box sx={commonHeaderStyle} width="20%">
          {data.email}
        </Box>
        <Box sx={commonHeaderStyle} width="15%">
          {formatDate({ date: data.createdAt })}
          &nbsp;
          {formatTime({ date: data.createdAt })}
        </Box>
        <Box sx={commonHeaderStyle} width="15%">
          {getUserCountry(data)}
        </Box>
        <Box sx={commonHeaderStyle} width="20%">
          {getUserLanguages(data)}
        </Box>
        <Box sx={commonHeaderStyle} width="20%">
          {getUserStudyingLanguage(data)}
        </Box>
      </Box>
    </Link>
  );
};

export default StudentListRow;
