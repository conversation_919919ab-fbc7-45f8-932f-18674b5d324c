import {
  Box,
  Divider,
  MenuItem,
  Select,
  Skeleton,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import SectionTitle from "./SectionTitle";
import axios from "axios";
import { SelectChangeEvent } from "@mui/material/Select";
import CreatorCard from "../CreatorCard";
import Image from "next/image";
import SelectAvatar from "../SelectAvatar";
import { ROLE_TYPES } from "@/constant/Enums";

type SelectCreatorProps = React.FC<{
  selectedCreator?: string;
  setSelectedCreator?: React.Dispatch<React.SetStateAction<string>>;

  selectedCreators?: string[];
  setSelectedCreators?: React.Dispatch<React.SetStateAction<string[]>>;

  avatarFile?: File;
  setAvatarFile?: React.Dispatch<React.SetStateAction<File>>;
  firstName?: string;
  setFirstName?: React.Dispatch<React.SetStateAction<string>>;
  lastName?: string;
  setLastName?: React.Dispatch<React.SetStateAction<string>>;
  email?: string;
  setEmail?: React.Dispatch<React.SetStateAction<string>>;
  allowMultiple?: boolean;
  allowCreate?: boolean;
}>;

const SelectCreator: SelectCreatorProps = ({
  selectedCreator,
  setSelectedCreator,

  selectedCreators,
  setSelectedCreators,

  avatarFile,
  setAvatarFile,
  firstName,
  setFirstName,
  lastName,
  setLastName,
  email,
  setEmail,
  allowMultiple = false,
  allowCreate = true,
}) => {
  const [isFetching, setIsfetching] = useState(false);
  const [creators, setCreators] = useState([]);

  const value = allowMultiple ? selectedCreators : selectedCreator;

  useEffect(() => {
    const fetchCreators = async () => {
      try {
        setIsfetching(true);
        const { data } = await axios.post(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/user/get-users`,
          {
            role: ROLE_TYPES.CREATOR,
          }
        );
        if (data.success) {
          setCreators(data.data);
        }
        setIsfetching(false);
      } catch (error) {
        console.error(`Something went wrong in fetchCreators due to `, error);
        setIsfetching(false);
      }
    };
    fetchCreators();
  }, []);

  return (
    <>
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        gap={2}
        maxWidth="600px"
        width="100%"
        mx="auto"
        my={10}
      >
        <SectionTitle text="Creator Details" />
        <Box width="100%" mt={10}>
          {isFetching ? (
            <>
              <Skeleton variant="rectangular" width="100%" height={80} />
            </>
          ) : (
            <Select
              multiple={allowMultiple}
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              sx={{
                width: "100%",
                ".MuiSelect-select": {
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  gap: 2,
                },
              }}
              value={value ?? "SELECT_CREATOR"}
              placeholder="Select Creator"
              label="Select Creator"
              onChange={(e: SelectChangeEvent) => {
                const selectedValue = e.target.value;
                if (allowMultiple && Array.isArray(selectedValue)) {
                  setSelectedCreators(selectedValue);
                } else {
                  setSelectedCreator(selectedValue);
                }
              }}
            >
              <MenuItem value="SELECT_CREATOR" disabled>
                <Typography color="#c2c2c2">Select Creator</Typography>
              </MenuItem>
              {allowCreate && (
                <MenuItem value="CREATE_NEW">Add New Creator</MenuItem>
              )}
              {creators.map((m) => (
                <MenuItem
                  key={m._id}
                  value={m._id}
                  sx={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 2,
                  }}
                  selected={(() => {
                    if (allowMultiple) {
                      return selectedCreators.includes(m._id);
                    }
                    return selectedCreator === m._id;
                  })()}
                >
                  <SelectAvatar
                    profileImageUrl={m.profileImageUrl}
                    firstName={m.firstName}
                  />
                  {m.firstName}&nbsp;{m.lastName}
                </MenuItem>
              ))}
            </Select>
          )}
        </Box>

        {selectedCreator === "CREATE_NEW" && (
          <CreatorCard
            firstName={firstName}
            setFirstName={setFirstName}
            lastName={lastName}
            setLastName={setLastName}
            email={email}
            setEmail={setEmail}
            setAvatarFile={setAvatarFile}
            avatarFile={avatarFile}
          />
        )}
      </Box>
    </>
  );
};

export default SelectCreator;
