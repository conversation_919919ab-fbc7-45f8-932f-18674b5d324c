import { Box, Button } from "@mui/material";
import React from "react";
import { Maybe } from "@/types";

type UploadCaptionsProps = {
  language: "English" | "Spanish";
  selectedSubtitle: Maybe<File>;
  setSelectedSubtitle: React.Dispatch<React.SetStateAction<Maybe<File>>>;
};

const UploadCaptions: React.FC<UploadCaptionsProps> = ({
  selectedSubtitle,
  setSelectedSubtitle,
  language = "English",
}) => {
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    console.log("file", file);
    if (file) {
      setSelectedSubtitle(file);
    }
  };

  const id = `upload-subtitle-${language}`;

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
    >
      <input
        accept=".srt,.vtt,.txt"
        style={{ display: "none" }}
        id={id}
        type="file"
        onChange={handleFileChange}
      />
      <label htmlFor={id}>
        <Button
          variant="contained"
          color="primary"
          style={{
            width: "fit-content",
            marginTop: 40,
            marginBottom: 20,
          }}
          component="span"
        >
          {selectedSubtitle ? "Change Subtitle" : `Add ${language} Subtitle`}
        </Button>
      </label>
      {selectedSubtitle && (
        <p style={{ marginTop: 10 }}>Selected: {selectedSubtitle.name}</p>
      )}
    </Box>
  );
};

export default UploadCaptions;
