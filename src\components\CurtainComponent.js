import React from "react";
import { useSpring, animated } from "react-spring";

const CurtainComponent = () => {
  const springs = useSpring({
    from: { y: 120 },
    to: { y: 0 },
    config: { duration: 600 },
  });
  return (
    <animated.div
      style={{
        width: "100vw",
        height: springs.y.interpolate((y) => `${y}vh`),
        backgroundColor: "#FFFFFF",
        // backgroundColor: "#275BB0",
        position: "absolute",
        zIndex: 1000,
        top: 0,
        left: 0,
      }}
    ></animated.div>
  );
};

export default CurtainComponent;
