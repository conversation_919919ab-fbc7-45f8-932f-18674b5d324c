import React, { useMemo } from "react";
import Button, { ButtonProps } from "@mui/material/Button";

type ButtonType = "primary" | "secondary";
type ButtonBgType = "solid" | "outline";

export type CustomButtonProps = Omit<ButtonProps, "children"> & {
  colortype?: ButtonType;
  className?: string;
  bgtype?: ButtonBgType;
  text: string;
} & ButtonProps;

export const getBgColor = (type: ButtonType) => {
  if (type === "primary") {
    return "#F9B238";
  }
  return "#14A79C";
};

const CustomButton: React.FC<CustomButtonProps> = ({
  colortype = "primary",
  text,
  type = "solid",
  ...props
}) => {
  const validatedType: ButtonType =
    colortype === "primary" || colortype === "secondary"
      ? colortype
      : "primary";

  const bgColor = useMemo(() => getBgColor(validatedType), [validatedType]);

  return (
    <Button
      {...props}
      variant="contained"
      sx={{
        outline: type === "solid" ? "" : `2px solid ${bgColor}`,
        backgroundColor: type === "solid" ? bgColor : "#fff",
        fontWeight: 500,
        fontSize: "0.9rem",
        color: type === "solid" ? "white" : bgColor,
        height: 45,
        borderRadius: "10px",
        border: "none",
        textTransform: "none",
        "&:hover": {
          backgroundColor: bgColor,
          color: "#fff",
        },
        ...(props?.sx ?? {}),
      }}
    >
      {text}
    </Button>
  );
};

export default CustomButton;
