import React, { useState } from "react";
import { Box, TextField, MenuItem, SvgIcon, Typography } from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import EditIcon from "@mui/icons-material/Edit";

function MultipleChoiceQuestion({
  qNumber,
  question,
  options,
  answer,
  handleSelect,
  showOutcome,
}) {
  const [selectedValue, setSelectedValue] = useState("");
  const isCorrect = selectedValue && selectedValue === answer.text;

  const handleChange = (event) => {
    setSelectedValue(event.target.value);
    const isCorrect = answer.text === event.target.value;
    handleSelect(qNumber, isCorrect);
  };

  const questionParts = question ? question.split("_____") : ["", ""];

  return (
    <Box
      sx={{
        margin: "5px 5px",
        display: "flex",
        flexWrap: "wrap",
        alignItems: "center",
      }}
    >
      <Box sx={{ whiteSpace: "nowrap", marginRight: 0 }}>
        <Typography variant="body1" component="span">
          {qNumber}. {questionParts[0]}
        </Typography>
      </Box>
      <TextField
        select
        size="small"
        value={selectedValue}
        onChange={handleChange}
        sx={{
          minWidth: 80,
          ".MuiOutlinedInput-notchedOutline": { borderStyle: "none" },
          ".MuiSelect-select": {
            color: selectedValue ? "#02B199" : "default",
          },
        }}
        label={selectedValue === "" ? "_____" : ""}
      >
        {options?.map((option, index) => (
          <MenuItem key={index} value={option.text}>
            {option.text}
          </MenuItem>
        ))}
      </TextField>
      <Box sx={{ whiteSpace: "nowrap", marginLeft: "0" }}>
        {" "}
        <Typography variant="body1" component="span">
          {questionParts[1]}
        </Typography>
        {showOutcome && (
          <SvgIcon
            sx={{
              marginLeft: "10px",
              verticalAlign: "middle",
              fontSize: "1.2rem",
            }}
            color={isCorrect ? "success" : "error"}
          >
            {isCorrect ? (
              <CheckCircleIcon />
            ) : (
              <EditIcon sx={{ color: "blue" }} />
            )}
          </SvgIcon>
        )}
      </Box>
    </Box>
  );
}

export default MultipleChoiceQuestion;
