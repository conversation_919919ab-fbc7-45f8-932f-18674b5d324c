import React, { useState, useEffect } from "react";
import { Box, Button, Grid } from "@mui/material";
import MultipleChoiceQuestion from "./MultipleChoiceQuestion";

function MultipleChoiceSection({ data, handleNextStage }) {
  const [answers, setAnswers] = useState({});
  const [showOutcome, setShowOutcome] = useState(false);
  const [submitDisabled, setSubmitDisabled] = useState(true);

  const handleMultipleChoiceAnswer = (qNumber, outcome) => {
    setAnswers((prevAnswers) => ({
      ...prevAnswers,
      [qNumber]: outcome,
    }));
  };

  const checkAnswers = () => {
    const counts = { true: 0, false: 0, empty: 0 };

    Object.values(answers).forEach((value) => {
      if (value === true) {
        counts.true += 1;
      } else if (value === false) {
        counts.false += 1;
      } else if (value === "") {
        counts.empty += 1;
      }
    });
    setShowOutcome(true);
    handleNextStage(counts);
  };

  useEffect(() => {
    const allAnswered = Object.values(answers).every((value) => value !== "");
    setSubmitDisabled(!allAnswered);
  }, [answers]);

  useEffect(() => {
    if (data) {
      const answersObject = data.reduce((acc, item) => {
        acc[item.questionNumber] = "";
        return acc;
      }, {});
      setAnswers(answersObject);
    }
  }, [data]);

  return (
    <>
      <Box sx={{ margin: { xs: 2, sm: 4 } }}>
        {data?.map((item, index) => (
          <Grid key={`${item.questionNumber}-q`} item xs={12}>
            {" "}
            <MultipleChoiceQuestion
              qNumber={item.questionNumber}
              question={item.question.questionText}
              options={item.question.options}
              answer={item.question.options.filter((opt) => opt.isCorrect)[0]}
              handleSelect={handleMultipleChoiceAnswer}
              showOutcome={showOutcome}
            />
          </Grid>
        ))}
      </Box>

      <Button
        size="small"
        variant="contained"
        onClick={checkAnswers}
        disabled={submitDisabled}
        sx={{
          mt: 2,
          marginBottom: { xs: 2, sm: 4 },
          display: "block",
          marginLeft: "auto",
          marginRight: "auto",
        }}
      >
        Check Answers
      </Button>
    </>
  );
}

export default MultipleChoiceSection;
