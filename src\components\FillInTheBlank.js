import React, { useState } from "react";
import TextField from "@mui/material/TextField";
import { Box } from "@mui/material";

function FillInTheBlank({ qNumber, question, label }) {
  const [answer, setAnswer] = useState("");
  const handleInputChange = (event) => {
    setAnswer(event.target.value);
  };

  return (
    <Box style={{ margin: "5px 10px" }}>
      {qNumber}. {question[0]}
      <TextField
        label=""
        value={answer}
        required
        id={label}
        name={label}
        size="small"
        margin="none"
        variant="standard"
        onChange={handleInputChange}
        sx={{ width: 85, margin: "0 5", marginLeft: "5px", marginRight: "5px" }}
      />
      {question[1]}
    </Box>
  );
}

export default FillInTheBlank;
