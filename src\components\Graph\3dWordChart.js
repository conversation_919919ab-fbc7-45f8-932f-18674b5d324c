import React, { useState, useEffect } from "react";
import { ForceGraph3D } from "react-force-graph";
import SpriteText from "three-spritetext";

const MyForceGraph = () => {
  const [data, setData] = useState({ nodes: [], links: [] });
  useEffect(() => {
    fetch("/data/roots/roots.json")
      .then((res) => res.json())
      .then((sdata) => {
        sdata.nodes.forEach((node, index) => {
          node.visible = node.group == 1;
        });
        sdata.links.forEach((link) => {
          link.visible = false;
        });
        setData(sdata);
      });
  }, []);

  return (
    <ForceGraph3D
      graphData={data}
      nodeAutoColorBy="group"
      nodeVisibility={(node) => node.visible}
      linkVisibility={(link) => link.visible}
      linkColor={() => data.linkColor}
      linkWidth={() => data.linkWidth}
      linkOpacity={() => data.linkOpacity}
      linkDistance={(link) => 5} // distance between nodes in cluster
      d3Force={(forceName, forceFn) => {
        if (forceName === "charge") {
          // Use a negative value for repulsion. Modify this value to affect unconnected nodes
          return forceFn.strength(-300); //disconnected node distance (-300 - 100)
        }
      }}
      nodeThreeObject={(node) => {
        if (node.visible) {
          const sprite = new SpriteText(node.id);
          const style = data.nodeStyles.find(
            (nodeStyle) => nodeStyle.group === node.group
          );
          sprite.color = style.color;
          sprite.textHeight = style.textHeight;
          sprite.fontFace = style.fontFace;
          return sprite;
        } else {
          return null;
        }
      }}
      onNodeClick={(node) => {
        const linkedNodes = data.links.filter(
          (link) => link.source.id === node.id || link.target.id === node.id
        );
        linkedNodes.forEach((linkedNode) => {
          if (linkedNode.source.id !== node.id) {
            linkedNode.visible = !linkedNode.visible;
          }
        });
        const linkedNodeIds = linkedNodes.map((link) => link.source.id);
        data.nodes.forEach((eachNode) => {
          if (linkedNodeIds.includes(eachNode.id) && eachNode.id !== node.id) {
            eachNode.visible = !eachNode.visible;
          }
        });
        setData({ ...data });
      }}
    />
  );
};

export default MyForceGraph;
