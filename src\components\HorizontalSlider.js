import * as React from "react";
import Box from "@mui/material/Box";
import Slider from "@mui/material/Slider";

export default function DiscreteSlider({ selectPlaybackRate }) {
  const min = 75;
  const max = 100;
  const step = 5;

  const marks = [];
  for (let i = min; i <= max; i += step) {
    marks.push({ value: i, label: `${i}` });
  }

  const handleChange = (v) => {
    const pct = v.target.value / 100;
    selectPlaybackRate(pct);
  };

  return (
    <Box sx={{ width: { xs: 200, md: 400 } }}>
      <Slider
        aria-label="playback-speed"
        defaultValue={100}
        valueLabelDisplay="auto"
        scale={(x) => -x}
        onChange={handleChange}
        marks={marks}
        min={min}
        max={max}
        step={step}
        sx={{
          "& .MuiSlider-valueLabelLabel": {
            fontFamily: "Arial, sans-serif",
            fontSize: "20px",
            color: "black",
          },
          direction: "rtl", // this flips the rendering of the component
        }}
      />
    </Box>
  );
}
