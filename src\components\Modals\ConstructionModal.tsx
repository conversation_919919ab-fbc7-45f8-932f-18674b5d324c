import { BaseModal } from "./BaseModal";
import React from "react";

type Props = {
  isOpen: boolean;
  handleClose: () => void;
};

export const ConstructionModal = ({ isOpen, handleClose }: Props) => {
  return (
    <BaseModal
      title="This is a sneak peak!"
      isOpen={isOpen}
      handleClose={handleClose}
    >
      <p className="text-sm text-gray-500">
        This site is currently under construction. We are working hard to update
        it and will have it ready soon.
      </p>

      <div className="mb-4 mt-4 flex justify-center">
        <video width="500" height="300" autoPlay muted>
          <source src="/videos/underConstruction.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      </div>

      <p className="text-sm text-gray-500">Thank you for your patience!</p>
    </BaseModal>
  );
};
