import { Paper } from "@mui/material";

export type PageProps = {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
};
import { QueryClient, QueryClientProvider } from "react-query";

const queryClient = new QueryClient();

function Page({ children, title, subtitle }: PageProps) {
  return (
    <QueryClientProvider client={queryClient}>
      <div className="w-full font-sans">
        <div className="mx-auto w-full md:w-10/12">
          <div className="p-8">
            <div className="mt-16">{children}</div>
          </div>
        </div>
      </div>
    </QueryClientProvider>
  );
}

export default Page;
