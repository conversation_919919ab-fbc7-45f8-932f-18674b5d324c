import { Box, Container, Typography } from "@mui/material";
import { useRouter } from "next/router";
import React from "react";

const containerStyles = { marginTop: "30px", height: "100%" };

const PageHeaderAndContainer = ({ title, children }) => {
  const router = useRouter();

  const handleGoBack = () => {
    router.back();
  };

  return (
    <Container maxWidth="lg" sx={containerStyles}>
      <Box display="flex" flexDirection="row">
        <Box
          sx={{
            cursor: "pointer",
            width: {
              xs: "15%",
              sm: "5%",
            },
          }}
          onClick={handleGoBack}
        >
          <svg
            width="35"
            height="35"
            viewBox="0 0 35 35"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              x="1"
              y="1"
              width="33"
              height="33"
              rx="16.5"
              stroke="#64748B"
              stroke-width="2"
            />
            <path
              d="M20.5 11.5L14.5 17.5L20.5 23.5"
              stroke="#64748B"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </Box>
        <Box
          sx={{
            width: {
              xs: "85%",
              sm: "95%",
            },
          }}
        >
          <Typography fontSize={24} fontWeight={700}>
            {title}
          </Typography>
        </Box>
      </Box>
      <Box display="flex" flexDirection="row" width="100%">
        <Box
          sx={{
            width: {
              xs: "0%",
              sm: "5%",
            },
          }}
        ></Box>
        <Box
          sx={{
            width: {
              xs: "100%",
              sm: "95%",
            },
          }}
        >
          {children}
        </Box>
      </Box>
    </Container>
  );
};

export default PageHeaderAndContainer;
