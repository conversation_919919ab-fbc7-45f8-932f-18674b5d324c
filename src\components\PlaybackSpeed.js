import { Box, Typography } from "@mui/material";
import Image from "next/image";
import reduce from "../../public/images/icons/button-icons/reduce.svg";
import increase from "../../public/images/icons/button-icons/increase.svg";

export default function PlaybackSpeed({
  playerSpeed,
  increaseClickHandler,
  decreaseClickHandler,
}) {
  return (
    <Box
      sx={{
        height: "70px",
        backgroundColor: "#14A79C",
        borderRadius: "10px",
        padding: "15px",
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        gap: "10px",
      }}
    >
      <Typography sx={{ color: "white", whiteSpace: "nowrap" }}>
        Playback Speed :
      </Typography>
      <Image
        src={reduce}
        alt="playback speed"
        width={20}
        height={20}
        style={{ cursor: "pointer" }}
        onClick={decreaseClickHandler}
      ></Image>
      <input
        variant="standard"
        defaultValue={playerSpeed}
        style={{
          height: "30px",
          width: "80px",
          backgroundColor: "white",
          padding: "0px",
          margin: "0px",
          border: "none",
          borderRadius: "5px",
          textAlign: "center",
        }}
      ></input>
      <Image
        src={increase}
        width={20}
        alt="increase speed"
        height={20}
        style={{ cursor: "pointer" }}
        onClick={increaseClickHandler}
      ></Image>
    </Box>
  );
}
