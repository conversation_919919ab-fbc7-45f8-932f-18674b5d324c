import { currencies } from "@/constant/currency";
import { Box, MenuItem, Select, TextField, Typography } from "@mui/material";
import React from "react";
import { InfoOutlined } from "@mui/icons-material";

const PriceInput = ({
  currency,
  onCurrencyChanged,
  price,
  onPriceChanged,
  label,
  isDisabled = false,
  showInfoAtBottom = false,
}) => {
  return (
    <Box display="flex" flexDirection="column" sx={{ marginBottom: "20px" }}>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        width="100%"
        gap={2}
        aria-disabled={isDisabled}
      >
        <Select
          label="languageProficiency"
          value={currency}
          onChange={(e) => {
            const val = e.target.value;
            onCurrencyChanged(val);
          }}
        >
          {currencies?.map((cur) => (
            <MenuItem key={cur.id} value={cur.value}>
              {cur.name}
            </MenuItem>
          ))}
        </Select>
        <TextField
          label={label}
          variant="outlined"
          fullWidth
          type="number"
          value={price}
          onChange={(e) => {
            const val = +e.target.value;
            onPriceChanged(val);
          }}
        />
      </Box>
      {showInfoAtBottom && (
        <Typography
          color={"orange"}
          textAlign="center"
          fontSize={14}
          mt={2}
          display="flex"
          flexDirection="row"
          alignItems="center"
          gap={1}
        >
          <InfoOutlined style={{ fontSize: 14 }} />
          {isDisabled
            ? "Currency & price is not editable"
            : "You cannot edit currency & price later.Please choose carefully."}
        </Typography>
      )}
    </Box>
  );
};

export default PriceInput;
