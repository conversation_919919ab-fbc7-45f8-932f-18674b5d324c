import { Box, Typography } from "@mui/material";
import React from "react";

const LimitedDealTag = ({ width = "60%" }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      sx={{ width, gap: 2 }}
    >
      <svg
        width="11"
        height="16"
        viewBox="0 0 11 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.84965 0.0397949L0.0146484 7.12304L6.09215 9.00754L1.52965 16L10.3446 7.64354L5.60015 5.52754L7.84965 0.0397949Z"
          fill="#FBB11C"
        />
      </svg>
      <Typography sx={{ color: "rgba(177, 177, 177, 1)" }}>
        Limited-Time Deal
      </Typography>
    </Box>
  );
};

export default LimitedDealTag;
