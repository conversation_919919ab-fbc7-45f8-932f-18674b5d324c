import { Box, Button, Typography } from "@mui/material";
import React, { useMemo } from "react";
import { UserCapIcon } from "./Icon";
import Link from "next/link";
import { MaybeCartSchedule } from "@/types";
import { getAccessRestrictedInfo } from "@/utils/classes";

type AccessRestrictedProps = React.FC<{
  redirectUrl: string;
  data: MaybeCartSchedule;
}>;
const AccessRestricted: AccessRestrictedProps = ({ redirectUrl, data }) => {
  const { title, description, button } = useMemo(() => {
    return getAccessRestrictedInfo({ data });
  }, [data]);

  return (
    <Box
      sx={{
        position: "absolute",
        zIndex: 50,
        background: "rgba(255, 255, 255, 0.95)",
        display: "flex",
        flexDirection: "column",
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        height: "100%",
        justifyContent: "center",
        gap: 3,
        textAlign: "center",
        alignItems: "center",
        p: 4,
      }}
    >
      <Box display="flex" flexDirection="column" gap={1} mb={4}>
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="center"
          gap={2}
        >
          <UserCapIcon />
          <Typography
            fontSize={20}
            fontWeight={800}
            textAlign="center"
            alignItems="center"
            mb={0}
          >
            {title}
          </Typography>
        </Box>
        <Typography fontSize={13} textAlign="center" fontWeight={600}>
          {description}
        </Typography>
      </Box>
      <Link href={redirectUrl}>
        <Button
          sx={{
            background: "rgba(20, 167, 156, 1)",
            color: "#fff",
            padding: "6px 10px",
            width: "fit-content",
            fontSize: "0.7rem",
            borderRadius: 1,
          }}
        >
          {button}
        </Button>
      </Link>
    </Box>
  );
};

export default AccessRestricted;
