const UserIcon = () => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8M6 8a6 6 0 1 1 12 0A6 6 0 0 1 6 8m2 10a3 3 0 0 0-3 3 1 1 0 1 1-2 0 5 5 0 0 1 5-5h8a5 5 0 0 1 5 5 1 1 0 1 1-2 0 3 3 0 0 0-3-3z"
        fill="#6D6D6D"
      />
    </svg>
  );
};

const TimeIcon = () => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7 2.125C4.30859 2.125 2.125 4.30859 2.125 7C2.125 9.69141 4.30859 11.875 7 11.875C9.69141 11.875 11.875 9.69141 11.875 7C11.875 4.30859 9.69141 2.125 7 2.125Z"
        stroke="#6D6D6D"
        stroke-miterlimit="10"
      />
      <path
        d="M7 3.75V7.40625H9.4375"
        stroke="#6D6D6D"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

const DateIcon = () => {
  return (
    <svg
      width="14"
      height="16"
      viewBox="0 0 14 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.33301 5.6665H11.6663M2.33301 5.6665V10.7998C2.33301 11.4532 2.33301 11.7798 2.46017 12.0295C2.57203 12.249 2.7505 12.4275 2.97001 12.5393C3.21909 12.6665 3.54576 12.6665 4.19792 12.6665H9.80142C10.4536 12.6665 10.7797 12.6665 11.0288 12.5393C11.2487 12.4273 11.4272 12.2488 11.5392 12.0295C11.6663 11.7798 11.6663 11.4543 11.6663 10.8022V5.6665M2.33301 5.6665V5.19984C2.33301 4.5465 2.33301 4.21984 2.46017 3.97017C2.57217 3.75025 2.75009 3.57234 2.97001 3.46034C3.21967 3.33317 3.54634 3.33317 4.19967 3.33317H4.66634M11.6663 5.6665V5.19809C11.6663 4.54592 11.6663 4.21925 11.5392 3.97017C11.4272 3.75057 11.2485 3.57209 11.0288 3.46034C10.7797 3.33317 10.453 3.33317 9.79967 3.33317H9.33301M4.66634 3.33317H9.33301M4.66634 3.33317V2.1665M9.33301 3.33317V2.1665"
        stroke="#6D6D6D"
        stroke-width="0.916667"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
const LocationIcon = () => {
  return (
    <>
      <svg
        width="14"
        height="14"
        viewBox="0 0 16 17"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8 9.5C7.50555 9.5 7.0222 9.35338 6.61108 9.07868C6.19995 8.80397 5.87952 8.41353 5.6903 7.95671C5.50108 7.49989 5.45157 6.99723 5.54804 6.51228C5.6445 6.02732 5.8826 5.58187 6.23223 5.23223C6.58187 4.8826 7.02732 4.6445 7.51228 4.54804C7.99723 4.45157 8.4999 4.50108 8.95671 4.6903C9.41353 4.87952 9.80397 5.19995 10.0787 5.61108C10.3534 6.0222 10.5 6.50555 10.5 7C10.4992 7.6628 10.2356 8.29822 9.76689 8.76689C9.29822 9.23556 8.6628 9.49921 8 9.5ZM8 5.5C7.70333 5.5 7.41332 5.58797 7.16665 5.7528C6.91997 5.91762 6.72771 6.15189 6.61418 6.42598C6.50065 6.70007 6.47095 7.00167 6.52882 7.29264C6.5867 7.58361 6.72956 7.85088 6.93934 8.06066C7.14912 8.27044 7.41639 8.4133 7.70737 8.47118C7.99834 8.52906 8.29994 8.49935 8.57403 8.38582C8.84812 8.27229 9.08238 8.08003 9.24721 7.83336C9.41203 7.58668 9.5 7.29667 9.5 7C9.4996 6.6023 9.34144 6.221 9.06022 5.93978C8.779 5.65856 8.3977 5.5004 8 5.5Z"
          fill="#3C3C3C"
        />
        <path
          d="M8 15.5L3.782 10.5255C3.72339 10.4508 3.66539 10.3756 3.608 10.3C2.88786 9.3507 2.49866 8.19155 2.5 7C2.5 5.54131 3.07947 4.14236 4.11092 3.11091C5.14237 2.07946 6.54131 1.5 8 1.5C9.45869 1.5 10.8576 2.07946 11.8891 3.11091C12.9205 4.14236 13.5 5.54131 13.5 7C13.5012 8.19098 13.1122 9.34954 12.3925 10.2985L12.392 10.3C12.392 10.3 12.242 10.497 12.2195 10.5235L8 15.5ZM4.4065 9.6975C4.4065 9.6975 4.523 9.8515 4.5495 9.8845L8 13.954L11.455 9.879C11.477 9.8515 11.594 9.6965 11.5945 9.696C12.1831 8.92057 12.5012 7.97352 12.5 7C12.5 5.80653 12.0259 4.66193 11.182 3.81802C10.3381 2.97411 9.19348 2.5 8 2.5C6.80653 2.5 5.66194 2.97411 4.81802 3.81802C3.97411 4.66193 3.5 5.80653 3.5 7C3.49877 7.97415 3.81723 8.92179 4.4065 9.6975Z"
          fill="#3C3C3C"
        />
      </svg>
    </>
  );
};

const CloseIcon = () => {
  return (
    <svg
      width="13"
      height="13"
      viewBox="0 0 13 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 11.486L6.243 6.243L11.486 11.486M11.486 1L6.242 6.243L1 1"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

const RedirectIcon = () => {
  return (
    <svg
      width="14"
      height="13"
      viewBox="0 0 14 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 5.5V11.25C11 11.4142 10.9677 11.5767 10.9048 11.7284C10.842 11.88 10.75 12.0178 10.6339 12.1339C10.5178 12.25 10.38 12.342 10.2284 12.4048C10.0767 12.4677 9.91415 12.5 9.75 12.5H2.25C1.91848 12.5 1.60054 12.3683 1.36612 12.1339C1.1317 11.8995 1 11.5815 1 11.25V3.75C1 3.41848 1.1317 3.10054 1.36612 2.86612C1.60054 2.6317 1.91848 2.5 2.25 2.5H7.48375M9.5 0.5H13V4M6 7.5L12.75 0.75"
        stroke="white"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

const UserCapIcon = () => {
  return (
    <svg
      width="26"
      height="27"
      viewBox="0 0 26 27"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.32227 12.7686V15.5311C5.32227 21.2186 20.6379 21.2186 20.6379 15.5311V12.7686H5.32227Z"
        fill="#596066"
      />
      <path
        d="M25.1875 10.8188L13 16.3032L0.8125 10.8188L13 5.33447L25.1875 10.8188Z"
        fill="#3E4347"
      />
      <path
        d="M12.9594 10.575C11.0906 11.4281 7.02812 13.2562 6.86562 13.3375C6.78437 13.3781 6.70312 13.4594 6.70312 13.5812V16.3437C6.70312 16.6687 7.10938 16.6687 7.10938 16.3437V13.7844C8.9375 12.9719 12.8781 11.1844 13.0406 11.1031C13.2844 10.9812 13.2031 10.4937 12.9594 10.575Z"
        fill="#FFCE31"
      />
      <path
        d="M6.90664 17.2781C7.33294 17.2781 7.67852 16.8416 7.67852 16.3031C7.67852 15.7646 7.33294 15.3281 6.90664 15.3281C6.48035 15.3281 6.13477 15.7646 6.13477 16.3031C6.13477 16.8416 6.48035 17.2781 6.90664 17.2781Z"
        fill="#FFCE31"
      />
      <path
        d="M6.90664 21.5845C7.31289 21.5845 7.67852 21.3813 7.67852 21.097V16.3032H6.13477V21.097C6.13477 21.3813 6.50039 21.5845 6.90664 21.5845Z"
        fill="#FFCE31"
      />
      <path
        d="M7.43418 16.4248C7.39355 16.4248 7.39355 16.4654 7.35293 16.4654V21.5029C7.39355 21.5029 7.39355 21.4623 7.43418 21.4623V16.4248ZM7.10918 16.5467H7.02793V21.5842H7.10918V16.5467ZM6.78418 16.5467H6.70293V21.5842H6.78418V16.5467ZM6.45918 16.4654C6.41855 16.4654 6.41855 16.4248 6.37793 16.4248V21.5029C6.41855 21.5029 6.41855 21.5436 6.45918 21.5436V16.4654Z"
        fill="#594640"
      />
    </svg>
  );
};

export {
  UserIcon,
  TimeIcon,
  DateIcon,
  LocationIcon,
  CloseIcon,
  RedirectIcon,
  UserCapIcon,
};
