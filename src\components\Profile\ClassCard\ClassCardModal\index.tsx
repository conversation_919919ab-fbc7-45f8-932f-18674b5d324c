import React, { useMemo } from "react";
import { Box, Button, Typography, Modal, IconButton } from "@mui/material";
import Image from "next/image";
import ClassTag from "../ClassTag";
import {
  CloseIcon,
  DateIcon,
  LocationIcon,
  RedirectIcon,
  TimeIcon,
  UserIcon,
} from "./Icon";
import CreatorDetails from "@/components/classes/OnlineClub/CreatorDetails";
import AccessRestricted from "./AccessRestricted";
import {
  getClassesImage,
  getClassesLevel,
  getClassesLocation,
  getClassTimings,
  getClassTitleAndSubtitle,
  getClassTypeAndSubType,
  getCreatorDetails,
  getLinkInfo,
  getRedirectUrl,
  getStartAndEndDateOfClasses,
  getTeachers,
} from "@/utils/classes";
import { formatDate } from "@/utils/dateTime";
import { MaybeCartSchedule } from "@/types";

type ClassCardModalProps = React.FC<{
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  data: MaybeCartSchedule;
  isRestricted?: boolean;
  cardDate: Date;
}>;

const ClassCardModal: ClassCardModalProps = ({
  open,
  setOpen,
  data,
  isRestricted = false,
  cardDate,
}) => {
  const handleClose = () => setOpen(false);

  console.log("data", data);

  const teachers = useMemo(() => {
    return getTeachers({ data });
  }, [data]);

  console.log("teachers", teachers);

  const creatorDetails = useMemo(() => {
    return getCreatorDetails({ cartData: data });
  }, [data]);

  const level = useMemo(() => {
    return getClassesLevel({ cartData: data });
  }, [data]);

  const imgSrc = useMemo(() => {
    return getClassesImage({ purchaseDetails: data });
  }, [data]);

  const { title, description } = useMemo(
    () => getClassTitleAndSubtitle({ cartData: data }),
    [data]
  );

  const { startDate, endDate } = useMemo(() => {
    return getStartAndEndDateOfClasses({ cartData: data });
  }, [data]);

  const location = useMemo(() => {
    return getClassesLocation({ cartData: data });
  }, [data]);

  const timings = useMemo(() => {
    return getClassTimings({ data });
  }, [data]);

  const redirectUrl = useMemo(() => {
    return getRedirectUrl({
      cartData: data,
    });
  }, [data]);

  const { type, subType } = useMemo(() => {
    return getClassTypeAndSubType({ cartData: data });
  }, [data]);

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: {
            xs: "100%",
            xsm: 300,
            sm: 375,
          },
          bgcolor: "background.paper",
          boxShadow: 24,
          borderRadius: 3,
          p: 3,
        }}
      >
        <Box
          sx={{
            height: "100%",
            width: "100%",
            position: "relative",
            overflow: "hidden",
          }}
        >
          {isRestricted && (
            <AccessRestricted data={data} redirectUrl={redirectUrl} />
          )}
          <Box
            sx={{
              height: 150,
              width: "100%",
              position: "relative",
              borderRadius: 2,
              overflow: "hidden",
              background: "#000",
            }}
          >
            <IconButton
              onClick={() => {
                setOpen(false);
              }}
              sx={{
                position: "absolute",
                top: 10,
                right: 10,
                cursor: "pointer",
                background: "rgba(58, 58, 58, 1)",
                borderRadius: 20,
                zIndex: 200,
              }}
            >
              <CloseIcon />
            </IconButton>
            <Image objectFit="contain" alt="Iggg" src={imgSrc} fill />
          </Box>
          <ClassTag textOne={`${type} ${subType && ":"}`} textTwo={subType} />
          <Typography fontSize={18} fontWeight={800}>
            {title}
          </Typography>
          <Typography color="rgba(183, 183, 183, 1)" fontSize={14} mb={3}>
            {level ?? null}
          </Typography>

          {creatorDetails.length > 0 && (
            <RowDetails text="" icon={<UserIcon />} name="Instructor">
              <CreatorDetails mt={0} isSmall creatorDetails={creatorDetails} />
            </RowDetails>
          )}
          {teachers.length > 0 && (
            <RowDetails text="" icon={<UserIcon />} name="Instructor">
              <CreatorDetails mt={0} isSmall creatorDetails={teachers} />
            </RowDetails>
          )}
          {startDate && (
            <RowDetails
              text={formatDate({
                date: startDate,
              }).toLocaleString()}
              icon={<DateIcon />}
              name="Date"
            />
          )}
          {cardDate && (
            <RowDetails
              text={formatDate({
                date: cardDate,
              }).toLocaleString()}
              icon={<DateIcon />}
              name="Date"
            />
          )}
          {timings && (
            <RowDetails text={timings} icon={<TimeIcon />} name="Time" />
          )}
          {location && (
            <RowDetails
              text={location}
              icon={<LocationIcon />}
              name="Location"
            />
          )}

          <Typography
            color="rgba(60, 60, 60, 1)"
            fontSize={14}
            my={1}
            fontWeight={700}
          >
            About this class
          </Typography>
          <Typography color="rgba(109, 109, 109, 1)" fontSize={12}>
            {description}
          </Typography>
          <LinkInfo data={data} />
        </Box>
      </Box>
    </Modal>
  );
};

type LinkInfoProps = React.FC<{
  data: MaybeCartSchedule;
}>;
const LinkInfo: LinkInfoProps = ({ data }) => {
  const { show, buttonText, status, helperText, url } = useMemo(() => {
    return getLinkInfo({ data });
  }, [data]);

  if (!show) {
    return null;
  }

  return (
    <>
      <Typography
        color="rgba(60, 60, 60, 1)"
        fontSize={14}
        my={1}
        fontWeight={700}
      >
        Event link:
      </Typography>
      <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
        <Button
          onClick={() => {
            if (url) {
              window.open(url, "_blank");
            }
          }}
          aria-disabled={status === "upcoming"}
          sx={{
            display: "flex",
            flexDirection: "row",
            gap: 2,
            padding: "6px",
            fontSize: 10,
            minWidth: "fit-content",
            background: "rgba(20, 167, 156, 1)",
          }}
        >
          <RedirectIcon />
          &nbsp; {buttonText}
        </Button>
        {helperText && (
          <Typography color="rgba(109, 109, 109, 1)" fontSize={13}>
            {helperText}
          </Typography>
        )}
      </Box>
    </>
  );
};

type RowDetailsProps = React.FC<{
  text?: string;
  name: string;
  icon: React.ReactNode;
  children?: React.ReactNode;
}>;
const RowDetails: RowDetailsProps = ({ text, icon, name, children }) => {
  return (
    <Box
      display="flex"
      gap={1}
      flexDirection="row"
      width="100%"
      alignItems="center"
    >
      {icon}
      <Typography
        fontSize={12}
        sx={{ textWrap: "nowrap" }}
        color="rgba(109, 109, 109, 1)"
      >
        {name} :{" "}
      </Typography>
      {text ? (
        <Typography fontSize={12} color="rgba(60, 60, 60, 1)">
          {text}
        </Typography>
      ) : (
        children
      )}
    </Box>
  );
};

export default ClassCardModal;
