import { Box, Typography } from "@mui/material";
import React from "react";

const ClassTag = ({ textOne, textTwo }) => {
  return (
    <Box display="flex" flexDirection="row" alignItems="center" gap={1} mt={1}>
      <span
        style={{
          display: "flex",
          height: 7,
          width: 7,
          borderRadius: 4,
          backgroundColor: "#F9B238",
        }}
      />
      <Typography color="rgba(109, 109, 109, 1)" sx={{ fontSize: 12 }}>
        {textOne}
      </Typography>
      {textTwo && (
        <Typography color="rgba(109, 109, 109, 1)" sx={{ fontSize: 12 }}>
          {textTwo}
        </Typography>
      )}
    </Box>
  );
};

export default ClassTag;
