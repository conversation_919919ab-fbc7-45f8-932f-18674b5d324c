import { CLASSESS_FETCH_DURATION } from "@/constant/Enums";
import { MaybeCartSchedule } from "@/types";
import {
  getClassesLevel,
  getClassTimings,
  getStartAndEndDateOfClasses,
} from "@/utils/classes";
import { formatDate } from "@/utils/dateTime";
import { Box, Typography } from "@mui/material";
import React, { useMemo } from "react";

type DateAndLevelProps = React.FC<{
  data: MaybeCartSchedule;
  cardDate?: Date;
  isDashboard?: boolean;
  active: CLASSESS_FETCH_DURATION;
}>;
const DateAndLevel: DateAndLevelProps = ({
  cardDate = null,
  data,
  isDashboard = false,
  active,
}) => {
  const level = useMemo(() => {
    return getClassesLevel({ cartData: data });
  }, [data]);

  const { startDate } = useMemo(() => {
    return getStartAndEndDateOfClasses({
      cartData: data,
      isDashboard,
      active,
    });
  }, [data, isDashboard, active]);

  const time = useMemo(() => {
    return getClassTimings({ data });
  }, [data]);

  const formattedDate = cardDate
    ? formatDate({ date: new Date(cardDate), inText: true })
    : formatDate({ date: startDate, inText: true });

  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      my={1}
    >
      <Typography fontSize={12} color="rgba(183, 183, 183, 1)">
        {level ?? <>&nbsp;</>}
      </Typography>

      {time && (
        <Typography fontSize={12} color="rgba(60, 60, 60, 1)">
          {formattedDate} {formattedDate && ","} {time}
        </Typography>
      )}
    </Box>
  );
};

export default DateAndLevel;
