import { CLASSESS_FETCH_DURATION } from "@/constant/Enums";
import { Box, Typography } from "@mui/material";
import React from "react";

type ClassStatusProps = React.FC<{
  status: CLASSESS_FETCH_DURATION;
}>;

const ClassStatus: ClassStatusProps = ({ status }) => {
  return (
    <Box
      sx={{
        color: "#fff",
        fontSize: 12,
        background: getStatusBg(status),
        zIndex: 100,
        display: "flex",
        position: "absolute",
        top: 10,
        right: 10,
        px: 2,
        borderRadius: 10,
      }}
    >
      <Typography fontSize={12}>{getStatusText(status)}</Typography>
    </Box>
  );
};

const getStatusBg = (type: CLASSESS_FETCH_DURATION) => {
  if (type === CLASSESS_FETCH_DURATION.CURRENT) {
    return "rgba(20, 174, 92, 1)";
  }
  if (type === CLASSESS_FETCH_DURATION.UPCOMING) {
    return "rgba(39, 132, 214, 1)";
  }
  if (type === CLASSESS_FETCH_DURATION.PAST) {
    return "#3C3C3C";
  }
  return "rgba(60, 60, 60, 1)";
};

const getStatusText = (type: CLASSESS_FETCH_DURATION) => {
  if (type === CLASSESS_FETCH_DURATION.CURRENT) {
    return "Now";
  }
  if (type === CLASSESS_FETCH_DURATION.UPCOMING) {
    return "Upcoming";
  }
  if (type === CLASSESS_FETCH_DURATION.PAST) {
    return "Ended";
  }
  return "Ended";
};

export default ClassStatus;
