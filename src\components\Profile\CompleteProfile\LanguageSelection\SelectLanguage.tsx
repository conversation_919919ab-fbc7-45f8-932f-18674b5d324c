import React, { useState, useMemo } from "react";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import TextField from "@mui/material/TextField";
import InputAdornment from "@mui/material/InputAdornment";
import SearchIcon from "@mui/icons-material/Search";
import Box from "@mui/material/Box";
import { commonStyle } from "./style";

const SelectLanguage = ({
  languagesList,
  selectedLanguage,
  isTransparentBorder = false,
  selectStyle = {},
  onChange,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [open, setOpen] = useState(false);

  const border = isTransparentBorder ? "none" : "1px solid rgba(0, 95, 86, 1)";
  const value = selectedLanguage ? `Language :${selectedLanguage}` : "";

  // Sort languages alphabetically and filter based on search term
  const filteredAndSortedLanguages = useMemo(() => {
    return languagesList
      .filter(
        (language) =>
          language.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          language.nameInEnglish
            .toLowerCase()
            .includes(searchTerm.toLowerCase())
      )
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [languagesList, searchTerm]);

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleSelectChange = (event) => {
    onChange(event);
    setOpen(false);
    setSearchTerm(""); // Clear search after selection
  };

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSearchTerm(""); // Clear search when closing
  };

  return (
    <>
      <FormControl
        sx={{
          width: "100%",
        }}
        variant="outlined"
      >
        <Select
          value={value}
          onChange={handleSelectChange}
          onOpen={handleOpen}
          onClose={handleClose}
          open={open}
          inputProps={{ "aria-label": "Without label" }}
          displayEmpty
          renderValue={(selected) => {
            if (!selected) {
              return "Select Language";
            }
            return selected;
          }}
          sx={{
            ...commonStyle,
            "& .MuiOutlinedInput-notchedOutline": {
              border: selectedLanguage ? border : "none",
            },
            "&:hover .MuiOutlinedInput-notchedOutline": {
              border: selectedLanguage ? border : "none",
            },
            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
              border: selectedLanguage ? border : "none",
            },
            ...selectStyle,
          }}
          MenuProps={{
            PaperProps: {
              sx: {
                maxHeight: 300,
              },
            },
          }}
        >
          {languagesList.length > 2 && (
            <Box
              sx={{
                px: 2,
                pb: 1,
                pt: 1,
                position: "sticky",
                top: 0,
                backgroundColor: "white",
                zIndex: 1,
              }}
            >
              <TextField
                size="small"
                placeholder="Search languages..."
                value={searchTerm}
                onChange={handleSearchChange}
                onClick={(e) => e.stopPropagation()}
                onKeyDown={(e) => e.stopPropagation()}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  width: "100%",
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": {
                      borderColor: "rgba(0, 0, 0, 0.23)",
                    },
                    "&:hover fieldset": {
                      borderColor: "rgba(0, 0, 0, 0.87)",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "rgba(0, 95, 86, 1)",
                    },
                  },
                }}
              />
            </Box>
          )}

          {/* Language Options */}
          {filteredAndSortedLanguages.length > 0 ? (
            filteredAndSortedLanguages.map((language) => (
              <MenuItem key={language._id} value={language._id}>
                {language.name}
              </MenuItem>
            ))
          ) : (
            <MenuItem disabled>
              <Box sx={{ color: "text.secondary", fontStyle: "italic" }}>
                No languages found
              </Box>
            </MenuItem>
          )}
        </Select>
      </FormControl>
    </>
  );
};

export default SelectLanguage;
