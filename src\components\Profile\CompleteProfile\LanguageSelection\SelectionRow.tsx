import React from "react";
import { SelectContStyle, SelectRowContainer } from "./style";
import Box from "@mui/material/Box";
import SelectLanguage from "./SelectLanguage";
import { LanguageProficiencyType, LanguageType } from "@/api/mongoTypes";
import {
  LanguageProficiencyListType,
  LangugaesListType,
  langugaeStateTypes,
  SingleLanguageType,
} from "@/types";
import SelectProficiency from "./SelectProficiency";
import DeleteIcon from "@mui/icons-material/Delete";
import { SxProps, Theme } from "@mui/material";
import { getProficiencyEn } from "@/utils/common";

type SelectionRowProps = {
  languagesList: LangugaesListType;
  proficiencyList: LanguageProficiencyListType;
  languages: langugaeStateTypes;
  setLanguages: React.Dispatch<React.SetStateAction<langugaeStateTypes>>;
  index: number;
  isTransparentBorder?: boolean;
  deleteStyle?: SxProps<Theme>;
  languageStyle?: SxProps<Theme>;
  proficiencyStyle?: SxProps<Theme>;
  selectContainerStyle?: SxProps<Theme>;
};

const SelectionRow: React.FC<SelectionRowProps> = ({
  languagesList,
  proficiencyList,
  languages,
  setLanguages,
  index,
  isTransparentBorder = false,
  deleteStyle = {},
  languageStyle = {},
  proficiencyStyle = {},
  selectContainerStyle = {},
}) => {
  const selectedLanguage = languages[index]?.language?.name;
  const selectedLanguageProf = getProficiencyEn({
    data: languages[index]?.proficiency,
  });

  return (
    <Box sx={SelectRowContainer}>
      <Box sx={{ ...SelectContStyle, ...selectContainerStyle }}>
        <SelectLanguage
          languagesList={languagesList.filter((m: LanguageType) =>
            languages?.every(
              (f: SingleLanguageType) => f.language._id !== m._id
            )
          )}
          // languagesList={languagesList}
          selectedLanguage={selectedLanguage}
          isTransparentBorder={isTransparentBorder}
          selectStyle={languageStyle}
          onChange={(e) => {
            const selectedId = e.target.value;
            const findLanguage =
              languagesList.length > 0 &&
              languagesList?.find((f: LanguageType) => f._id === selectedId);
            const updatedLangList = languages.map((m, i) => {
              if (i === index) {
                return {
                  ...m,
                  language: findLanguage,
                };
              }
              return m;
            });
            setLanguages(updatedLangList);
          }}
        />
      </Box>
      <Box sx={{ ...SelectContStyle, ...selectContainerStyle }}>
        <SelectProficiency
          selectStyle={proficiencyStyle}
          proficiencyList={proficiencyList}
          selectedLanguageProf={selectedLanguageProf}
          isTransparentBorder={isTransparentBorder}
          onChange={(e) => {
            const selectedId = e.target.value;
            const findProficiency = proficiencyList?.find(
              (f: LanguageProficiencyType) => f._id === selectedId
            );
            const updatedLangList = languages.map((m, i) => {
              if (i === index) {
                return {
                  ...m,
                  proficiency: findProficiency,
                };
              }
              return m;
            });
            setLanguages(updatedLangList);
          }}
        />
      </Box>
      {languages.length !== 1 && (
        <Box
          sx={{
            cursor: "pointer",
            position: "absolute",
            right: {
              xs: 0,
              md: 10,
              lg: "6rem",
            },
            ...deleteStyle,
          }}
        >
          <DeleteIcon
            onClick={() => {
              setLanguages((prev: langugaeStateTypes) => {
                const newList = prev.filter(
                  (f: SingleLanguageType) =>
                    f.language?.name !== selectedLanguage
                );
                return newList;
              });
            }}
          />
        </Box>
      )}
    </Box>
  );
};

export default SelectionRow;
