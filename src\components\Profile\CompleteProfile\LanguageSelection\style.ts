const CustomButtonStyle = {
  width: {
    xs: "100%",
    sm: "50%",
    lg: "41.666667%",
  },
};

const BottonButtonContainer = {
  display: "flex",
  flexDirection: {
    xs: "column",
    md: "row",
  },
  width: "100%",
  alignItems: "center",
  gap: "1rem",
  justifyContent: "center",
  marginBottom: "1.5rem",
  marginTop: "1.5rem",
  paddingBottom: "5rem",
};

const ContainerStyle = {
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  padding: "1rem",
};

const ShowAddMoreContainerStyle = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "flex-start",
  width: {
    xs: "100%",
    lg: "66.666667%",
  },
  marginBottom: "0.25rem",
};

const SelectRowContainer = {
  display: "flex",
  flexDirection: {
    xs: "column",
    sm: "row",
  },
  width: "100%",
  alignItems: "center",
  rowGap: "3rem",
  justifyContent: "center",
  gap: "1rem",
  marginTop: "1.5rem",
  position: "relative",
};

const SelectContStyle = {
  width: {
    xs: "75%",
    sm: "41.666667%",
    lg: "33.333333%",
  },
};

const commonStyle = {
  borderRadius: {
    xs: 2,
    md: 4,
  },
  fontWeight: 600,
  height: {
    xs: "40px",
    md: "50px",
  },
  fontSize: {
    xs: "12px",
    md: "16px",
  },
  color: "rgba(109, 109, 109, 1)",
  boxShadow: "2px 2px 4px 4px rgba(0, 0, 0, 0.1)",
};

const SelectStyle = {
  width: "100%",
  height: "56px",
  backgroundColor: "white",
  borderRadius: "8px",
  boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
  marginTop: "2rem",
  "& .MuiOutlinedInput-notchedOutline": {
    border: "none",
  },
  "&:hover .MuiOutlinedInput-notchedOutline": {
    border: "none",
  },
  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
    border: "none",
  },
};

export {
  SelectContStyle,
  SelectRowContainer,
  ShowAddMoreContainerStyle,
  ContainerStyle,
  BottonButtonContainer,
  CustomButtonStyle,
  commonStyle,
  SelectStyle,
};
