import React from "react";
import CustomButton from "../../CustomButton";
import { Typography } from "@mui/material";

const TessaVideo = "/videos/TessaWelcomeWEB.webm";

type WelcomeSectionProps = {
  stageIx: number;
  setStageIx: React.Dispatch<React.SetStateAction<number>>;
};

const WelcomeSection: React.FC<WelcomeSectionProps> = ({
  stageIx,
  setStageIx,
}) => {
  const handleContinue = () => {
    setStageIx(+stageIx + 1);
  };

  return (
    <div>
      <Typography
        variant="h2"
        gutterBottom
        sx={{
          fontSize: { xs: "28px", sm: "32px", md: "48px" },
          fontWeight: "500",
          textAlign: "center",
        }}
      >
        Welcome
      </Typography>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
        }}
      >
        <video
          width="100%"
          autoPlay
          controls
          poster="/videos/TessaWelcomeWEB.webp"
        >
          <source src={TessaVideo} type="video/webm" />
        </video>
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          marginTop: "1.5rem",
          marginBottom: "1.5rem",
        }}
      >
        <CustomButton
          sx={{
            width: {
              xs: "100%",
              sm: "50%",
              lg: "33%",
            },
          }}
          text="Continue"
          onClick={handleContinue}
        />
      </div>
    </div>
  );
};

export default WelcomeSection;
