import React from "react";

const CartIcon = ({ size = 70 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 78 77"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M71.0811 9.25203C72.9715 9.25203 74.504 7.71955 74.504 5.82914C74.504 3.93873 72.9715 2.40625 71.0811 2.40625C69.1907 2.40625 67.6582 3.93873 67.6582 5.82914C67.6582 7.71955 69.1907 9.25203 71.0811 9.25203Z"
        fill="#F44336"
      />
      <path
        d="M48.6794 47.1504C48.6794 47.1504 54.4003 54.8504 54.4003 57.8883C54.4003 60.9262 51.9339 63.3926 48.896 63.3926C46.3033 63.3926 13.8851 63.3264 10.3058 63.3264C6.72648 63.3264 4.69922 65.4259 4.69922 65.4259"
        stroke="#84B0C1"
        stroke-width="2.95548"
        stroke-miterlimit="10"
        stroke-linecap="round"
      />
      <path
        d="M35.8896 46.3984C35.8896 46.3984 42.2662 54.8504 42.2662 57.8943C42.2662 60.9382 39.475 63.3805 36.4371 63.3805"
        stroke="#84B0C1"
        stroke-width="2.95548"
        stroke-miterlimit="10"
        stroke-linecap="round"
      />
      <path
        d="M50.2917 52.1737C49.0825 50.1043 48.102 48.9012 46.5259 46.7476C46.5259 46.7476 46.941 46.8378 48.0599 46.8378C49.1788 46.8378 49.9849 46.4829 49.9849 46.4829C51.573 48.7267 52.0482 49.2982 52.8964 50.8021C52.8964 50.8021 52.8664 51.7827 52.0843 52.3C51.3023 52.8174 50.2917 52.1737 50.2917 52.1737ZM38.1341 52.276C36.4557 49.7133 35.1143 47.7582 35.1143 47.7582L38.7236 47.8424C38.7236 47.8424 40.3418 50.0141 41.2682 51.7346C41.2682 51.7346 41.4066 52.5166 40.3719 52.9256C39.3372 53.3227 38.1341 52.276 38.1341 52.276Z"
        fill="#2F7889"
      />
      <path
        d="M48.6909 47.3066L6.61758 44.5034C6.47922 44.4913 6.37695 44.3831 6.37695 44.2447V20.7537C6.37695 20.5311 6.55141 20.3446 6.77398 20.3326L60.0905 16.9398C60.6078 16.9037 60.9808 17.421 60.7943 17.9023L49.4729 46.8134C49.3466 47.1322 49.0337 47.3307 48.6909 47.3066Z"
        stroke="#84B0C1"
        stroke-width="2.95548"
        stroke-miterlimit="10"
      />
      <path
        d="M14.7393 19.8878L13.6325 44.8166M24.2861 19.2381L20.8271 45.352M33.8389 18.5884L28.0218 45.8873M43.3918 17.9387L35.2165 46.4227M52.9386 17.2891L42.4112 46.9581M5.4873 29.1639L56.957 27.3953M5.96254 37.3632H53.7266"
        stroke="#84B0C1"
        stroke-width="1.77341"
        stroke-miterlimit="10"
      />
      <path
        d="M48.8717 74.5935C51.3768 74.5935 53.4075 72.5628 53.4075 70.0578C53.4075 67.5527 51.3768 65.522 48.8717 65.522C46.3667 65.522 44.3359 67.5527 44.3359 70.0578C44.3359 72.5628 46.3667 74.5935 48.8717 74.5935Z"
        fill="#424242"
      />
      <path
        d="M45.9482 63.814H51.8014L50.4058 69.8837C50.2735 70.8282 49.6298 71.514 48.8778 71.514C48.1259 71.514 47.4762 70.8282 47.3499 69.8837L45.9482 63.814Z"
        fill="#84B0C1"
      />
      <path
        d="M49.8104 70.0999C49.7021 70.4969 49.5036 70.9481 49.1005 71.0504C47.6448 71.4233 47.4402 67.844 48.1741 67.2304C48.6193 66.8575 49.5457 66.7913 49.9367 67.2545C50.436 67.844 49.9969 69.4442 49.8104 70.0999Z"
        fill="#A8E3F0"
      />
      <path
        d="M48.9437 65.9614C47.9751 65.9614 47.2894 65.3899 46.9525 65.035C46.9296 65.0114 46.914 64.9818 46.9076 64.9496C46.9012 64.9175 46.9042 64.8841 46.9162 64.8536C46.9283 64.8231 46.949 64.7968 46.9757 64.7777C47.0024 64.7586 47.034 64.7477 47.0668 64.7462C47.6924 64.7161 48.318 64.7221 48.9376 64.7041C49.5633 64.7221 50.1889 64.7161 50.8085 64.7462C50.9529 64.7522 51.0251 64.9327 50.9228 65.035C50.604 65.3899 49.9122 65.9614 48.9437 65.9614Z"
        fill="#2F7889"
      />
      <path
        d="M47.4458 71.5978C47.8368 72.0489 48.4624 72.2595 49.058 72.1873C49.6475 72.1151 50.1889 71.7662 50.5438 71.2849C50.7483 71.0022 50.8927 70.6774 51.1273 70.4187C51.3619 70.16 51.7349 69.9796 52.0598 70.1119C52.5169 70.2924 52.5771 70.93 52.4207 71.3932C52.269 71.856 52.0265 72.2837 51.7073 72.6515C51.3881 73.0192 50.9987 73.3195 50.5619 73.5348C49.9663 73.8296 49.1362 73.9499 48.4744 73.8837C47.2051 73.7514 45.8396 72.6024 45.3944 71.4233C45.1538 70.7917 44.9433 69.5224 46.2005 69.8111C46.784 69.9495 46.9765 71.0564 47.4458 71.5978Z"
        fill="#757575"
      />
      <path
        d="M60.5479 18.4739L63.3211 11.6943C64.452 8.92713 65.2882 8.30752 66.9665 7.55557L70.8947 5.96143"
        stroke="#84B0C1"
        stroke-width="2.95548"
        stroke-miterlimit="10"
        stroke-linecap="round"
      />
      <path
        d="M12.2907 74.5935C14.7957 74.5935 16.8264 72.5628 16.8264 70.0578C16.8264 67.5527 14.7957 65.522 12.2907 65.522C9.78562 65.522 7.75488 67.5527 7.75488 70.0578C7.75488 72.5628 9.78562 74.5935 12.2907 74.5935Z"
        fill="#424242"
      />
      <path
        d="M9.36133 63.814H15.2145L13.8189 69.8837C13.6866 70.8282 13.0429 71.514 12.2909 71.514C11.539 71.514 10.8893 70.8282 10.763 69.8837L9.36133 63.814Z"
        fill="#84B0C1"
      />
      <path
        d="M13.2293 70.0999C13.121 70.4969 12.9225 70.9481 12.5195 71.0504C11.0637 71.4233 10.8592 67.844 11.5931 67.2304C12.0382 66.8575 12.9646 66.7913 13.3556 67.2545C13.8549 67.844 13.4098 69.4442 13.2293 70.0999Z"
        fill="#A8E3F0"
      />
      <path
        d="M12.3626 65.9614C11.3941 65.9614 10.7083 65.3899 10.3714 65.035C10.3486 65.0114 10.333 64.9818 10.3265 64.9496C10.3201 64.9175 10.3231 64.8841 10.3352 64.8536C10.3473 64.8231 10.3679 64.7968 10.3946 64.7777C10.4213 64.7586 10.453 64.7477 10.4857 64.7462C11.1114 64.7161 11.737 64.7221 12.3566 64.7041C12.9822 64.7221 13.6078 64.7161 14.2274 64.7462C14.3718 64.7522 14.444 64.9327 14.3417 65.035C14.0169 65.3899 13.3251 65.9614 12.3626 65.9614Z"
        fill="#2F7889"
      />
      <path
        d="M10.8589 71.5978C11.2499 72.0489 11.8755 72.2595 12.471 72.1873C13.0606 72.1151 13.602 71.7662 13.9569 71.2849C14.1614 71.0022 14.3058 70.6774 14.5404 70.4187C14.775 70.16 15.148 69.9796 15.4728 70.1119C15.93 70.2924 15.9902 70.93 15.8338 71.3932C15.6821 71.856 15.4396 72.2837 15.1204 72.6515C14.8012 73.0192 14.4117 73.3195 13.9749 73.5348C13.3794 73.8296 12.5492 73.9499 11.8875 73.8837C10.6182 73.7514 9.25268 72.6024 8.80752 71.4233C8.5669 70.7917 8.35635 69.5224 9.61362 69.8111C10.1971 69.9495 10.3896 71.0564 10.8589 71.5978Z"
        fill="#757575"
      />
    </svg>
  );
};

export default CartIcon;
