import React, { useEffect, useState } from "react";
import { Container, Skeleton, Typography } from "@mui/material";
import Image from "next/image";
import { useSnackbar } from "@/hooks/useSnackbar";
import CustomButton from "../CustomButton";
import { GoalType } from "@/api/mongoTypes";

type goalsType = string[];
type setGoalsType = React.Dispatch<React.SetStateAction<goalsType>>;

type GoalSelectionProps = {
  goals: goalsType;
  data: GoalType[];
  setGoals: setGoalsType;
  setStageIx: React.Dispatch<React.SetStateAction<number>>;
  stageIx: number;
};

const getBorderColor = (index: number) => {
  if (index === 0) {
    return "rgba(255, 213, 128, 1)";
  }
  if (index === 1) {
    return "rgba(0, 95, 86, 1)";
  }
  if (index === 2) {
    return "rgba(241, 100, 37, 1)";
  }
  if (index === 3) {
    return "rgba(114, 202, 196, 1)";
  }
  if (index === 4) {
    return "rgba(128, 175, 112, 1)";
  }
  return "rgba(0, 95, 86, 1)";
};

const GoalsSection: React.FC<GoalSelectionProps> = ({
  goals,
  setGoals,
  stageIx,
  data,
  setStageIx,
}) => {
  const { showSnackbar } = useSnackbar();
  // const [d, setData] = useState<GoalType[]>([]);

  const handleContinue = () => {
    if (goals.length === 0) {
      showSnackbar("Please select goal", {
        type: "warning",
      });
    } else {
      setStageIx(+stageIx + 1);
    }
  };

  const handleDoItLater = () => {
    setStageIx(+stageIx + 1);
  };

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        padding: "1rem",
      }}
    >
      <Typography
        variant="h2"
        component="div"
        sx={{
          textAlign: "center",
          fontWeight: "600",
          fontSize: {
            xs: "1.875rem",
            md: "3rem",
          },
          marginTop: {
            xs: 0,
            sm: 0,
            md: "4rem",
          },
        }}
      >
        Goals
      </Typography>
      <Typography
        variant="h5"
        component="div"
        sx={{
          textAlign: "center",
          fontWeight: "500",
          fontSize: {
            xs: "0.875rem",
            md: "1rem",
          },
          marginTop: {
            xs: "0.5rem",
            md: "1rem",
          },
        }}
      >
       What brings you here?
      </Typography>

      <Container
        sx={{
          display: "flex",
          marginTop: "1rem",
          flexWrap: {
            xs: "nowrap",
            sm: "wrap",
          },
          flexDirection: {
            xs: "column",
            sm: "row",
          },
          width: {
            xs: "100%",
            md: "83.333333%",
            lg: "66.666667%",
          },
        }}
      >
        {data?.map((m, i) => (
          <GoalCard
            setGoals={setGoals}
            data={m}
            index={i}
            goals={goals}
            key={m.name}
          />
        ))}
      </Container>
      <Container
        sx={{
          display: "flex",
          flexDirection: {
            xs: "column",
            md: "row",
          },
          width: "100%",
          alignItems: "center",
          justifyContent: "center",
          gap: "1rem",
          marginBottom: "1.5rem",
          paddingBottom: {
            xs: "0rem",
            md: "4rem",
          },
        }}
      >
        <CustomButton
          text="Do it later"
          colortype="secondary"
          onClick={handleDoItLater}
          sx={{
            width: {
              xs: "100%",
              sm: "50%",
              lg: "33%",
            },
          }}
        />
        <CustomButton
          text="Continue"
          sx={{
            width: {
              xs: "100%",
              sm: "50%",
              lg: "33%",
            },
          }}
          onClick={handleContinue}
        />
      </Container>
    </div>
  );
};

type GoalCardProps = React.FC<{
  setGoals: setGoalsType;
  data: GoalType;
  index: number;
  goals: goalsType;
}>;
const GoalCard: GoalCardProps = ({ setGoals, data, index, goals }) => {
  const activeGoal = goals.includes(data?._id);

  return (
    <Container
      key={data.name}
      onClick={() => {
        if (activeGoal) {
          setGoals((prev) => prev.filter((f) => f !== data._id));
        } else {
          setGoals((prev) => [...prev, data._id]);
        }
      }}
      sx={{
        display: "flex",
        flexDirection: "column",
        width: {
          xs: "80%",
          sm: "41.666667%",
        },
        transform: activeGoal && `scale(1.1)`,
        alignItems: "center",
        justifyContent: "center",
        padding: "1rem",
        cursor: "pointer",
      }}
    >
      <span
        style={{
          border: `4px solid ${getBorderColor(index)}`,
          borderRadius: "0.75rem",
          width: "100%",
          height: "80px",
          overflow: "hidden",
          position: "relative",
        }}
      >
        <Image
          src={data.image}
          alt={`Subject ${data.name}`}
          key={data.name}
          quality={100}
          style={{
            objectFit: "cover",
            objectPosition: "center",
          }}
          fill
        />
      </span>
      <Typography
        variant="h5"
        sx={{
          textAlign: "center",
          marginTop: "0.5rem",
          fontSize: "0.875rem",
          fontWeight: "600",
        }}
      >
        {data.name}
        &nbsp;
        {activeGoal && <GoalSelected />}
      </Typography>
    </Container>
  );
};

const GoalSelected = () => {
  return (
    <svg
      width="11"
      height="11"
      viewBox="0 0 117 117"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g fill="none">
        <path
          d="M34.5 55.1c-1.6-1.6-4.2-1.6-5.8 0s-1.6 4.2 0 5.8l18.9 18.9c.8.8 1.8 1.2 2.9 1.2h.2c1.1-.1 2.2-.6 3-1.5L101 22.8c1.4-1.7 1.2-4.3-.5-5.8-1.7-1.4-4.3-1.2-5.8.5L50.2 70.8z"
          fill="#17AB13"
        />
        <path
          d="M89.1 9.3c-23-14.4-52.5-11-71.7 8.2-22.6 22.6-22.6 59.5 0 82.1a57.94 57.94 0 0 0 82 0c19.3-19.3 22.6-48.9 8.1-71.9-1.2-1.9-3.7-2.5-5.6-1.3s-2.5 3.7-1.3 5.6c12.5 19.8 9.6 45.2-7 61.8-19.4 19.4-51.1 19.4-70.5 0s-19.4-51.1 0-70.5C39.7 6.8 65 3.9 84.8 16.2c1.9 1.2 4.4.6 5.6-1.3s.6-4.4-1.3-5.6"
          fill="#4A4A4A"
        />
      </g>
    </svg>
  );
};

const GoalsLoading = () => {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        padding: "1rem",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          width: "100%",
          marginTop: "0.5rem",
        }}
      >
        <Skeleton variant="rectangular" width="13%" height={60} />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          width: "100%",
          marginTop: "1rem",
        }}
      >
        <Skeleton variant="rectangular" width="18%" height={20} />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          width: "100%",
          marginTop: "1rem",
        }}
      >
        <Skeleton variant="rectangular" width="15%" height={20} />
      </div>
      <Container
        sx={{
          display: "flex",
          width: "100%",
          padding: "1rem",
          alignItems: "center",
          justifyContent: "center",
          gap: "0.5rem",
          flexDirection: {
            xs: "column",
            sm: "row",
          },
          flexWrap: {
            xs: "nowrap",
            sm: "wrap",
          },
        }}
      >
        {new Array(5).fill("")?.map((m, i) => (
          <Container
            key={m.name}
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              width: {
                xs: "100%",
                sm: "41.666667%",
                md: "33.333333%",
              },
              padding: "1rem",
              cursor: "pointer",
            }}
          >
            <Container
              sx={{
                position: "relative",
                overflow: "hidden",
                borderRadius: "0.75rem",
                width: { xs: "100%", sm: "200px", md: "275px" },
                height: {
                  xs: "150px",
                  sm: "70px",
                  md: "120px",
                },
              }}
            >
              <Skeleton variant="rectangular" width="100%" height="100%" />
            </Container>
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                width: "100%",
                marginTop: "0.5rem",
              }}
            >
              <Skeleton variant="rectangular" width="15%" height={20} />
            </div>
          </Container>
        ))}
      </Container>
      <Container
        sx={{
          display: "flex",
          flexDirection: {
            xs: "column",
            md: "row",
          },
          width: "100%",
          alignItems: "center",
          justifyContent: "center",
          gap: "1rem",
          marginBottom: "1.5rem",
          paddingBottom: "5rem",
        }}
      >
        <Skeleton
          sx={{
            width: {
              xs: "100%",
              sm: "50%",
              lg: "33%",
            },
          }}
          variant="rectangular"
          width={210}
          height={60}
        />
        <Skeleton
          sx={{
            width: {
              xs: "100%",
              sm: "50%",
              lg: "33%",
            },
          }}
          variant="rectangular"
          width={210}
          height={60}
        />
      </Container>
    </div>
  );
};

export default GoalsSection;
