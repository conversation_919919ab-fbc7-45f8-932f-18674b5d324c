import { INTEREST_TYPES } from "@/constant/Enums";
import { ValueOf } from "@/types";
import React from "react";

type InterestsIconProps = React.FC<{
  type: ValueOf<typeof INTEREST_TYPES>;
  size?: number;
  color?: string;
}>;
const InterestsIcon: InterestsIconProps = ({
  type = INTEREST_TYPES.HATE,
  size = 20,
  color = "#fff",
}) => {
  const SIZE = size;
  const dimensions = {
    height: SIZE,
    width: SIZE,
  };

  return (
    <svg
      {...dimensions}
      viewBox="0 0 25 25"
      style={{ height: "16px" }}
      fill="none"
    >
      {type === INTEREST_TYPES.HATE ? (
        <>
          <g clip-path="url(#a)" fill={color}>
            <path d="M16 11.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3m-7 0a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3" />
            <path d="M12.49 2.5c-5.52 0-9.99 4.48-9.99 10s4.47 10 9.99 10c5.53 0 10.01-4.48 10.01-10s-4.48-10-10.01-10m.01 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8m0-6c-2.33 0-4.32 1.45-5.12 3.5h1.67c.69-1.19 1.97-2 3.45-2s2.75.81 3.45 2h1.67c-.8-2.05-2.79-3.5-5.12-3.5" />
          </g>
          <defs>
            <clipPath id="a">
              <path fill={color} d="M.5.5h24v24H.5z" />
            </clipPath>
          </defs>
        </>
      ) : type === INTEREST_TYPES.NEUTRAL ? (
        <>
          <g clip-path="url(#a)" fill={color}>
            <path d="M9 16h6v1H9zm6.5-4.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3m-7 0a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3" />
            <path d="M11.99 2.5C6.47 2.5 2 6.98 2 12.5s4.47 10 9.99 10c5.53 0 10.01-4.48 10.01-10s-4.48-10-10.01-10m.01 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8" />
          </g>
          <defs>
            <clipPath id="a">
              <path fill={color} d="M0 .5h24v24H0z" />
            </clipPath>
          </defs>
        </>
      ) : (
        <>
          <g clip-path="url(#a)" fill={color}>
            <path d="M9 16h6v1H9zm6.5-4.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3m-7 0a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3" />
            <path d="M11.99 2.5C6.47 2.5 2 6.98 2 12.5s4.47 10 9.99 10c5.53 0 10.01-4.48 10.01-10s-4.48-10-10.01-10m.01 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8" />
          </g>
          <defs>
            <clipPath id="a">
              <path fill={color} d="M0 .5h24v24H0z" />
            </clipPath>
          </defs>
        </>
      )}
    </svg>
  );
};

export default InterestsIcon;
