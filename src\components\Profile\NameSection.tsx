import React from "react";
import { <PERSON>, Container, TextField, Typography } from "@mui/material";
import { useSnackbar } from "@/hooks/useSnackbar";
import CustomButton from "../CustomButton";

type NameSectionProps = React.FC<{
  firstName: string;
  setFirstName: React.Dispatch<React.SetStateAction<string>>;
  lastName: string;
  setLastName: React.Dispatch<React.SetStateAction<string>>;
  stageIx: number;
  setStageIx: React.Dispatch<React.SetStateAction<number>>;
}>;

const buttonContainerStyle = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  gap: "0.75rem",
  width: "100%",
  marginTop: "2rem",
  marginBottom: "2rem",
  flexWrap: {
    xs: "wrap",
    sm: "nowrap",
  },
};

const buttonStyle = {
  width: {
    xs: "100%",
    sm: "50%",
    lg: "33%",
  },
};

const NameSection: NameSectionProps = ({
  firstName,
  setFirstName,
  lastName,
  setLastName,
  stageIx,
  setStageIx,
}) => {
  const { showSnackbar } = useSnackbar();

  const handleDoItLater = () => {
    setStageIx(+stageIx + 1);
    setLastName("");
    setFirstName("");
  };

  const handleContinue = () => {
    if (!firstName) {
      showSnackbar("First name cannot be empty", {
        type: "warning",
      });
      return;
    }
    if (firstName.length < 1) {
      showSnackbar("First name is too small", {
        type: "warning",
      });
      return;
    }
    if (!lastName) {
      showSnackbar("Last name cannot be empty", {
        type: "warning",
      });
      return;
    }
    if (lastName.length < 1) {
      showSnackbar("last name is too small", {
        type: "warning",
      });
      return;
    }
    setStageIx(+stageIx + 1);
  };

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        width: "100%",
        height: "100%",
        alignItems: "center",
      }}
    >
      <Card
        sx={{
          flexDirection: "column",
          boxShadow: "2px 2px 7px 4px rgba(204, 204, 204, 0.54)",
          padding: 10,
          rowGap: 5,
          borderRadius: 5,
          display: "flex",
          width: {
            xs: "100%",
            sm: "85%",
            lg: "75%",
          },
        }}
      >
        <Container
          sx={{
            marginBottom: "1rem",
            width: {
              xs: "66.666667%",
              sm: "83.333333%",
              md: "66.666667%",
            },
          }}
        >
          <Typography
            variant="h5"
            component="div"
            sx={{
              textAlign: "center",
              fontWeight: "600",
              fontSize: {
                xs: "11px",
                sm: "14px",
                md: "16px",
              },
            }}
          >
            Add your first and last name to personalize your profile and make it
            yours.
          </Typography>
        </Container>
        <TextField
          placeholder="Enter First Name"
          variant="outlined"
          fullWidth
          type="text"
          value={firstName}
          onChange={(e) => setFirstName(e.target.value)}
        />
        <TextField
          placeholder="Enter last Name"
          variant="outlined"
          fullWidth
          type="text"
          value={lastName}
          onChange={(e) => setLastName(e.target.value)}
        />
      </Card>

      <Container sx={buttonContainerStyle}>
        {/* <CustomButton
          text="Do it later"
          onClick={handleDoItLater}
          colortype="secondary"
          sx={buttonStyle}
        /> */}
        <CustomButton
          text="Continue"
          sx={buttonStyle}
          onClick={handleContinue}
        />
      </Container>
    </div>
  );
};

export default NameSection;
