import React from "react";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import logo from "@/../public/images/icons/patito-feo.svg";

const NoUserData = () => {
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      height="100%"
    >
      <Box>
        <Image alt="Logo" src={logo} height={80} width={80} />
      </Box>
      <Typography variant="h6" color="error" mb={2}>
        No user data exists
      </Typography>
      <Typography variant="body2" color="text.secondary">
        Please check your network connection or contact support if the issue
        persists.
      </Typography>
    </Box>
  );
};

export default NoUserData;
