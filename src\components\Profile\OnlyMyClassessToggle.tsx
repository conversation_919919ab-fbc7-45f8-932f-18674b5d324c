import { Box, Switch, Typography } from "@mui/material";
import React from "react";

type OnlyMyClassessToggleProps = React.FC<{
  isMyClassesOnly: boolean;
  smallerText?: boolean;
  setIsMyClassessOnly: React.Dispatch<React.SetStateAction<boolean>>;
}>;
const OnlyMyClassessToggle: OnlyMyClassessToggleProps = ({
  isMyClassesOnly,
  setIsMyClassessOnly,
  smallerText = false,
}) => {
  return (
    <Box display="flex" flexDirection="row" alignItems="center">
      <Typography
        color={smallerText ? "rgba(60, 60, 60, 1)" : "#000"}
        fontSize={smallerText ? 14 : null}
        fontWeight={700}
      >
        Display My Classes/Events Only
      </Typography>
      <Switch
        checked={isMyClassesOnly}
        onChange={(event) => {
          setIsMyClassessOnly(event.target.checked);
        }}
        inputProps={{ "aria-label": "controlled" }}
      />
    </Box>
  );
};

export default OnlyMyClassessToggle;
