import React, { useEffect } from "react";
import { useUserContext } from "../../contexts/UserContext";
import { Box, Container, Skeleton } from "@mui/material";
import NoUserData from "./NoUserData";
import Retrying from "./Retrying";

const ProfileComponent = ({ children, stageIx, setStageIx }) => {
  const { user, isLoaded, isfetching, dbUser, isRetrying } = useUserContext();
  const totalChildren = React.Children.count(children);

  let noUser = !user && !dbUser;
  let clerkAvailableButNoMongodb = user && !dbUser && !isRetrying;
  let clerkAvailableMongoRetrying = user && !dbUser && isRetrying;

  useEffect(() => {
    noUser = !user && !dbUser;
    clerkAvailableButNoMongodb = user && !dbUser && !isRetrying;
    clerkAvailableMongoRetrying = user && !dbUser && isRetrying;
  }, [user, dbUser]);

  if (!isLoaded || isfetching) {
    return <WelcomeLoading />;
  }

  return (
    <Container
      sx={{
        marginTop: "2rem",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        padding: { xs: "1rem", sm: "2rem", md: "4rem" },
        minHeight: "85vh",
      }}
    >
      {clerkAvailableMongoRetrying ? (
        <Retrying isRetrying />
      ) : clerkAvailableButNoMongodb ? (
        <Retrying isRetrying={false} />
      ) : noUser ? (
        <NoUserData />
      ) : (
        <>
          <Container
            sx={{
              width: "100%",
              display: "flex",
              flexDirection: "column",
              height: "100%",
              borderRadius: "1rem",
              boxShadow:
                stageIx === 3 || stageIx === 4
                  ? "2px 2px 7px 4px rgba(204, 204, 204, 0.54)"
                  : "",
              marginBottom: stageIx === 3 || stageIx === 4 ? 30 : 0,
            }}
          >
            {React.Children.map(children, (child, index) => {
              if (index === stageIx) {
                return React.cloneElement(child, { stageIx, setStageIx });
              }
              return null;
            })}
          </Container>
          {stageIx !== totalChildren - 1 && (
            <Container
              sx={{
                width: "100%",
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
              }}
            >
              <Steps
                steps={React.Children.toArray(children)}
                step={stageIx}
                setStep={setStageIx}
              />
            </Container>
          )}
        </>
      )}
    </Container>
  );
};

const Steps = ({ steps, step, setStep, disabled = false }) => {
  const stepsContainerStyle = {
    display: "flex",
    alignItems: "center",
  };

  const stepWrapperStyle = {
    display: "flex",
    alignItems: "center",
  };

  return (
    <div style={stepsContainerStyle} aria-disabled={disabled}>
      {Array.from(steps)
        .slice(0, steps.length - 1)
        .map((s, index) => (
          <div key={index} style={stepWrapperStyle}>
            <Step
              isVisited={index < step}
              isActive={step === index}
              setStep={setStep}
              onClick={() => {
                setStep(index);
              }}
            />
            <StepLine show={index < steps.length - 2} />
          </div>
        ))}
    </div>
  );
};

const Step = ({ isActive, isVisited, ...props }) => {
  return (
    <Box
      {...props}
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        cursor: "pointer",
        borderRadius: "9999px",
        padding: "0px !important",
        height: {
          xs: "10px",
          md: "14px",
        },
        width: {
          xs: "10px",
          md: "14px",
        },
        border: `3px solid ${
          isVisited
            ? "rgba(250, 193, 96, 1)"
            : !isActive
            ? "#72CAC4"
            : "#F16425"
        }`,
        backgroundColor: isActive
          ? "#FAC160"
          : isVisited
          ? "rgba(251, 209, 136, 1)"
          : "#fff",
      }}
    >
      &nbsp;
    </Box>
  );
};

const StepLine = ({ show = false }) => {
  if (show) {
    return (
      <Container
        sx={{
          backgroundColor: "#72CAC4",
          margin: "0px",
          height: "3px",
          width: {
            xs: "20px",
            sm: "30px",
            md: "40px",
            lg: "50px",
          },
        }}
      ></Container>
    );
  }
  return null;
};

const WelcomeLoading = () => {
  return (
    <Container
      sx={{
        marginTop: "5rem",
      }}
    >
      <Container
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Skeleton
          variant="rectangular"
          width="15%"
          height={70}
          sx={{
            marginBottom: "2.5rem",
          }}
        />
        <Skeleton variant="rectangular" width="60%" height={500} />
      </Container>
      <Container
        sx={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          marginTop: "1.5rem",
          marginBottom: "1.5rem",
        }}
      >
        <Skeleton
          variant="rectangular"
          width={210}
          height={60}
          sx={{
            width: {
              xs: "100%",
              sm: "50%",
              lg: "33.333333%",
            },
            marginBottom: "2.5rem",
          }}
        />
      </Container>
    </Container>
  );
};

export default ProfileComponent;
