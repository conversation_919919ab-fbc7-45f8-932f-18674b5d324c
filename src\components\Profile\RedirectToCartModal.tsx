import { Box, Modal, Typography } from "@mui/material";
import React from "react";
import CustomButton from "../CustomButton";
import Image from "next/image";
import { NextRouter } from "next/router";
import {
  getLocalBuyClassDetaills,
  getLocalBuyEventDetails,
  handleReturnToCart,
} from "@/utils/classes";
import { CLASSES_TYPE } from "@/constant/Enums";

type RedirectToCartModalProps = React.FC<{
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  router: NextRouter;
}>;
const RedirectToCartModal: RedirectToCartModalProps = ({
  open,
  setOpen,
  router,
}) => {
  const handleClose = () => {
    setOpen((prev) => !prev);
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: {
            xs: "90%",
            sm: 350,
            md: 350,
          },
          maxWidth: 350,
          bgcolor: "background.paper",
          boxShadow: 24,
          borderRadius: 2,
          p: 4,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Image
          src="/images/cart/cart.webp"
          height={200}
          width={170}
          alt="cart"
        />
        <Typography
          color="rgba(20, 167, 156, 1)"
          fontSize="1.2rem"
          fontWeight="800"
          mt={4}
          textAlign="center"
        >
          Items Waiting in Your Cart!
        </Typography>
        <Typography textAlign="center" fontSize="0.9rem" mt={4}>
          You have a pending purchase.
        </Typography>
        <Typography fontSize="0.9rem" textAlign="center" mt={2}>
          Return to your cart or continue profile creation?
        </Typography>
        <CustomButton
          text="Continue Profile Creation"
          onClick={() => {
            handleClose();
          }}
          sx={{ width: "100%", mb: 3, mt: 3 }}
        />
        <CustomButton
          text="Return to Cart"
          onClick={() => {
            handleReturnToCart(router);
            handleClose();
          }}
          colortype="secondary"
          sx={{ width: "100%" }}
        />
      </Box>
    </Modal>
  );
};

export default RedirectToCartModal;
