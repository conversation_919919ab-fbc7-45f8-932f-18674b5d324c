import { Box, keyframes, Typography } from "@mui/material";
import Image from "next/image";
import React from "react";
import logo from "@/../public/images/icons/patito-feo.svg";

const rotate = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

type RetryingProps = React.FC<{
  isRetrying: boolean;
}>;
const Retrying: RetryingProps = ({ isRetrying = false }) => {
  if (isRetrying) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        height="100%"
      >
        <Box
          sx={{
            animation: `${rotate} 5s linear infinite`,
          }}
        >
          <Image alt="Logo" src={logo} height={80} width={80} />
        </Box>
        <Typography variant="body1" mt={2} color="text.secondary">
          Fetching user data... please wait.
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      height="100%"
    >
      <Box>
        <Image alt="Logo" src={logo} height={80} width={80} />
      </Box>
      <Typography variant="h6" color="error" mb={2}>
        Unable to fetch user data.
      </Typography>
      <Typography variant="body2" color="text.secondary">
        Please check your network connection or contact support if the issue
        persists.
      </Typography>
    </Box>
  );
};

export default Retrying;
