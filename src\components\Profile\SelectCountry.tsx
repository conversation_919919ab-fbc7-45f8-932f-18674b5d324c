import React, { useEffect, useMemo, useState } from "react";
import {
  Autocomplete,
  Box,
  FormControl,
  Typography,
  TextField,
} from "@mui/material";

const SelectCountry = ({
  country,
  setCountry,
  countriesList,
  containerStyle = {},
  countryButtonStyle = {},
  isManageProfile = false,
}) => {
  const [inputValue, setInputValue] = useState("");
  const selectedCountryLogic = isManageProfile ? false : !country;

  useEffect(() => {
    const sel = countriesList?.find((c) => c._id === country);
    setInputValue(sel ? sel.name : "");
  }, [country, countriesList]);

  const options = useMemo(() => {
    if (!Array.isArray(countriesList)) return [];
    const seen = new Set();
    return countriesList.filter((c) => {
      if (!c || !c.name) return false;
      if (seen.has(c.name)) return false;
      seen.add(c.name);
      return true;
    });
  }, [countriesList]);

  if (!options || options.length === 0 || selectedCountryLogic) {
    return null;
  }

  return (
    <Box
      sx={{
        display: "inline-flex",
        alignItems: "stretch",
        backgroundColor: "#fff",
        borderRadius: "16px",
        boxShadow: "0px 2px 14px rgba(0, 0, 0, 0.08)",
        width: "100%",
        ...containerStyle,
      }}
    >
      <Box
        sx={{
          backgroundColor: "#14A79C",
          padding: "12px 24px",
          display: "flex",
          borderTopLeftRadius: "16px",
          borderBottomLeftRadius: "16px",
          alignItems: "center",
          ...countryButtonStyle,
        }}
      >
        <Typography
          variant="body1"
          sx={{
            color: "#fff",
            fontSize: "16px",
            fontWeight: 500,
          }}
        >
          Country
        </Typography>
      </Box>

      <FormControl sx={{ width: "100%" }}>
        <Autocomplete
          disablePortal
          options={options}
          value={options.find((c) => c._id === country) || null}
          onChange={(event, newValue) => {
            setCountry(newValue ? newValue._id : "");
          }}
          inputValue={inputValue}
          onInputChange={(event, newInputValue) => {
            setInputValue(newInputValue);
          }}
          getOptionLabel={(option) => {
            if (!option) return "";
            return typeof option === "string" ? option : option.name || "";
          }}
          isOptionEqualToValue={(option, value) => {
            if (!option || !value) return false;
            if (typeof option === "string" || typeof value === "string") {
              return option === value;
            }
            return option._id === value._id;
          }}
          filterOptions={(opts, { inputValue: iv }) => {
            const q = (iv || "").trim().toLowerCase();
            if (!q) return opts;
            return opts.filter((o) => {
              const name = typeof o === "string" ? o : o?.name || "";
              return name.toLowerCase().includes(q);
            });
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Select Country"
              size="small"
              sx={{
                "& .MuiOutlinedInput-root": {
                  "& fieldset": { border: "none", height: "100%" },
                  height: "100%",
                },
                p: 0,
                height: "100%",
              }}
            />
          )}
          sx={{
            borderTopRightRadius: "16px",
            borderBottomRightRadius: "16px",
            height: "100%",
          }}
        />
      </FormControl>
    </Box>
  );
};

export default SelectCountry;
