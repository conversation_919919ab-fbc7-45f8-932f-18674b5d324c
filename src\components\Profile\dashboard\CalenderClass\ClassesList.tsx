import { Box, Typography } from "@mui/material";
import React from "react";
import Link from "next/link";
import ScheduleCard from "../../schedule/ScheduleCard";
import EmptyList from "../../EmptyInfo/EmptyList";
import { getEmptyClassesTitleAndDesc } from "@/utils/dashboard";
import { formatDate } from "@/utils/dateTime";
import { MySchedulesResponse } from "@/types";
import { CLASSESS_FETCH_DURATION } from "@/constant/Enums";

type ClassesListProps = React.FC<{
  list: MySchedulesResponse;
  selectedDate: Date;
}>;
const ClassesList: ClassesListProps = ({ list, selectedDate }) => {
  const { description, title } = getEmptyClassesTitleAndDesc({ type: 2 });

  return (
    <Box mt={2}>
      <Typography fontWeight={600} fontSize={13}>
        {formatDate({
          date: selectedDate,
        })}
      </Typography>

      {list.map((m, i) => (
        <ScheduleCard
          active={CLASSESS_FETCH_DURATION.ALL}
          selectedDate={selectedDate}
          info={m}
          key={i}
          isSmaller
        />
      ))}
      {list?.length === 0 && (
        <EmptyList title={title} description={description} isSmall />
      )}
      {list?.length > 0 && <SeeAll />}
    </Box>
  );
};

const SeeAll = () => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="center"
      mt={2}
    >
      <Link
        style={{ color: "rgba(20, 167, 156, 1)", fontWeight: "bold" }}
        href="/profile/schedule"
      >
        See all
      </Link>
    </Box>
  );
};

export default ClassesList;
