import { <PERSON>, <PERSON><PERSON>, Card, CardContent, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import WeekStrip from "./WeekStrip";
import ClassesList from "./ClassesList";
import Link from "next/link";
import { CLASSESS_FETCH_DURATION } from "@/constant/Enums";
import axios from "axios";
import Loading from "./Loading";
import OnlyMyClassessToggle from "../../OnlyMyClassessToggle";
import { getDateAsPerUTC } from "@/utils/dateTime";
import { useUserContext } from "@/contexts/UserContext";

const CalenderClass = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [list, setList] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isMyClassesOnly, setIsMyClassessOnly] = useState(false);
  const { dbUser } = useUserContext();
  const userId = dbUser?._id;

  useEffect(() => {
    const fetchSchedulesByDate = async (userId) => {
      try {
        setIsLoading(true);
        const { data: respdata } = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/schedule/my-schedules`,
          {
            params: {
              timeFilter: CLASSESS_FETCH_DURATION.ALL,
              skip: 0,
              limit: 2,
              isMyClassesOnly: isMyClassesOnly,
              filters: "",
              isAscending: true,
              // date: selectedDate,
              date: getDateAsPerUTC(selectedDate),
              currentDate: new Date(),
              timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
              // currentDate: getDateAsPerUTC(new Date()),
            },
            headers: {
              userid: userId,
            },
          }
        );
        if (respdata.success) {
          setList(respdata.data);
        } else {
          setList([]);
        }
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        setList([]);
      }
    };

    if (userId) {
      fetchSchedulesByDate(userId);
    }
  }, [selectedDate, isMyClassesOnly, userId]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <>
      <Card
        sx={{
          padding: 3,
          borderRadius: 2,
          boxShadow: "0px 1px 20px 0px rgba(0, 0, 0, 0.12)",
          gap: 2,
          display: {
            xs: "none",
            md: "flex",
          },
        }}
      >
        <Box width="100%">
          <WeekStrip
            selectedDate={selectedDate}
            setSelectedDate={setSelectedDate}
          />
          <OnlyMyClassessToggle
            smallerText
            isMyClassesOnly={isMyClassesOnly}
            setIsMyClassessOnly={setIsMyClassessOnly}
          />
          <ClassesList list={list.slice(0, 2)} selectedDate={selectedDate} />
        </Box>
      </Card>
      <Box
        sx={{
          display: {
            xs: "flex",
            md: "none",
          },
          my: 4,
        }}
      >
        <Link href="/profile/schedule" style={{ width: "100%" }}>
          <Button sx={{ width: "100%", color: "#000" }}>
            <CalenderIcon /> &nbsp; View Schedule
          </Button>
        </Link>
      </Box>
    </>
  );
};

const CalenderIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.1667 11.6667C14.3877 11.6667 14.5996 11.5789 14.7559 11.4226C14.9122 11.2663 15 11.0543 15 10.8333C15 10.6123 14.9122 10.4004 14.7559 10.2441C14.5996 10.0878 14.3877 10 14.1667 10C13.9457 10 13.7337 10.0878 13.5774 10.2441C13.4211 10.4004 13.3333 10.6123 13.3333 10.8333C13.3333 11.0543 13.4211 11.2663 13.5774 11.4226C13.7337 11.5789 13.9457 11.6667 14.1667 11.6667ZM14.1667 15C14.3877 15 14.5996 14.9122 14.7559 14.7559C14.9122 14.5996 15 14.3877 15 14.1667C15 13.9457 14.9122 13.7337 14.7559 13.5774C14.5996 13.4211 14.3877 13.3333 14.1667 13.3333C13.9457 13.3333 13.7337 13.4211 13.5774 13.5774C13.4211 13.7337 13.3333 13.9457 13.3333 14.1667C13.3333 14.3877 13.4211 14.5996 13.5774 14.7559C13.7337 14.9122 13.9457 15 14.1667 15ZM10.8333 10.8333C10.8333 11.0543 10.7455 11.2663 10.5893 11.4226C10.433 11.5789 10.221 11.6667 10 11.6667C9.77899 11.6667 9.56702 11.5789 9.41074 11.4226C9.25446 11.2663 9.16667 11.0543 9.16667 10.8333C9.16667 10.6123 9.25446 10.4004 9.41074 10.2441C9.56702 10.0878 9.77899 10 10 10C10.221 10 10.433 10.0878 10.5893 10.2441C10.7455 10.4004 10.8333 10.6123 10.8333 10.8333ZM10.8333 14.1667C10.8333 14.3877 10.7455 14.5996 10.5893 14.7559C10.433 14.9122 10.221 15 10 15C9.77899 15 9.56702 14.9122 9.41074 14.7559C9.25446 14.5996 9.16667 14.3877 9.16667 14.1667C9.16667 13.9457 9.25446 13.7337 9.41074 13.5774C9.56702 13.4211 9.77899 13.3333 10 13.3333C10.221 13.3333 10.433 13.4211 10.5893 13.5774C10.7455 13.7337 10.8333 13.9457 10.8333 14.1667ZM5.83333 11.6667C6.05435 11.6667 6.26631 11.5789 6.42259 11.4226C6.57887 11.2663 6.66667 11.0543 6.66667 10.8333C6.66667 10.6123 6.57887 10.4004 6.42259 10.2441C6.26631 10.0878 6.05435 10 5.83333 10C5.61232 10 5.40036 10.0878 5.24408 10.2441C5.0878 10.4004 5 10.6123 5 10.8333C5 11.0543 5.0878 11.2663 5.24408 11.4226C5.40036 11.5789 5.61232 11.6667 5.83333 11.6667ZM5.83333 15C6.05435 15 6.26631 14.9122 6.42259 14.7559C6.57887 14.5996 6.66667 14.3877 6.66667 14.1667C6.66667 13.9457 6.57887 13.7337 6.42259 13.5774C6.26631 13.4211 6.05435 13.3333 5.83333 13.3333C5.61232 13.3333 5.40036 13.4211 5.24408 13.5774C5.0878 13.7337 5 13.9457 5 14.1667C5 14.3877 5.0878 14.5996 5.24408 14.7559C5.40036 14.9122 5.61232 15 5.83333 15Z"
        fill="black"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M5.83331 1.45825C5.99907 1.45825 6.15805 1.5241 6.27526 1.64131C6.39247 1.75852 6.45831 1.91749 6.45831 2.08325V2.71909C7.00998 2.70825 7.61748 2.70825 8.28581 2.70825H11.7133C12.3825 2.70825 12.99 2.70825 13.5416 2.71909V2.08325C13.5416 1.91749 13.6075 1.75852 13.7247 1.64131C13.8419 1.5241 14.0009 1.45825 14.1666 1.45825C14.3324 1.45825 14.4914 1.5241 14.6086 1.64131C14.7258 1.75852 14.7916 1.91749 14.7916 2.08325V2.77242C15.0083 2.78909 15.2136 2.8102 15.4075 2.83575C16.3841 2.96742 17.175 3.24409 17.7991 3.86742C18.4225 4.49159 18.6991 5.28242 18.8308 6.25909C18.9583 7.20909 18.9583 8.42159 18.9583 9.95325V11.7133C18.9583 13.2449 18.9583 14.4583 18.8308 15.4074C18.6991 16.3841 18.4225 17.1749 17.7991 17.7991C17.175 18.4224 16.3841 18.6991 15.4075 18.8308C14.4575 18.9583 13.245 18.9583 11.7133 18.9583H8.28748C6.75581 18.9583 5.54248 18.9583 4.59331 18.8308C3.61665 18.6991 2.82581 18.4224 2.20165 17.7991C1.57831 17.1749 1.30165 16.3841 1.16998 15.4074C1.04248 14.4574 1.04248 13.2449 1.04248 11.7133V9.95325C1.04248 8.42159 1.04248 7.20825 1.16998 6.25909C1.30165 5.28242 1.57831 4.49159 2.20165 3.86742C2.82581 3.24409 3.61665 2.96742 4.59331 2.83575C4.78776 2.8102 4.99304 2.78909 5.20915 2.77242V2.08325C5.20915 1.91764 5.27488 1.75879 5.39191 1.6416C5.50894 1.52442 5.6677 1.45847 5.83331 1.45825ZM4.75831 4.07492C3.92081 4.18742 3.43748 4.39909 3.08498 4.75159C2.73248 5.10409 2.52081 5.58742 2.40831 6.42492C2.38943 6.56659 2.37331 6.71631 2.35998 6.87408H17.64C17.6266 6.71631 17.6105 6.56631 17.5916 6.42409C17.4791 5.58659 17.2675 5.10325 16.915 4.75075C16.5625 4.39825 16.0791 4.18659 15.2408 4.07409C14.385 3.95909 13.2558 3.95742 11.6666 3.95742H8.33331C6.74415 3.95742 5.61581 3.95992 4.75831 4.07492ZM2.29165 9.99992C2.29165 9.28825 2.29165 8.66909 2.30248 8.12492H17.6975C17.7083 8.66909 17.7083 9.28825 17.7083 9.99992V11.6666C17.7083 13.2558 17.7066 14.3849 17.5916 15.2416C17.4791 16.0791 17.2675 16.5624 16.915 16.9149C16.5625 17.2674 16.0791 17.4791 15.2408 17.5916C14.385 17.7066 13.2558 17.7083 11.6666 17.7083H8.33331C6.74415 17.7083 5.61581 17.7066 4.75831 17.5916C3.92081 17.4791 3.43748 17.2674 3.08498 16.9149C2.73248 16.5624 2.52081 16.0791 2.40831 15.2408C2.29331 14.3849 2.29165 13.2558 2.29165 11.6666V9.99992Z"
        fill="black"
      />
    </svg>
  );
};

export default CalenderClass;
