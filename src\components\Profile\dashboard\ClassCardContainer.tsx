import { CartType } from "@/api/mongoTypes";
import { Box, Skeleton } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import ClassCard from "../ClassCard";
import EmptyList from "../EmptyInfo/EmptyList";
import useWindowDimensions from "@/hooks/useWindowDimension";
import {
  getEmptyClassesTitleAndDesc,
  getEmptySuggestionsTitleAndDesc,
} from "@/utils/dashboard";
import { MySchedulesResponse } from "@/types";

type ClassCardContainerProps = React.FC<{
  data: CartType[] | MySchedulesResponse;
  active: number;
  isYourClasses?: boolean;
  isLoading?: boolean;
  isDashboard?: boolean;
}>;
const ClassCardContainer: ClassCardContainerProps = ({
  data,
  active,
  isYourClasses = false,
  isLoading = false,
  isDashboard = false,
}) => {
  const { width } = useWindowDimensions();

  const [renderArray, setRenderArray] = useState(3);

  useEffect(() => {
    setRenderArray(width > 900 ? 4 : 3);
  }, [width]);

  const { title, description } = useMemo(() => {
    if (isYourClasses) {
      return getEmptyClassesTitleAndDesc({ type: active });
    } else {
      return getEmptySuggestionsTitleAndDesc({ type: active });
    }
  }, [active]);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        width: "100%",
        p: 1,
        alignItems: "stretch",
        overflowX: "scroll",
        "&::-webkit-scrollbar": { display: "none" },
        scrollbarWidth: "none",
        msOverflowStyle: "none",
        minHeight: 300,
      }}
    >
      {isLoading ? (
        <>
          {new Array(10)
            .fill("")
            ?.slice(0, renderArray)
            .map((m, i) => (
              <CardContainer key={i}>
                <Skeleton
                  variant="rectangular"
                  sx={{ borderRadius: 3 }}
                  width="100%"
                  height={300}
                />
              </CardContainer>
            ))}
        </>
      ) : data.length > 0 ? (
        <>
          {data?.slice(0, renderArray).map((m, i) => {
            if (m.date && m.data.length > 0) {
              return m.data.map((j, i) => (
                <CardContainer key={i}>
                  <ClassCard
                    active={active}
                    data={j}
                    key={i}
                    isYourClasses={isYourClasses}
                    isSuggestion={!isYourClasses}
                    cardDate={m.date}
                    isDashboard={isDashboard}
                  />
                </CardContainer>
              ));
            } else {
              return data?.slice(0, renderArray).map((m, i) => (
                <CardContainer key={i}>
                  <ClassCard
                    active={active}
                    data={m}
                    key={i}
                    isYourClasses={isYourClasses}
                    isSuggestion={!isYourClasses}
                    isDashboard={isDashboard}
                  />
                </CardContainer>
              ));
            }
          })}
        </>
      ) : (
        <EmptyList title={title} description={description} isSmall />
      )}
    </Box>
  );
};

type CardContainerProps = React.FC<{
  children: React.ReactNode;
}>;
const CardContainer: CardContainerProps = ({ children }) => {
  return (
    <Box
      sx={{
        width: {
          xs: 250,
          // xs: "100%",
          sm: "33%",
          md: "25%",
        },
        minWidth: {
          xs: 250,
          // xs: "100%",
          sm: "33%",
          md: "25%",
        },
        p: 1,
      }}
    >
      {children}
    </Box>
  );
};

export default ClassCardContainer;
