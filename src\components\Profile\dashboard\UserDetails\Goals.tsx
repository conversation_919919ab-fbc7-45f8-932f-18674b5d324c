import { Box, Typography } from "@mui/material";
import React from "react";
import CardSectionTitle from "./CardSectionTitle";
import { GoalIcon } from "./Icons";
import Tag from "@/components/Tag";

const Goals = ({ goals, sx = {} }) => {
  return (
    <Box display="flex" flexDirection="column" sx={sx}>
      <CardSectionTitle icon={<GoalIcon />} name="Goals" />
      <Box
        display="flex"
        flexWrap="wrap"
        flexDirection="row"
        alignItems="center"
        mt={1}
        gap={1}
      >
        {goals.length === 0 ? (
          <Typography fontSize={12} color="rgba(109, 109, 109, 1)">
            Not selected
          </Typography>
        ) : (
          goals.map((m, i) => (
            <Tag key={i} text={m.name} sx={{ maxWidth: "max-content" }} />
          ))
        )}
      </Box>
    </Box>
  );
};

export default Goals;
