import { Box, Typography } from "@mui/material";
import Link from "next/link";
import React from "react";

const NotionLink = ({ url }) => {
  const height = 16;

  if (!url) {
    return null;
  }

  return (
    <Box display="flex" flexDirection="row" alignItems="center" mt={2}>
      <svg
        width={height}
        height={height}
        viewBox="0 0 19 19"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.4688 10.2916C12.4688 10.1342 12.4062 9.98313 12.2948 9.87178C12.1835 9.76043 12.0325 9.69788 11.875 9.69788H7.125C6.96753 9.69788 6.81651 9.76043 6.70516 9.87178C6.59381 9.98313 6.53125 10.1342 6.53125 10.2916C6.53125 10.4491 6.59381 10.6001 6.70516 10.7115C6.81651 10.8228 6.96753 10.8854 7.125 10.8854H11.875C12.0325 10.8854 12.1835 10.8228 12.2948 10.7115C12.4062 10.6001 12.4688 10.4491 12.4688 10.2916ZM12.4688 13.4583C12.4688 13.3008 12.4062 13.1498 12.2948 13.0384C12.1835 12.9271 12.0325 12.8645 11.875 12.8645H7.125C6.96753 12.8645 6.81651 12.9271 6.70516 13.0384C6.59381 13.1498 6.53125 13.3008 6.53125 13.4583C6.53125 13.6158 6.59381 13.7668 6.70516 13.8781C6.81651 13.9895 6.96753 14.052 7.125 14.052H11.875C12.0325 14.052 12.1835 13.9895 12.2948 13.8781C12.4062 13.7668 12.4688 13.6158 12.4688 13.4583Z"
          fill="black"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M5.54134 1.78125C4.96394 1.78125 4.41019 2.01062 4.00191 2.4189C3.59363 2.82719 3.36426 3.38093 3.36426 3.95833V15.0417C3.36426 15.6191 3.59363 16.1728 4.00191 16.5811C4.41019 16.9894 4.96394 17.2188 5.54134 17.2188H13.458C14.0354 17.2188 14.5892 16.9894 14.9974 16.5811C15.4057 16.1728 15.6351 15.6191 15.6351 15.0417V6.308C15.6351 6.00637 15.5369 5.71346 15.3548 5.47279L12.9814 2.33146C12.8523 2.16055 12.6853 2.02192 12.4935 1.92645C12.3018 1.83098 12.0905 1.78128 11.8763 1.78125H5.54134ZM4.55176 3.95833C4.55176 3.41208 4.99509 2.96875 5.54134 2.96875H11.2809V6.44971C11.2809 6.77746 11.5469 7.04346 11.8747 7.04346H14.4476V15.0417C14.4476 15.5879 14.0043 16.0312 13.458 16.0312H5.54134C4.99509 16.0312 4.55176 15.5879 4.55176 15.0417V3.95833Z"
          fill="black"
        />
      </svg>
      <Typography fontWeight="700" fontSize={height - 2}>
        My Notes page :
      </Typography>
      &nbsp;
      <svg
        width={height}
        height={height}
        viewBox="0 0 19 19"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clip-path="url(#clip0_10163_6469)">
          <path
            d="M12.4517 3.07481C13.5869 1.93481 15.2479 1.91106 16.167 2.83414C17.0885 3.75881 17.0639 5.43081 15.9271 6.57081L14.0089 8.49693C13.9009 8.60907 13.8413 8.75914 13.8428 8.91482C13.8444 9.07051 13.907 9.21934 14.0173 9.32928C14.1275 9.43921 14.2766 9.50145 14.4322 9.50257C14.5879 9.5037 14.7378 9.44363 14.8497 9.33531L16.7687 7.40918C18.2815 5.88998 18.4715 3.4651 17.0085 1.99577C15.5439 0.525643 13.1238 0.717227 11.6094 2.23644L7.77295 6.08948C6.26007 7.60869 6.07007 10.0336 7.53307 11.5021C7.58771 11.5589 7.65312 11.6043 7.72549 11.6356C7.79785 11.6669 7.87572 11.6834 7.95454 11.6843C8.03337 11.6851 8.11158 11.6703 8.18461 11.6406C8.25764 11.6109 8.32403 11.567 8.37991 11.5114C8.43578 11.4558 8.48002 11.3896 8.51005 11.3167C8.54007 11.2438 8.55529 11.1657 8.55479 11.0868C8.5543 11.008 8.53811 10.9301 8.50718 10.8576C8.47624 10.7851 8.43118 10.7194 8.37461 10.6645C7.45311 9.73985 7.47845 8.06785 8.61449 6.92785L12.4517 3.07481Z"
            fill="#009BC1"
          />
          <path
            d="M11.4673 7.49703C11.356 7.38543 11.205 7.32261 11.0474 7.32239C10.8898 7.32217 10.7386 7.38456 10.627 7.49584C10.5154 7.60712 10.4526 7.75817 10.4523 7.91577C10.4521 8.07337 10.5145 8.2246 10.6258 8.33619C11.5473 9.26086 11.5227 10.9321 10.3859 12.0729L6.54871 15.9251C5.41266 17.0651 3.75175 17.0889 2.83262 16.1658C1.91112 15.2411 1.93645 13.5691 3.0725 12.4291L4.9915 10.503C5.04655 10.4477 5.09017 10.3822 5.11988 10.3101C5.1496 10.2379 5.16481 10.1607 5.16467 10.0827C5.16452 10.0047 5.14901 9.92747 5.11903 9.85547C5.08904 9.78347 5.04517 9.71807 4.98991 9.66303C4.93466 9.60798 4.8691 9.56435 4.79698 9.53464C4.72487 9.50493 4.6476 9.48971 4.56961 9.48986C4.49161 9.49 4.4144 9.50551 4.3424 9.5355C4.2704 9.56548 4.205 9.60935 4.14995 9.66461L2.23095 11.5907C0.71808 13.1107 0.52808 15.5348 1.99108 17.0042C3.45566 18.4751 5.87579 18.2827 7.39025 16.7635L11.2275 12.9104C12.7403 11.392 12.9303 8.96557 11.4673 7.49703Z"
            fill="#009BC1"
          />
        </g>
        <defs>
          <clipPath id="clip0_10163_6469">
            <rect width="19" height="19" fill="white" />
          </clipPath>
        </defs>
      </svg>
      &nbsp;
      <Link href={url} target="_blank">
        <Typography fontSize={height - 2} color="rgba(0, 155, 193, 1)">
          My Notes page
        </Typography>
      </Link>
    </Box>
  );
};

export default NotionLink;
