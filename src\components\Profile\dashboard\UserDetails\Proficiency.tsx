import React, { useMemo } from "react";
import CardSectionTitle from "./CardSectionTitle";
import { ProficiencyIcon } from "./Icons";
import { Box, Typography } from "@mui/material";
import { UserLanguageType } from "@/api/mongoTypes";
import NameValue from "./NameValue";
import { getProficiencyList } from "@/utils/format";

type ProficiencyProps = React.FC<{
  languages: UserLanguageType[];
}>;
const Proficiency: ProficiencyProps = ({ languages }) => {
  const list = useMemo(() => {
    return getProficiencyList(languages);
  }, [languages]);

  return (
    <Box mt={1}>
      <CardSectionTitle
        icon={<ProficiencyIcon />}
        name="Proficiency of Languages"
      />
      <Box display="flex" flexDirection="row" mt={1}>
        <Box width="5%"></Box>
        <Box width="95%">
          {list.length > 0 ? (
            <>
              {list.map((m, i) => (
                <NameValue key={i} name={m.proficiency} value={m.languages} />
              ))}
            </>
          ) : (
            <Typography fontSize={12} color="rgba(109, 109, 109, 1)">
              No proficiencies selected
            </Typography>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Proficiency;
