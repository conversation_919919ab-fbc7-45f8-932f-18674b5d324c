import { Box, Typography } from "@mui/material";
import React from "react";
import NameValue from "./NameValue";
import CardSectionTitle from "./CardSectionTitle";

type StudyingLanguageProps = React.FC<{
  studyingLangauge: string;
  proficiency: string;
}>;
const StudyingLanguage: StudyingLanguageProps = ({
  studyingLangauge,
  proficiency,
}) => {
  if (!studyingLangauge) {
    return (
      <Box mt={1}>
        <CardSectionTitle
          icon={<StudyingLangIcon />}
          name="Studying Language"
        />
        <Box display="flex" flexDirection="row" mt={1}>
          <Box width="5%"></Box>
          <Box width="95%">
            <Typography fontSize={14} color="rgba(109, 109, 109, 1)">
              Not Selected
            </Typography>
          </Box>
        </Box>
      </Box>
    );
  }
  if (proficiency && studyingLangauge) {
    return (
      <Box mt={1}>
        <CardSectionTitle
          icon={<StudyingLangIcon />}
          name="Studying Language"
        />
        <Box display="flex" flexDirection="row" mt={1}>
          <Box width="5%"></Box>
          <Box width="95%">
            <NameValue name={proficiency} value={studyingLangauge} />
          </Box>
        </Box>
      </Box>
    );
  }

  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      mb={3}
    >
      <Box display="flex" flexDirection="row" alignItems="center">
        <StudyingLangIcon />
        &nbsp;
        <Typography fontSize={14}>
          <b>Studying Language</b>
        </Typography>
      </Box>
      <Typography fontSize={14} color="rgba(109, 109, 109, 1)">
        {studyingLangauge}
      </Typography>
    </Box>
  );
};

const StudyingLangIcon = () => {
  return (
    <svg
      width="16"
      height="17"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 5.5C8.5 3.52625 10.3884 2.51844 14.5 2.5C14.5657 2.49976 14.6309 2.51252 14.6916 2.53756C14.7524 2.5626 14.8076 2.59942 14.8541 2.6459C14.9006 2.69238 14.9374 2.74759 14.9624 2.80837C14.9875 2.86914 15.0003 2.93427 15 3V12C15 12.1326 14.9473 12.2598 14.8536 12.3536C14.7598 12.4473 14.6326 12.5 14.5 12.5C10.5 12.5 8.95469 13.3066 8 14.5M8 5.5C7.5 3.52625 5.61157 2.51844 1.5 2.5C1.43427 2.49976 1.36914 2.51252 1.30837 2.53756C1.24759 2.5626 1.19238 2.59942 1.1459 2.6459C1.09942 2.69238 1.0626 2.74759 1.03756 2.80837C1.01252 2.86914 0.999756 2.93427 1 3V11.9397C1 12.2484 1.19125 12.5 1.5 12.5C5.5 12.5 7.05094 13.3125 8 14.5M8 5.5V14.5"
        stroke="black"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export default StudyingLanguage;
