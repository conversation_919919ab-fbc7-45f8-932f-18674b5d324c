import { Box, Card, CardContent, Typography } from "@mui/material";
import React, { useMemo } from "react";
import NotionLink from "./NotionLink";
import Header from "./Header";
import Proficiency from "./Proficiency";
import Interests from "./Interests";
import Goals from "./Goals";
import {
  CountryType,
  LanguageType,
  UserLanguageType,
  UserType,
} from "@/api/mongoTypes";
import StudyingLanguage from "./StudyingLanguage";
import { getProficiencyEn } from "@/utils/common";

type UserDetailsProps = React.FC<{
  userDetails: UserType;
}>;

const UserDetails: UserDetailsProps = ({ userDetails }) => {
  const countryName = useMemo(() => {
    if (!userDetails) {
      return null;
    }
    const countryInfo = userDetails?.countryOfResidence as CountryType;
    if (
      countryInfo &&
      typeof countryInfo !== "string" &&
      "name" in countryInfo
    ) {
      return countryInfo.name;
    }
    return null;
  }, [userDetails]);

  const proficiencyLanguages = useMemo(() => {
    return userDetails?.languages?.map((m) => m as UserLanguageType) ?? [];
  }, [userDetails]);

  const studyingLangauge = useMemo(() => {
    const lang = userDetails.languageOfInterest;
    if (lang && lang?._id && "name" in lang) {
      return lang.name;
    }
    return null;
  }, [userDetails]);

  const studyingLangaugeProficiency = useMemo(() => {
    const lang = userDetails.proficiencyOfLanguageOfInterest;
    return getProficiencyEn({
      data: lang,
    });
  }, [userDetails]);

  return (
    <Card
      sx={{
        padding: 3,
        borderRadius: 2,
        boxShadow: "0px 1px 20px 0px rgba(0, 0, 0, 0.12)",
      }}
    >
      <CardContent
        sx={{ border: "1px solid rgba(237, 237, 237, 1)", borderRadius: 2 }}
      >
        <Header userDetails={userDetails} />
        <Box
          display="flex"
          sx={{
            flexDirection: {
              xs: "column",
              sm: "row",
            },
          }}
        >
          <Box
            sx={{
              width: {
                xs: "100%",
                sm: "50%",
              },
              p: 2,
            }}
          >
            <NameIconValue
              name="Country"
              value={countryName}
              icon={<CountryIcon />}
            />
            <StudyingLanguage
              proficiency={studyingLangaugeProficiency}
              studyingLangauge={studyingLangauge}
            />
            <Proficiency languages={proficiencyLanguages} />
          </Box>
          <Box
            sx={{
              width: {
                xs: "100%",
                sm: "50%",
              },
              p: 2,
            }}
          >
            <Interests selectedInterests={userDetails?.interest} />
            <Goals goals={userDetails?.goals} />
          </Box>
        </Box>
      </CardContent>
      <NotionLink url={userDetails.notionUrl} />
    </Card>
  );
};

export default UserDetails;

const CountryIcon = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 9C7.50555 9 7.0222 8.85338 6.61108 8.57868C6.19995 8.30397 5.87952 7.91353 5.6903 7.45671C5.50108 6.99989 5.45157 6.49723 5.54804 6.01228C5.6445 5.52732 5.8826 5.08187 6.23223 4.73223C6.58187 4.3826 7.02732 4.1445 7.51228 4.04804C7.99723 3.95157 8.4999 4.00108 8.95671 4.1903C9.41353 4.37952 9.80397 4.69995 10.0787 5.11108C10.3534 5.5222 10.5 6.00555 10.5 6.5C10.4992 7.1628 10.2356 7.79822 9.76689 8.26689C9.29822 8.73556 8.6628 8.99921 8 9ZM8 5C7.70333 5 7.41332 5.08797 7.16665 5.2528C6.91997 5.41762 6.72771 5.65189 6.61418 5.92598C6.50065 6.20007 6.47095 6.50167 6.52882 6.79264C6.5867 7.08361 6.72956 7.35088 6.93934 7.56066C7.14912 7.77044 7.41639 7.9133 7.70737 7.97118C7.99834 8.02906 8.29994 7.99935 8.57403 7.88582C8.84812 7.77229 9.08238 7.58003 9.24721 7.33336C9.41203 7.08668 9.5 6.79667 9.5 6.5C9.4996 6.1023 9.34144 5.721 9.06022 5.43978C8.779 5.15856 8.3977 5.0004 8 5Z"
        fill="black"
      />
      <path
        d="M8 15L3.782 10.0255C3.72339 9.95081 3.66539 9.87564 3.608 9.8C2.88786 8.8507 2.49866 7.69155 2.5 6.5C2.5 5.04131 3.07947 3.64236 4.11092 2.61091C5.14237 1.57946 6.54131 1 8 1C9.45869 1 10.8576 1.57946 11.8891 2.61091C12.9205 3.64236 13.5 5.04131 13.5 6.5C13.5012 7.69098 13.1122 8.84954 12.3925 9.7985L12.392 9.8C12.392 9.8 12.242 9.997 12.2195 10.0235L8 15ZM4.4065 9.1975C4.4065 9.1975 4.523 9.3515 4.5495 9.3845L8 13.454L11.455 9.379C11.477 9.3515 11.594 9.1965 11.5945 9.196C12.1831 8.42057 12.5012 7.47352 12.5 6.5C12.5 5.30653 12.0259 4.16193 11.182 3.31802C10.3381 2.47411 9.19348 2 8 2C6.80653 2 5.66194 2.47411 4.81802 3.31802C3.97411 4.16193 3.5 5.30653 3.5 6.5C3.49877 7.47415 3.81723 8.42179 4.4065 9.1975Z"
        fill="black"
      />
    </svg>
  );
};

type NameIconValueProps = React.FC<{
  name: String;
  value: String;
  icon: React.ReactNode;
}>;
const NameIconValue: NameIconValueProps = ({ name, value, icon }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      mb={3}
    >
      <Box display="flex" flexDirection="row" alignItems="center">
        {icon}
        &nbsp;
        <Typography fontSize={14}>
          <b>{name}</b>
        </Typography>
      </Box>
      <Typography fontSize={14} color="rgba(109, 109, 109, 1)">
        {value ?? "Not Selected"}
      </Typography>
    </Box>
  );
};
