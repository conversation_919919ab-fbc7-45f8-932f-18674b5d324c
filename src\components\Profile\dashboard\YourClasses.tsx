import { Box } from "@mui/material";
import React, { useEffect, useState } from "react";
import SectionHeader from "./SectionHeader";
import { CartType } from "@/api/mongoTypes";
import ClassCardContainer from "./ClassCardContainer";
import { getClassessFetchTabs } from "@/utils/classes";
import { CLASSESS_FETCH_DURATION } from "@/constant/Enums";
import { useSnackbar } from "@/hooks/useSnackbar";
import axios from "axios";
import { getLocalUser } from "@/utils/common";
import { useUserContext } from "@/contexts/UserContext";
import { getDateAsPerUTC } from "@/utils/dateTime";
import { groupDataByDate } from "@/utils/listLogic";

const YourClasses = () => {
  const [active, setActive] = useState<CLASSESS_FETCH_DURATION>(2);
  const [isLoading, setIsLoading] = useState(true);
  const { showSnackbar } = useSnackbar();
  const [classess, setClassess] = useState([]);
  const { dbUser } = useUserContext();
  const userId = dbUser?._id;

  const fetchYourClassess = async (userId) => {
    try {
      setIsLoading(true);
      setClassess([]);
      const isAscending = true;
      const { data: respdata } = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/schedule/my-schedules`,
        {
          params: {
            timeFilter: active,
            skip: 0,
            limit: 4,
            isMyClassesOnly: true,
            filters: "",
            isAscending,
            currentDate: new Date(),
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          },
          headers: {
            userid: userId,
          },
        }
      );
      if (respdata.success) {
        const groupedData = groupDataByDate({
          data: respdata?.data,
          active,
          isAscending,
        }) as any[];
        setClassess((prev) => [...prev, ...groupedData]);
      } else {
        showSnackbar("Failed to fetch the suggested class", {
          type: "error",
        });
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.error(
        "Something went wrong in fetchSuggestedClassess due to ",
        error
      );
    }
  };

  useEffect(() => {
    if (userId) {
      fetchYourClassess(userId);
    }
  }, [active, userId]);

  return (
    <Box>
      <SectionHeader
        isListEmpty={false}
        title="Your Classes & Events"
        linkText="See All"
        link="/profile/classes"
        active={active}
        setActive={setActive}
        tabs={getClassessFetchTabs({
          needPast: true,
          needAll: false,
          needCurrent: true,
          needUpcoming: true,
        })}
      />
      <ClassCardContainer
        isLoading={isLoading}
        isYourClasses
        data={classess}
        active={active}
      />
    </Box>
  );
};

export default YourClasses;
