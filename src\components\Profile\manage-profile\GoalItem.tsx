import { Box, Container, styled, Typography } from "@mui/material";
import Image from "next/image";

type GoalItemType = {
  id: string;
  name: string;
  image?: string;
  checked?: boolean;
  setGoal: React.Dispatch<React.SetStateAction<string>>;
};

const GoalText = styled(Typography)(({ theme }) => ({
  color: "#666666",
  fontSize: "14px",
  textAlign: "center",
  marginTop: theme.spacing(1),
}));

const GoalItem = ({ id, image, name, checked, setGoal }: GoalItemType) => {
  return (
    <Container
      onClick={() => setGoal(id)}
      sx={{
        display: "inline-flex",
        flexDirection: "column",
        alignItems: "center",
        transition: "all .2s ease",
        "&:hover": {
          transform: "scale(1.05)",
          transition: "all .3s ease",
        },
      }}
    >
      <Box
        sx={{
          position: "relative",
          width: 116,
          height: 90,
          borderRadius: "12px",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.1)",
          cursor: "pointer",
          marginBottom: 1,
          "&:hover": {
            boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.15)",
          },
          overflow: "hidden",
          objectFit: "cover",
          objectPosition: "center",
        }}
      >
        <Image height={90} width={116} src={image} alt={name} />
      </Box>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyItems: "items",
          alignContent: "center",
          gap: 2,
        }}
      >
        <GoalText variant="body2" sx={{ color: `#64748B` }}>
          {name}
        </GoalText>
        {checked && <CheckedGoalIcon />}
      </Box>
    </Container>
  );
};

const CheckedGoalIcon = () => {
  return (
    <svg
      width="11"
      height="11"
      viewBox="0 0 117 117"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g fill="none">
        <path
          d="M34.5 55.1c-1.6-1.6-4.2-1.6-5.8 0s-1.6 4.2 0 5.8l18.9 18.9c.8.8 1.8 1.2 2.9 1.2h.2c1.1-.1 2.2-.6 3-1.5L101 22.8c1.4-1.7 1.2-4.3-.5-5.8-1.7-1.4-4.3-1.2-5.8.5L50.2 70.8z"
          fill="#17AB13"
        />
        <path
          d="M89.1 9.3c-23-14.4-52.5-11-71.7 8.2-22.6 22.6-22.6 59.5 0 82.1a57.94 57.94 0 0 0 82 0c19.3-19.3 22.6-48.9 8.1-71.9-1.2-1.9-3.7-2.5-5.6-1.3s-2.5 3.7-1.3 5.6c12.5 19.8 9.6 45.2-7 61.8-19.4 19.4-51.1 19.4-70.5 0s-19.4-51.1 0-70.5C39.7 6.8 65 3.9 84.8 16.2c1.9 1.2 4.4.6 5.6-1.3s.6-4.4-1.3-5.6"
          fill="#4A4A4A"
        />
      </g>
    </svg>
  );
};

export default GoalItem;
