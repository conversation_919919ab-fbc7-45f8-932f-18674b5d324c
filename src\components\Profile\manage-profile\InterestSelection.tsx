import React from "react";
import InformationEditGroup from "./InformationEditGroup";
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from "react-beautiful-dnd";
import { InterestType } from "@/api/mongoTypes";
import { Box, styled, Typography } from "@mui/material";
import Image from "next/image";
import {
  LoveFace,
  NeutralFace,
  DislikeFace,
} from "@/components/SentimentEmoji";

const DropArea = styled(Box)({
  flex: 1,
  marginLeft: "32px",

  overflow: "hidden",
  position: "relative",
  "& .placeholder-text": {
    color: "#909090",
    fontWeight: 400,
    whiteSpace: "nowrap",
  },
  "& > div": {
    display: "flex",
    flexWrap: "wrap",
    gap: "8px",
    padding: "4px",
    overflow: "auto",
    "&::-webkit-scrollbar": {
      display: "none",
    },
    scrollbarWidth: "none",
    msOverflowStyle: "none",
  },
});

const InterestRemoveIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 6L6 18"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 6L18 18"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const InterestsList = styled(Box)(({ theme }) => ({
  display: "flex",
  overflowX: "auto",
  gap: theme.spacing(2),
  padding: theme.spacing(1),
  marginBottom: theme.spacing(4),
  "&::-webkit-scrollbar": {
    display: "none",
  },
  scrollbarWidth: "none",
  msOverflowStyle: "none",
}));

const InterestItem = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  gap: theme.spacing(1),
  padding: "4px 10px",
  borderRadius: "100px",
  border: "1px solid #E0E0E0",
  backgroundColor: "#fff",
  cursor: "grab",
  whiteSpace: "nowrap",
  fontWeight: "500",
  transition: "transform 0.2s ease, box-shadow 0.2s ease",
  "&:active": {
    cursor: "grabbing",
  },
  "&.dragging": {
    transform: "scale(1.05)",
    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
    zIndex: 1,
  },
  "& img": {
    width: 24,
    height: 24,
    borderRadius: "50%",
  },
  "& .remove-icon": {
    display: "block",
    cursor: "pointer",
    position: "relative",
    top: "3px",
    paddingLeft: "3px",
    color: "#666",
    "&:hover": {
      color: "#ff0000",
    },
  },
}));

const CategoryRow = styled(Box)({
  display: "flex",
  alignItems: "center",
  marginBottom: "12px",
  paddingBottom: "12px",
  borderBottom: "1px solid #EAEAEA",
  "&:last-child": {
    marginBottom: 0,
    paddingBottom: "12px",
  },
});

const CategoryButton = styled(Box)(({ bgcolor }: { bgcolor: string }) => ({
  display: "inline-flex",
  alignItems: "center",
  gap: "8px",
  padding: "8px 24px",
  backgroundColor: bgcolor,
  borderRadius: "100px",
  minWidth: "140px",
  "& .MuiTypography-root": {
    fontWeight: 500,
    fontSize: "16px",
  },
  "& .emoji": {
    fontSize: "20px",
    lineHeight: 1,
  },
}));

const InterestSelection = ({
  lovedInterests,
  neutralInterests,
  hatedInterests,
  availableInterests,
  setAvailableInterests,
  setLovedInterests,
  setNeutralInterests,
  setHatedInterests,
}) => {
  const handleRemoveInterest = (
    interest: InterestType,
    fromList: "loved" | "neutral" | "hated"
  ) => {
    // Remove from current list
    if (fromList === "loved") {
      setLovedInterests((prev) => prev.filter((i) => i._id !== interest._id));
    } else if (fromList === "neutral") {
      setNeutralInterests((prev) => prev.filter((i) => i._id !== interest._id));
    } else if (fromList === "hated") {
      setHatedInterests((prev) => prev.filter((i) => i._id !== interest._id));
    }
    setAvailableInterests((prev) => [...prev, interest]);
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const sourceId = result.source.droppableId;
    const destId = result.destination.droppableId;
    const itemId = result.draggableId;

    if (sourceId == destId) return; // No need to make changes.

    const lists: Record<string, InterestType[]> = {
      available: availableInterests,
      loved: lovedInterests,
      neutral: neutralInterests,
      hated: hatedInterests,
    };

    // Find the source item
    const item = lists[sourceId].find((i) => i._id === itemId);
    if (!item) return;

    // Remove from source
    const newSourceList = lists[sourceId].filter((i) => i._id !== itemId);

    // Add to destination
    const newDestList = [...lists[destId], item];

    // Update states
    if (sourceId === "available") setAvailableInterests(newSourceList);
    if (sourceId === "loved") setLovedInterests(newSourceList);
    if (sourceId === "neutral") setNeutralInterests(newSourceList);
    if (sourceId === "hated") setHatedInterests(newSourceList);

    if (destId === "loved") setLovedInterests(newDestList);
    if (destId === "neutral") setNeutralInterests(newDestList);
    if (destId === "hated") setHatedInterests(newDestList);
  };

  return (
    <InformationEditGroup
      index="6"
      title="Interests"
      isPending={
        ![lovedInterests, neutralInterests, hatedInterests].some(
          (interests) => interests.length > 0
        )
      }
    >
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="available" direction="horizontal">
          {(provided) => (
            <InterestsList ref={provided.innerRef} {...provided.droppableProps}>
              {availableInterests.map((interest, index) => (
                <Draggable
                  key={interest._id}
                  draggableId={interest._id}
                  index={index}
                >
                  {(provided, snapshot) => (
                    <InterestItem
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      className={snapshot.isDragging ? "dragging" : ""}
                    >
                      <Image
                        src={interest.image}
                        alt={interest.name}
                        width={20}
                        height={20}
                        objectFit="cover"
                        priority
                      />
                      <Typography>{interest.name}</Typography>
                    </InterestItem>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </InterestsList>
          )}
        </Droppable>

        <Box sx={{ mt: 2 }}>
          <CategoryRow>
            <CategoryButton bgcolor="#F9B238B2">
              <Typography sx={{ color: "#fff" }}>Love it</Typography>
              <LoveFace />
            </CategoryButton>
            <Droppable droppableId="loved" direction="horizontal">
              {(provided) => (
                <DropArea ref={provided.innerRef} {...provided.droppableProps}>
                  {lovedInterests.length === 0 ? (
                    <Typography className="placeholder-text">
                      Select Interests you love
                    </Typography>
                  ) : (
                    <Box
                      sx={{
                        display: "flex",
                        flexWrap: "wrap",
                        gap: 2,
                        py: 1,
                        position: "relative",
                      }}
                    >
                      {lovedInterests.map((interest, index) => (
                        <Draggable
                          key={interest._id}
                          draggableId={interest._id}
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <InterestItem
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className={snapshot.isDragging ? "dragging" : ""}
                            >
                              <Image
                                src={interest.image}
                                alt={interest.name}
                                width={20}
                                height={20}
                                objectFit="cover"
                                priority
                              />
                              <Typography>{interest.name}</Typography>
                              <Box
                                className="remove-icon"
                                onClick={(e) => {
                                  handleRemoveInterest(interest, "loved");
                                }}
                              >
                                <InterestRemoveIcon />
                              </Box>
                            </InterestItem>
                          )}
                        </Draggable>
                      ))}
                    </Box>
                  )}
                  {provided.placeholder}
                </DropArea>
              )}
            </Droppable>
          </CategoryRow>

          <CategoryRow>
            <CategoryButton bgcolor="#B3B3B3" color="#fff">
              <Typography>Neutral</Typography>
              <NeutralFace />
            </CategoryButton>
            <Droppable droppableId="neutral" direction="horizontal">
              {(provided) => (
                <DropArea ref={provided.innerRef} {...provided.droppableProps}>
                  {neutralInterests.length === 0 ? (
                    <Typography className="placeholder-text">
                      Select Interests you&apos;re okay with
                    </Typography>
                  ) : (
                    <Box
                      sx={{
                        display: "flex",
                        flexWrap: "wrap",
                        gap: 2,
                        py: 1,
                      }}
                    >
                      {neutralInterests.map((interest, index) => (
                        <Draggable
                          key={interest._id}
                          draggableId={interest._id}
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <InterestItem
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className={snapshot.isDragging ? "dragging" : ""}
                            >
                              <Image
                                src={interest.image}
                                alt={interest.name}
                                width={20}
                                height={20}
                                objectFit="cover"
                                priority
                              />
                              <Typography>{interest.name}</Typography>
                              <Box
                                className="remove-icon"
                                onClick={(e) => {
                                  handleRemoveInterest(interest, "neutral");
                                }}
                              >
                                <InterestRemoveIcon />
                              </Box>
                            </InterestItem>
                          )}
                        </Draggable>
                      ))}
                    </Box>
                  )}
                  {provided.placeholder}
                </DropArea>
              )}
            </Droppable>
          </CategoryRow>

          <CategoryRow>
            <CategoryButton bgcolor="#C00F0CB2" color="#fff">
              <Typography>Hate it</Typography>
              <DislikeFace />
            </CategoryButton>
            <Droppable droppableId="hated" direction="horizontal">
              {(provided) => (
                <DropArea ref={provided.innerRef} {...provided.droppableProps}>
                  {hatedInterests.length === 0 ? (
                    <Typography className="placeholder-text">
                      Select Interests you hate
                    </Typography>
                  ) : (
                    <Box
                      sx={{
                        display: "flex",
                        flexWrap: "wrap",
                        gap: 2,
                        py: 1,
                      }}
                    >
                      {hatedInterests.map((interest, index) => (
                        <Draggable
                          key={interest._id}
                          draggableId={interest._id}
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <InterestItem
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className={snapshot.isDragging ? "dragging" : ""}
                            >
                              <Image
                                src={interest.image}
                                alt={interest.name}
                                width={20}
                                height={20}
                                objectFit="cover"
                                priority
                              />
                              <Typography>{interest.name}</Typography>
                              <Box
                                className="remove-icon"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemoveInterest(interest, "hated");
                                }}
                              >
                                <InterestRemoveIcon />
                              </Box>
                            </InterestItem>
                          )}
                        </Draggable>
                      ))}
                    </Box>
                  )}
                  {provided.placeholder}
                </DropArea>
              )}
            </Droppable>
          </CategoryRow>
        </Box>
      </DragDropContext>
    </InformationEditGroup>
  );
};

export default InterestSelection;
