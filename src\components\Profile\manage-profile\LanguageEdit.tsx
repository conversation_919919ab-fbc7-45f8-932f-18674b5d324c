import React from "react";
import InformationEditGroup from "./InformationEditGroup";
import {
  Box,
  FormControl,
  Grid,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import LanguageProficiencyManager from "@/components/LanguageProficiencyManager";
import { LEVEL_TYPES } from "@/constant/Enums";
import { getProficiencyEn } from "@/utils/common";

const InputContStyle = {
  display: "inline-flex",
  alignItems: "center",
  gap: 2,
  backgroundColor: "#fff",
  borderRadius: "16px",
  boxShadow: "0px 2px 14px rgba(0, 0, 0, 0.08)",
  padding: "4px 16px",
  width: "100%",
};

const SelectContStyle = {
  width: "100%",
  "& .MuiOutlinedInput-root": {
    backgroundColor: "transparent",
    borderRadius: "16px",
  },
  "& .MuiOutlinedInput-notchedOutline": {
    border: "none",
  },
};

const PlaceholderStyle = {
  color: "rgba(0, 0, 0, 0.87)",
  fontSize: "16px",
  fontWeight: 400,
  minWidth: "fit-content",
};

const LanguageEdit = ({
  studyLanguage,
  studyLanguageProficiency,
  setStudyLanguage,
  setStudyLanguageProficiency,
  selectionData,
  dbUser,
  languages,
  setLanguages,
  isStudentDashboard = false,
  level,
  setLevel,
}) => {
  return (
    <>
      <InformationEditGroup
        index="3"
        title="What Language Do You Want To Study?"
        isPending={!studyLanguage || !studyLanguageProficiency}
      >
        <>
          <Grid container spacing={8}>
            <Grid item xs={12} sm={6}>
              <Box sx={InputContStyle}>
                <Typography variant="body1" sx={PlaceholderStyle}>
                  Language:
                </Typography>
                <FormControl sx={SelectContStyle}>
                  <Select
                    value={studyLanguage}
                    onChange={(e) => setStudyLanguage(e.target.value)}
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      <Typography sx={{ color: `rgba(0, 0, 0, .38)` }}>
                        Select Language
                      </Typography>
                    </MenuItem>
                    {selectionData.languages
                      .filter(({ code }) => RegExp("^(es|en)$").test(code))
                      .map((language) => {
                        return (
                          <MenuItem key={language._id} value={language._id}>
                            {language.name}
                          </MenuItem>
                        );
                      })}
                  </Select>
                </FormControl>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Box sx={InputContStyle}>
                <Typography variant="body1" sx={PlaceholderStyle}>
                  {isStudentDashboard
                    ? "Student-stated level:"
                    : "Proficiency:"}
                </Typography>
                <FormControl sx={SelectContStyle}>
                  <Select
                    value={studyLanguageProficiency}
                    onChange={(e) =>
                      setStudyLanguageProficiency(e.target.value)
                    }
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      <Typography sx={{ color: `rgba(0, 0, 0, .38)` }}>
                        Select{" "}
                        {isStudentDashboard
                          ? "Student-stated level:"
                          : "Proficiency:"}
                      </Typography>
                    </MenuItem>
                    {selectionData.proficiencies.map((prof) => {
                      return (
                        <MenuItem key={prof._id} value={prof._id}>
                          {getProficiencyEn({
                            data: prof,
                          })}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              </Box>
            </Grid>

            {isStudentDashboard && (
              <Grid item xs={12} sm={6}>
                <Box sx={InputContStyle}>
                  <Typography variant="body1" sx={PlaceholderStyle}>
                    Current proficiency level:
                  </Typography>
                  <FormControl sx={SelectContStyle}>
                    <Select
                      value={level}
                      onChange={(e) => setLevel(e.target.value)}
                      displayEmpty
                    >
                      <MenuItem value="" disabled>
                        <Typography sx={{ color: `rgba(0, 0, 0, .38)` }}>
                          Select Current proficiency level
                        </Typography>
                      </MenuItem>
                      <MenuItem value={LEVEL_TYPES.BEGINNER}>Beginner</MenuItem>
                      <MenuItem value={LEVEL_TYPES.ADVANCED}>Advanced</MenuItem>
                      <MenuItem value={LEVEL_TYPES.INTERMEDIATE}>
                        Intermediate
                      </MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Grid>
            )}
          </Grid>
        </>
      </InformationEditGroup>

      {/** Proficient Languages Selection group */}
      <InformationEditGroup
        index="4"
        title="Proficiency Level"
        isPending={Array.isArray(dbUser?.langauges) && dbUser.langauges.length}
      >
        <LanguageProficiencyManager
          userId={dbUser?._id}
          value={languages}
          onChange={setLanguages}
        />
      </InformationEditGroup>
    </>
  );
};

export default LanguageEdit;
