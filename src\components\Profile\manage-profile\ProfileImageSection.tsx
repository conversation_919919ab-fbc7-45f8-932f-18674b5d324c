import CustomButton from "@/components/CustomButton";
import { Avatar, Box, Typography, styled } from "@mui/material";
import React from "react";

const ProfileImage = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  gap: theme.spacing(8),
  marginBottom: theme.spacing(8),
  [theme.breakpoints.down("sm")]: {
    flexDirection: "column",
    gap: theme.spacing(4),
    alignItems: "center",
  },
  "& .MuiAvatar-root": {
    width: 160,
    height: 160,
    border: "1px dashed #E0E0E0",
    backgroundColor: "#F5F5F5",
  },
  "& .MuiButton-root": {
    textTransform: "none",
    borderRadius: "8px",
    padding: "8px 16px",
  },
}));

const ProfileImageSection = ({ profileImage }) => {
  return (
    <Box>
      <ProfileImage>
        <Box
          sx={{
            display: `flex`,
            flexDirection: `column`,
            alignItems: `center`,
            gap: 3,
          }}
        >
          <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 700 }}>
            Profile Image
          </Typography>
          <Avatar src={profileImage || undefined} />
        </Box>
        <Box sx={{ display: "flex", gap: 2, mt: 6 }}>
          <CustomButton colortype="secondary" text="Change Image" />

          <CustomButton text="Remove Image" />
        </Box>
      </ProfileImage>
    </Box>
  );
};

export default ProfileImageSection;
