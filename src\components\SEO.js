import Head from "next/head";

const SEO = ({
  title = "",
  description = "",
  keywords = "",
  url = "https://www.patitofeo.com/",
  imgUrl = "https://www.patitofeo.com/android-chrome-192x192.png",

  ogTitle = "",
  ogDescription = "",

  twitterTitle = "",
  twitterDescription = "",
}) => {
  ogTitle = ogTitle ?? title;
  ogDescription = ogDescription ?? description;
  twitterTitle = twitterTitle ?? title;
  twitterDescription = twitterDescription ?? description;

  return (
    <Head>
      {title && <title>{title}</title>}
      {description && <meta name="description" content={description} />}
      {keywords && <meta name="keywords" content={keywords} />}

      {ogTitle && <meta property="og:title" content={ogTitle} />}
      {ogDescription && (
        <meta property="og:description" content={ogDescription} />
      )}

      {twitterTitle && <meta name="twitter:card" content={twitterTitle} />}
      {twitterDescription && (
        <meta name="twitter:description" content={twitterDescription} />
      )}
      <meta property="og:type" content="website" />
      <meta property="og:url" content={url} />
      <meta property="og:image" content={imgUrl} />
      <link rel="canonical" href={url} />
    </Head>
  );
};

export default SEO;
