import React from "react";
import { styled, SxProps, Theme } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import IconButton from "@mui/material/IconButton";
import SearchIcon from "@mui/icons-material/Search";
import Paper from "@mui/material/Paper";
import { Box, CircularProgress } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

const SearchContainer = styled(Paper)(({ theme }) => ({
  position: "relative",
  display: "flex",
  alignItems: "center",
  padding: "2px 4px",
}));

const SInput = styled(InputBase)(({ theme }) => ({
  marginLeft: theme.spacing(1),
  fontSize: 12,
  flex: 1,
  "& .MuiInputBase-input::placeholder": {
    color: "rgba(109, 109, 109, 1)",
    opacity: 1, // Ensures the color isn't affected by default opacity
    fontSize: 12,
  },
}));

const SearchButton = styled(IconButton)(({ theme }) => ({
  padding: 10,
}));

type SearchInputProps = React.FC<{
  placeholder: string;
  searchValue: string;
  setSearchValue: React.Dispatch<React.SetStateAction<string>>;
  handleSearch: () => void;
  handleClear?: () => void;
  isLoading?: boolean;
  showLoadingProgress?: boolean;
  disabled: boolean;
  style?: SxProps<Theme>;
}>;

const SearchInput: SearchInputProps = ({
  placeholder,
  searchValue,
  setSearchValue,
  handleSearch,
  disabled,
  style,
  handleClear,
  isLoading = false,
  showLoadingProgress = false,
}) => {
  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <SearchContainer
      aria-disabled={isLoading}
      sx={{
        boxShadow: "none",
        border: "1px solid rgba(217, 217, 217, 1)",
        borderRadius: 10,
        padding: "6px",
        width: {
          xs: "100%",
          sm: 400,
        },
        gap: 1,
        ...style,
      }}
    >
      <SInput
        disabled={disabled}
        placeholder={placeholder}
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        onKeyPress={handleKeyPress}
        inputProps={{ "aria-label": "search" }}
        style={{
          padding: 0,
        }}
      />
      {searchValue.length > 0 && handleClear && (
        <Box
          onClick={handleClear}
          sx={{
            height: 30,
            width: 30,
            borderRadius: 20,
            border: "1px solid rgba(20, 167, 156, 1)",
            cursor: "pointer",
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            color: "rgba(20, 167, 156, 1)",
          }}
        >
          <CloseIcon />
        </Box>
      )}
      <SearchButton
        style={{ padding: "4px", background: "rgba(20, 167, 156, 1)" }}
        onClick={handleSearch}
        aria-label="search"
      >
        {isLoading && showLoadingProgress ? (
          <CircularProgress style={{ color: "#fff" }} size={24} />
        ) : (
          <SearchIcon style={{ color: "#fff" }} />
        )}
      </SearchButton>
    </SearchContainer>
  );
};

export default SearchInput;
