// Love face
export const LoveFace = () => (
  <svg
    width='24'
    height='25'
    viewBox='0 0 24 25'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <g clip-path='url(#clip0_10504_5223)'>
      <path
        d='M15.5 11.4453C16.3284 11.4453 17 10.7737 17 9.94531C17 9.11689 16.3284 8.44531 15.5 8.44531C14.6716 8.44531 14 9.11689 14 9.94531C14 10.7737 14.6716 11.4453 15.5 11.4453Z'
        fill='white'
      />
      <path
        d='M8.5 11.4453C9.32843 11.4453 10 10.7737 10 9.94531C10 9.11689 9.32843 8.44531 8.5 8.44531C7.67157 8.44531 7 9.11689 7 9.94531C7 10.7737 7.67157 11.4453 8.5 11.4453Z'
        fill='white'
      />
      <path
        d='M11.99 2.44531C6.47 2.44531 2 6.92531 2 12.4453C2 17.9653 6.47 22.4453 11.99 22.4453C17.52 22.4453 22 17.9653 22 12.4453C22 6.92531 17.52 2.44531 11.99 2.44531ZM12 20.4453C7.58 20.4453 4 16.8653 4 12.4453C4 8.02531 7.58 4.44531 12 4.44531C16.42 4.44531 20 8.02531 20 12.4453C20 16.8653 16.42 20.4453 12 20.4453ZM7 14.4453C7.78 16.7853 9.72 18.4453 12 18.4453C14.28 18.4453 16.22 16.7853 17 14.4453H7Z'
        fill='white'
      />
    </g>
    <defs>
      <clipPath id='clip0_10504_5223'>
        <rect
          width='24'
          height='24'
          fill='white'
          transform='translate(0 0.445312)'
        />
      </clipPath>
    </defs>
  </svg>
);

// Neutral Face
export const NeutralFace = () => (
  <svg
    width='25'
    height='25'
    viewBox='0 0 25 25'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <g clip-path='url(#clip0_10504_5233)'>
      <path d='M9.5 15.9453H15.5V16.9453H9.5V15.9453Z' fill='white' />
      <path
        d='M16 11.4453C16.8284 11.4453 17.5 10.7737 17.5 9.94531C17.5 9.11689 16.8284 8.44531 16 8.44531C15.1716 8.44531 14.5 9.11689 14.5 9.94531C14.5 10.7737 15.1716 11.4453 16 11.4453Z'
        fill='white'
      />
      <path
        d='M9 11.4453C9.82843 11.4453 10.5 10.7737 10.5 9.94531C10.5 9.11689 9.82843 8.44531 9 8.44531C8.17157 8.44531 7.5 9.11689 7.5 9.94531C7.5 10.7737 8.17157 11.4453 9 11.4453Z'
        fill='white'
      />
      <path
        d='M12.49 2.44531C6.97 2.44531 2.5 6.92531 2.5 12.4453C2.5 17.9653 6.97 22.4453 12.49 22.4453C18.02 22.4453 22.5 17.9653 22.5 12.4453C22.5 6.92531 18.02 2.44531 12.49 2.44531ZM12.5 20.4453C8.08 20.4453 4.5 16.8653 4.5 12.4453C4.5 8.02531 8.08 4.44531 12.5 4.44531C16.92 4.44531 20.5 8.02531 20.5 12.4453C20.5 16.8653 16.92 20.4453 12.5 20.4453Z'
        fill='white'
      />
    </g>
    <defs>
      <clipPath id='clip0_10504_5233'>
        <rect
          width='24'
          height='24'
          fill='white'
          transform='translate(0.5 0.445312)'
        />
      </clipPath>
    </defs>
  </svg>
);

// Dislike face
export const DislikeFace = () => (
  <svg
    width='24'
    height='25'
    viewBox='0 0 24 25'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <g clip-path='url(#clip0_10504_5243)'>
      <path
        d='M15.5 11.4453C16.3284 11.4453 17 10.7737 17 9.94531C17 9.11689 16.3284 8.44531 15.5 8.44531C14.6716 8.44531 14 9.11689 14 9.94531C14 10.7737 14.6716 11.4453 15.5 11.4453Z'
        fill='white'
      />
      <path
        d='M8.5 11.4453C9.32843 11.4453 10 10.7737 10 9.94531C10 9.11689 9.32843 8.44531 8.5 8.44531C7.67157 8.44531 7 9.11689 7 9.94531C7 10.7737 7.67157 11.4453 8.5 11.4453Z'
        fill='white'
      />
      <path
        d='M11.99 2.44531C6.47 2.44531 2 6.92531 2 12.4453C2 17.9653 6.47 22.4453 11.99 22.4453C17.52 22.4453 22 17.9653 22 12.4453C22 6.92531 17.52 2.44531 11.99 2.44531ZM12 20.4453C7.58 20.4453 4 16.8653 4 12.4453C4 8.02531 7.58 4.44531 12 4.44531C16.42 4.44531 20 8.02531 20 12.4453C20 16.8653 16.42 20.4453 12 20.4453ZM12 14.4453C9.67 14.4453 7.68 15.8953 6.88 17.9453H8.55C9.24 16.7553 10.52 15.9453 12 15.9453C13.48 15.9453 14.75 16.7553 15.45 17.9453H17.12C16.32 15.8953 14.33 14.4453 12 14.4453Z'
        fill='white'
      />
    </g>
    <defs>
      <clipPath id='clip0_10504_5243'>
        <rect
          width='24'
          height='24'
          fill='white'
          transform='translate(0 0.445312)'
        />
      </clipPath>
    </defs>
  </svg>
);
