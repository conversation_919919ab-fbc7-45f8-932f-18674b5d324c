// import React, { useState, useEffect } from "react";
// import TextField from "@mui/material/TextField";
// import Button from "@mui/material/Button";
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { userSignInValidator } from "@/api/validators";

// function SignInForm() {
//   const {
//     register,
//     handleSubmit,
//     formState: { errors, isSubmitting },
//   } = useForm({ resolver: zodResolver(userSignInValidator), mode: "all" });

//   const onSubmit = (data) => {
//     // Handle form submission logic here (e.g., call API to authenticate)
//     console.log("Submitted data:", data);
//   };

//   return (
//     <form onSubmit={handleSubmit(onSubmit)}>
//       <TextField
//         {...register("email", { required: true })}
//         label="Email"
//         variant="outlined"
//         margin="normal"
//         fullWidth
//         error={!!errors.email}
//         helperText={errors.email?.message as string}
//         autoFocus
//       />
//       <TextField
//         {...register("password", { required: true })}
//         label="Password"
//         variant="outlined"
//         margin="normal"
//         fullWidth
//         type="password"
//         error={!!errors.password}
//         helperText={errors.password?.message as string}
//       />
//       <Button type="submit"
//         sx={{
//           fontWeight: 600,
//           fontSize: "1.2rem",
//           color: "white",
//           width: "160px",
//           borderRadius: "10px",
//           backgroundColor: "#F3B358",
//           border: "none",
//           "&:hover": {
//             backgroundColor: "#02AC9F",
//           }
//         }}
//         disabled={isSubmitting}
//       >
//         Sign In
//       </Button>
//     </form>
//   );
// }

// export default SignInForm;
