import React from "react";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import ShareIcon from "@mui/icons-material/Share";
import { styled, alpha } from "@mui/material/styles";

import {
  PinterestShareButton,
  FacebookShareButton,
  TwitterShareButton,
  PinterestIcon,
  TwitterIcon,
  FacebookIcon,
} from "react-share";

const StyledMenu = styled((props) => (
  <Menu
    elevation={6}
    getContentAnchorEl={null}
    anchorOrigin={{
      vertical: "bottom",
      horizontal: "center",
    }}
    transformOrigin={{
      vertical: "top",
      horizontal: "center",
    }}
    {...props}
  />
))(({ theme }) => ({
  "& .MuiPaper-root": { border: "1px solid #d3d4d5" },
}));

const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  "&:focus": {
    backgroundColor: theme.palette.common.white,
    "& .MuiListItemIcon-root, & .MuiListItemText-primary": {
      color: theme.palette.common.white,
    },
  },
}));

export default function SocialShareBtns({ url, img }) {
  const [anchorEl, setAnchorEl] = React.useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <IconButton aria-label="share" onClick={handleClick}>
        <ShareIcon />
      </IconButton>

      <StyledMenu
        id="customized-menu"
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
      >
        <StyledMenuItem>
          <FacebookShareButton url={url} title={"check this out"}>
            <Button>
              <FacebookIcon size={32} round={true} />
            </Button>
          </FacebookShareButton>
        </StyledMenuItem>

        <StyledMenuItem>
          <TwitterShareButton url={url} title={"check this out"}>
            <Button>
              <TwitterIcon size={32} round={true} />
            </Button>
          </TwitterShareButton>
        </StyledMenuItem>

        <StyledMenuItem>
          <PinterestShareButton media={img} url={url} title={"check this out"}>
            <Button>
              <PinterestIcon size={32} round={true} />
            </Button>
          </PinterestShareButton>
        </StyledMenuItem>
      </StyledMenu>
    </div>
  );
}
