import React, { useState } from "react";
import { Box, Typography, TextField, Button } from "@mui/material";

const SvgIconComponent = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth="1.5"
    stroke="currentColor"
    className="size-3"
    style={{ width: "24px", height: "24px" }}
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m5.25 4.5 7.5 7.5-7.5 7.5m6-15 7.5 7.5-7.5 7.5"
    />
  </svg>
);

export default function TextBoxWithButton({
  handleSubmit,
  buttonText,
  placeholderText = "",
  fieldType = "email",
  size = "",
}) {
  const [fieldValue, setFieldValue] = useState("");

  const handleChange = (event) => {
    setFieldValue(event.target.value);
  };

  const onSubmit = async (event) => {
    event.preventDefault();
    handleSubmit(fieldValue)
  };

  return (
    <Box display="flex" justifyContent="center" alignItems="center">
      <form
        onSubmit={onSubmit}
        style={{
          display: "flex",
          borderRadius: "10px 0 0 10px",
          boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
          width: "100%",
          maxWidth: "600px",
        }}
      >
        <TextField
          placeholder={placeholderText}
          onChange={handleChange}
          value={fieldValue}
          required
          type={fieldType}
          variant="outlined"
          fullWidth
          size={size}
          sx={{
            borderRadius: "10px 0 0 10px",
            "& fieldset": { border: "none" },
            width: "100%",

          }}
          InputProps={{
            sx: {
              '& input': {
                fontSize: { xs: '12px', md: '14px' },
                height: '40px',
                padding: '0px 16px',
                textAlign: 'center',
              },
            },
          }}
        />
        {buttonText ? (
          <Button
            type="submit"
            variant="contained"
            sx={{
              color: "white",
              borderRadius: "0 4px 4px 0",
              px: 4,
              my: 0,
              whiteSpace: "nowrap",
              overflow: "hidden",

            }}
              aria-label={buttonText}
          >
            <Typography variant="button" sx={{ fontSize: { xs: '12px', md: '14px' } }}>{buttonText}</Typography>
          </Button>
        ) : (
          <Button
            type="submit"
            variant="contained"
            aria-label="submit email"
            endIcon={<SvgIconComponent />}
            sx={{
              color: "white",
              borderRadius: "0 4px 4px 0",
              px: 4,

              my: 0,
              whiteSpace: "nowrap",
              overflow: "hidden",

            }}
          >

          </Button>
        )}



      </form>
    </Box>
  );
}
