import React from "react";
import { a, useSprings, config } from "react-spring";
import { Box, Typography } from "@mui/material";
import styles from "./TrailComponent.module.css";

const PATITO_COLORS = ["#02B199", "#03E0C2"];
const DUCKLING_COLORS = ["#FFE608", "#F6B80C"];

const Trail = ({ open, children }) => {
  const items = React.Children.toArray(children);
  const trails = useSprings(
    items.length,
    items.map((item, i) => ({
      config: config.slow,
      reset: true,
      height: open ? 150 : 0,
      from: {
        opacity: 0,
        transform: i < 2 ? "rotate(0deg)" : "rotate(-180deg) ",
        x: 20,
        height: 0,
      },
      to: async (next) => {
        await new Promise((resolve) => setTimeout(resolve, 300 * i));
        await next({
          opacity: 1,
          transform: i < 2 ? "rotate(0deg)" : "rotate(-180deg) ",
          x: 20,
          height: 150,
        });
        await new Promise((resolve) => setTimeout(resolve, 500));
        if (i < 2) {
          await next({
            opacity: 1,
            x: 20,
            height: 150,
            transform: "rotate(-180deg) ",
          });
          await new Promise((resolve) => setTimeout(resolve, 800));
        } else {
          await new Promise((resolve) => setTimeout(resolve, 500));
          await next({
            opacity: 1,
            x: 20,
            height: 150,
            transform: "rotate(-180deg) ",
          });
        }
        await next({
          opacity: 1,
          x: 20,
          height: 150,
          transform: "rotate(0deg)",
        });
        await next({
          opacity: 0,
        });
      },
    }))
  );
  return (
    <Box
      sx={{
        paddingLeft: "20px",
        paddingRight: "20px",
        height: "100vh",
      }}
    >
      {trails.map(({ height, ...style }, index) => {
        return (
          <a.div key={index} style={style} className={`${styles.trailsText}`}>
            <a.div>{items[index]}</a.div>
          </a.div>
        );
      })}
    </Box>
  );
};

const TextGradient = (props) => {
  const [colorA, colorB] = props.colors;

  return (
    <Typography
      sx={{
        background: `linear-gradient(90deg, ${colorA}, ${colorB})`,
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        fontWeight: "800",
        fontSize: { xs: "70px", md: "96px" },
      }}
      {...props}
    />
  );
};

const TrailComponent = ({ isOpened }) => (
  <div className={styles.container}>
    <Trail open={isOpened}>
      <span>
        <TextGradient colors={PATITO_COLORS}>Patito</TextGradient>
      </span>
      <span>
        <TextGradient colors={PATITO_COLORS}>Feo</TextGradient>
      </span>
      <span>
        <TextGradient colors={DUCKLING_COLORS}>Ugly</TextGradient>
      </span>
      <span>
        <TextGradient colors={DUCKLING_COLORS}>Duckling</TextGradient>
      </span>
    </Trail>
  </div>
);

export default TrailComponent;
