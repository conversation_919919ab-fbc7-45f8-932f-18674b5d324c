import React from 'react';
import Box from '@mui/material/Box';
import Slider from '@mui/material/Slider';


const marks = [
  {
    value: 80,
    label: '80%',
  },
  {
    value: 85,
    label: '85%',
  },
  {
    value: 90,
    label: '90%',
  },
  {
    value: 95,
    label: '95%',
  },
  {
    value: 100,
    label: '100%',
  },
]


export default function VerticalSlider(props) {
  function preventHorizontalKeyboardNavigation(event) {
    if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
      event.preventDefault();
    }
  }

  return (
    <Box sx={{ height: 200 }}>
      <Slider
        sx={{
          '& input[type="range"]': {
            WebkitAppearance: 'slider-vertical',
          },
        }}
        orientation="vertical"
        defaultValue={100}
        aria-label="playback-speed"
        valueLabelDisplay="100%"
        min={80}
        max={100}
        step={5}
        onKeyDown={preventHorizontalKeyboardNavigation}
        marks={marks}
      />
    </Box>
  );
}
