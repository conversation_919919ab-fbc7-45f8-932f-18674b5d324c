import React, { useEffect, useState } from "react";

import {
  BigPlayButton,
  ControlBar,
  CurrentTimeDisplay,
  ForwardControl,
  FullscreenToggle,
  Loading<PERSON>pinner,
  PlaybackRateMenuButton,
  Player,
  ReplayControl,
  TimeDivider,
  VolumeMenuButton,
  ClosedCaptionButton,
  DurationDisplay,
  PlayToggle,
  PosterImage,
  ProgressControl,
  Shortcut,
} from "video-react";
import { Box } from "@mui/material";
import Grid from "@mui/material//Grid";

import { CompTestSection } from "./compTestSection";
import useIsDesktop from "../hooks/useIsDesktop";
import ButtonIcon from "./ButtonIcon";
import PlaybackSpeed from "./PlaybackSpeed";
import "video-react/dist/video-react.css";

// button icons
import forwardButtonIcon from "../../public/images/icons/button-icons/tenforward.svg";
import backwardButtonIcon from "../../public/images/icons/button-icons/tenbackward.svg";
import playButtonIcon from "../../public/images/icons/button-icons/play.svg";
import pauseButtonIcon from "../../public/images/icons/button-icons/pause.svg";
import closedCaptionsButtonIcon from "../../public/images/icons/button-icons/closedcaptions.svg";
import replayButtonIcon from "../../public/images/icons/button-icons/rewind.svg";

const playbackContainerStyles = { marginTop: { xs: "10px", sm: "0px" } };
const playbackButtonContainerStyles = {
  display: "flex",
  flexDirection: "row",
  width: { xs: "100%", sm: "85%" },
  gap: { xs: "20px", sm: "40px" },
  justifyContent: "center",
};
const playbackSectionContainerStyles = {
  display: "flex",
  flexDirection: { xs: "column", sm: "row" },
  width: { xs: "100%", sm: "85%" },
  justifyContent: "space-between",
  alignItems: { xs: "none", sm: "center" },
};

const playbackSectionStyles = {
  display: "flex",
  justifyContent: "center",
  backgroundColor: "white",
  borderRadius: "21px",
  margin: "10px 0px",
  padding: { xs: "10px 10px", sm: "10px 0px" },
  boxShadow: "0px 6px 48px 0px #0000000D",
};

export function VideoPlayer({
  src,
  onPlayerChange = () => {},
  onChange = () => {},
  startTime = undefined,
}) {
  const [player, setPlayer] = useState(undefined);
  const [playerState, setPlayerState] = useState(undefined);
  const [testOpen, setTestOpen] = useState(false);
  const isDesktop = useIsDesktop();
  const [playerSpeed, setPlayerSpeed] = useState(100);

  // Remove manual showPlay state - we'll derive it from playerState
  const showPlay = playerState ? playerState.paused : true;

  useEffect(() => {
    if (player) {
      const videoElement = player.video.video;
      videoElement.disablePictureInPicture = true;
      const handleContextMenu = (e) => e.preventDefault();
      videoElement.addEventListener("contextmenu", handleContextMenu);

      const handleDoubleClick = (e) => {
        e.preventDefault();
        e.stopPropagation();
      };
      videoElement.addEventListener("dblclick", handleDoubleClick);

      return () => {
        videoElement.removeEventListener("contextmenu", handleContextMenu);
        videoElement.removeEventListener("dblclick", handleDoubleClick);
      };
    }
  }, [player]);

  useEffect(() => {
    if (playerState) {
      onChange(playerState);
    }
  }, [playerState, onChange]);

  useEffect(() => {
    onPlayerChange(player);
    if (player) {
      player.subscribeToStateChange(setPlayerState);
    }
  }, [player, onPlayerChange]);

  const play = () => {
    player.play();
  };

  const pause = () => {
    player.pause();
  };

  const changeCurrentTime = (seconds) => {
    if (player) {
      const curTime = playerState.currentTime;
      player.seek(curTime + seconds);
    }
  };

  const replay = () => {
    player.seek(0);
    player.play();
  };

  const selectPlaybackRate = (pct) => {
    changePlaybackRate(pct);
  };

  const changePlaybackRate = (pct) => {
    player.playbackRate = pct;
  };

  const testYourself = () => {
    setTestOpen(!testOpen);
  };

  const decreaseClickHandler = () => {
    if (playerSpeed <= 0) {
      return;
    } else {
      setPlayerSpeed((prevCount) => prevCount - 1);
    }
  };

  const increaseClickHandler = () => {
    if (playerSpeed >= 100) {
      return;
    } else {
      setPlayerSpeed((prevCount) => prevCount + 1);
    }
  };

  return (
    <div className={"video-player"}>
      <Player
        sx={{ borderRadius: " 15px", overflow: "hidden" }}
        ref={(player) => {
          setPlayer(player);
        }}
        startTime={startTime}
        playsInline
        poster="/testLogo2.png"
        disableDefaultControls
      >
        <source src={src} />
        {/* <BigPlayButton position="center" /> */}
        <LoadingSpinner />
        <ControlBar>
          {/* <CurrentTimeDisplay order={4.1} />
          <TimeDivider order={4.2} />
          <PlaybackRateMenuButton rates={[5, 2, 1, 0.5, 0.1]} order={7.1} />
          <VolumeMenuButton order={7} /> */}

          {/* <PlayToggle /> */}
          <CurrentTimeDisplay />
          <TimeDivider />
          <DurationDisplay />

          {/* <ForwardControl /> */}
          {/* <FullscreenToggle /> */}
          <PlaybackRateMenuButton rates={[5, 2, 1, 0.5, 0.1]} order={7.1} />
          {/* <ReplayControl /> */}
          {/* <ClosedCaptionButton /> */}
          <ProgressControl />
          {/* <Shortcut /> */}
          <VolumeMenuButton />
        </ControlBar>
      </Player>
      <div id="videoButtonBar">
        <Grid
          container
          spacing={0}
          justifyContent="space-between"
          alignItems="center"
        >
          <Grid container spacing={2} sx={playbackSectionStyles}>
            <Box sx={playbackSectionContainerStyles}>
              <Box sx={playbackButtonContainerStyles}>
                <ButtonIcon
                  icon={backwardButtonIcon}
                  iconWidth="58"
                  iconHeight="58"
                  onClick={() => changeCurrentTime(-10)}
                ></ButtonIcon>

                <ButtonIcon
                  icon={showPlay ? playButtonIcon : pauseButtonIcon}
                  onClick={showPlay ? play : pause}
                  iconWidth="58"
                  iconHeight="58"
                ></ButtonIcon>

                <ButtonIcon
                  icon={forwardButtonIcon}
                  iconWidth="58"
                  iconHeight="58"
                  onClick={() => changeCurrentTime(10)}
                ></ButtonIcon>

                <ButtonIcon
                  icon={replayButtonIcon}
                  onClick={replay}
                  iconWidth="58"
                  iconHeight="58"
                ></ButtonIcon>

                <ButtonIcon
                  icon={closedCaptionsButtonIcon}
                  iconWidth="58"
                  iconHeight="58"
                ></ButtonIcon>
              </Box>

              <Box sx={playbackContainerStyles}>
                <PlaybackSpeed
                  playerSpeed={playerSpeed}
                  increaseClickHandler={increaseClickHandler}
                  decreaseClickHandler={decreaseClickHandler}
                ></PlaybackSpeed>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </div>

      {testOpen && (
        <CompTestSection
          id="outlined-multiline-flexible"
          defaultLabel="What did you hear?"
          answer="esta es una buena palabra"
        />
      )}
    </div>
  );
}
