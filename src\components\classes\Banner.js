import React from "react";
import { Typography, Box } from "@mui/material";

export default function Banner({ title, backgroundImage }) {
  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: { xs: "250px", sm: "300px" },
        backgroundImage: `url(${backgroundImage.src})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      <Typography
        sx={{
          fontSize: { xs: "45px", sm: "48px" },
          fontWeight: { xs: "500", sm: "700" },
          textAlign: "center",
          color: "#000000",
          maxWidth: "350px",
        }}
      >
        {title}
      </Typography>
    </Box>
  );
}
