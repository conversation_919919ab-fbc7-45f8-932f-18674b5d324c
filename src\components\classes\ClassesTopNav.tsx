import { AppBar, Box, Typography } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/router";
import React from "react";

export const navs = [
  {
    id: 11,
    name: "Online Clubs",
    href: "/classes/online-clubs",
  },
  {
    id: 1,
    name: "In- Person Classes",
    href: "/classes/clases-lineares",
  },
  {
    id: 2,
    name: "Community Classes",
    href: "/classes/experiencias-libres",
  },
  {
    id: 3,
    name: "Online Classes",
    href: "/classes/online-clases",
  },
];

const ClassesTopNav = () => {
  const { pathname } = useRouter();

  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      className="scrollbar-hidden"
      sx={{
        background: "rgba(20, 167, 156, 1)",
        height: {
          xs: 40,
          md: 50,
        },
        gap: {
          xs: 2,
          md: 10,
        },
        overflowX: "scroll",
      }}
    >
      {navs.map((m) => (
        <Nav key={m.id} data={m} pathname={pathname} />
      ))}
    </Box>
  );
};

const Nav = ({ data, pathname }) => {
  const isActive = pathname === data.href;

  return (
    <Link
      href={data.href}
      className="nowrap"
      style={{
        color: "#fff",
        borderBottom: isActive ? "4px solid #fff" : "",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        padding: "0 0.75rem",
      }}
    >
      <Typography
        sx={{
          fontSize: {
            xs: "0.75rem",
            md: "0.95rem",
          },
        }}
      >
        {data.name}
      </Typography>
    </Link>
  );
};

export default ClassesTopNav;
