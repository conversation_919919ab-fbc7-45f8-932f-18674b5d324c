import { Box, Grid, Skeleton } from "@mui/material";
import React from "react";

const ExperiencesLoading = () => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="center"
      flexWrap="wrap"
      gap={10}
      mt={10}
    >
      {new Array(6).fill("").map((m, i) => (
        <CardLoading key={i} />
      ))}
    </Box>
  );
};

const CardLoading = () => {
  return (
    <Box
      sx={{
        height: 350,
        width: {
          xs: "100%",
          md: 350,
          sm: 275,
        },
        borderRadius: 5,
        p: 4,
        boxShadow: "0px 2px 10px 0px #00000029",
        flexDirection: "column",
        display: "flex",
        justifyContent: "space-between",
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
      >
        <Skeleton variant="rectangular" width="100%" height={200} />
      </Box>
      <Skeleton
        variant="rectangular"
        sx={{ marginTop: "0.5rem" }}
        width="100%"
        height={50}
      />

      <Box display="flex" flexDirection="row" justifyContent="space-between">
        <Skeleton
          variant="rectangular"
          sx={{ marginTop: "0.5rem" }}
          width="40%"
          height={20}
        />
        <Skeleton
          variant="rectangular"
          sx={{ marginTop: "0.5rem" }}
          width="40%"
          height={20}
        />
      </Box>
      <Skeleton
        variant="rectangular"
        sx={{ marginTop: "1rem" }}
        width="100%"
        height={80}
      />
      <Skeleton
        variant="rectangular"
        sx={{ marginTop: "1rem" }}
        width="100%"
        height={80}
      />
    </Box>
  );
};

export default ExperiencesLoading;
