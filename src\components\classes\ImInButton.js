import React from "react";
import { Button } from "@mui/material";

export const ImInButton = ({
  handleClick,
  marginBottom = { xs: "30px", sm: "60px" },
  marginTop = { xs: "10px", sm: "20px" },
}) => {
  return (
    <Button
      variant="contained"
      size="large"
      onClick={handleClick}
      sx={{
        marginBottom: marginBottom,
        marginTop: marginTop,
        textAlign: "center",
        fontSize: { xs: "24px", sm: "32px", md: "32px" },
        fontWeight: { xs: "700px" },
        whiteSpace: { sm: "nowrap" },
        width: { xs: "192px", sm: "400px", md: "400px" },
        height: { xs: "50px", sm: "50px", md: "50px" },
      }}
    >
      I’m In!
    </Button>
  );
};
