import { Grid, Typography, CardMedia, Box } from "@mui/material";
export default function ImageSection({ data }) {
  return (
    <>
      {data.map((section, index) => (
        <Grid
          key={index}
          container
          spacing={3}
          flexDirection={index % 2 === 1 ? "row" : "row-reverse"}
          justifyContent="center"
          alignItems="center"
        >
          <ImgDetail image={section.image} />
          <InfoDetail section={section} />
        </Grid>
      ))}
    </>
  );
}

const ImgDetail = ({ image }) => {
  return (
    <Grid item xs={12} lg={6} order={{ xs: 2, lg: 2 }}>
      <CardMedia
        component="img"
        image={image}
        alt="Clases Lineares"
        sx={{
          width: { xs: "310px", sm: "100%" },
          height: { xs: "310px", sm: "auto" },
          maxWidth: { xs: "480px" },
          maxHeight: { xs: "352px" },
          borderRadius: { xs: "40px 0px 40px 0px" },
          objectFit: "cover",
          margin: { xs: "auto" },
        }}
      />
    </Grid>
  );
};

const InfoDetail = ({ section }) => {
  return (
    <Grid item xs={12} lg={6} order={{ xs: 1, lg: 1 }}>
      <Box
        sx={{
          maxWidth: {
            xs: "300px",
            sm: "400px",
            md: "500px",
            lg: "500px",
            xl: "500px",
          },
          margin: "auto",
        }}
      >
        <Typography
          sx={{
            textAlign: "center",
            fontSize: { xs: "24px", sm: "32px" },
            margin: { xs: " 40px" },
          }}
        >
          {section.title}: <strong>{section.subtitle}</strong>
        </Typography>

        <Box sx={{ padding: { xs: "0px 40px 20px 40px " } }}>
          {section.body.map((content, index) => (
            <Typography
              key={index}
              variant="body1"
              sx={{
                textAlign: "center",
                fontSize: { sm: "16px" },
                fontWeight: { xs: "500", sm: "400" },
                paddingBottom: { xs: "10px" },
              }}
            >
              {content}
            </Typography>
          ))}
        </Box>
      </Box>
    </Grid>
  );
};
