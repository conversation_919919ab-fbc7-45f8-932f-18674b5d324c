import { Grid, Typography, CardMedia, Box } from "@mui/material";
import Head from "next/head";

const getStyles = (index) => {
  return {
    maxWidth:
      index % 2 === 1
        ? { xs: "180px", sm: "200px", md: "250px", lg: "305px" }
        : {
            xs: "100px",
            sm: "150px",
            md: "200px",
            lg: "240px",
          },
    height:
      index % 2 === 1
        ? { xs: "180px", sm: "200px", md: "250px", lg: "305px" }
        : {
            xs: "100px",
            sm: "150px",
            md: "200px",
            lg: "240px",
          },
    borderRadius: { xs: "40px 0px 40px 0px" },
    margin: { xs: "10px" },
    overflow: "hidden",
  };
};

const videoUrl = "/images/classes/mission/center.mp4";
const videoType = "video/mp4";

export default function MissionStatement({ data }) {
  return (
    <>
      <Head>
        <link rel="preload" href={videoUrl} as="video" type={videoType} />
      </Head>
      <Grid
        container
        sx={{
          marginTop: { sm: "80px" },
          marginBottom: { xs: "20px", sm: "0px" },
        }}
      >
        {/* Mission Images */}
        <Grid
          item
          order={{ xs: 2, sm: 1 }}
          xs={12}
          sx={{
            display: " flex",
            flexDirection: { xs: "row" },
            justifyContent: {
              xs: "center",
            },
            alignItems: {
              xs: "center",
            },
          }}
        >
          {data.images.map((image, index) => {
            if (index === 1) {
              return (
                <Box
                  key={index}
                  sx={{
                    ...getStyles(index),
                  }}
                >
                  <video
                    poster="/images/classes/mission/poster.webp"
                    height="100%"
                    width="100%"
                    autoPlay
                    muted
                    playsInline
                    style={{ objectFit: "cover" }}
                    loop
                  >
                    <source src={videoUrl} type={videoType} />
                  </video>
                </Box>
              );
            }
            return (
              <CardMedia
                key={index}
                component="img"
                image={image}
                alt="Nuestra Misión"
                sx={{
                  ...getStyles(index),
                }}
              />
            );
          })}
        </Grid>

        {/* Mission Content */}
        <Grid
          item
          order={{ xs: 1, sm: 2 }}
          xs={12}
          sx={{
            display: "flex",
            flexDirection: { xs: "column" },
            alignItems: { xs: "center" },
            padding: { xs: "0px 40px" },
          }}
        >
          {/* Mission Title */}
          <Typography
            sx={{
              marginBottom: "16px",
              fontSize: { xs: "24px", sm: "32px" },
              fontWeight: "700",
              paddingTop: { xs: "30px", sm: "10px" },
            }}
          >
            {data.title}
          </Typography>

          {/* Mission Description */}
          <Typography
            maxWidth="md"
            sx={{
              marginBottom: { xs: "20px", sm: "60px" },
              fontSize: { xs: "16px" },
              fontWeight: { xs: "500" },
            }}
          >
            {data.body}
          </Typography>
        </Grid>
      </Grid>
    </>
  );
}