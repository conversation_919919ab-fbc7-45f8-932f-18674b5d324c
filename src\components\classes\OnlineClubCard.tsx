import React, { useState } from "react";
import CustomButton from "../CustomButton";
import { Avatar, AvatarGroup, Box, Card, Typography } from "@mui/material";
import Image from "next/image";
import Tag from "../Tag";
import Rating from "./OnlineClub/Rating";
import CheckoutClubsModal from "../CheckoutModal/CheckoutClub/CheckoutClubsModal";
import { ClubType, UserType } from "@/api/mongoTypes";
import { getClubImage, getPriceSymbol } from "@/utils/classes";
import CreatorDetails from "./OnlineClub/CreatorDetails";

type OnlineClubCardProps = React.FC<{
  data: ClubType;
  onClick?: () => void;
  redirectToSignup?: () => void;
  isAdmin: boolean;
}>;
const OnlineClubCard: OnlineClubCardProps = ({
  data,
  onClick,
  isAdmin = false,
  redirectToSignup = () => {},
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const imageUrl = getClubImage(data);

  const creatorDetails = data.teachers.map((m) => m as UserType);

  const rating = data?.ratings?.value ?? 0;

  return (
    <>
      <Card
        onClick={() => {
          if (onClick) {
            onClick();
          }
        }}
        sx={{
          width: {
            xs: "100%",
            md: 350,
            sm: 275,
          },
          borderRadius: 5,
          p: 4,
          boxShadow: "0px 2px 10px 0px #00000029",
          flexDirection: "column",
          display: "flex",
          justifyContent: "space-between",
          cursor: "pointer",
        }}
      >
        <Box
          width="100%"
          height={250}
          mb={2}
          sx={{
            position: "relative",
            overflow: "hidden",
            borderRadius: 3,
          }}
        >
          <Image
            alt="beruh"
            src={imageUrl}
            quality={75}
            objectFit="cover"
            priority
            fill
          />

          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            width="100%"
            p={1}
            sx={{
              position: "absolute",
              bottom: 2,
            }}
          >
            <Box
              sx={{
                background: "rgba(0, 0, 0, 0.52)",
                color: "#fff",
                p: 2,
                borderRadius: 2,
              }}
            >
              <Typography sx={{ fontSize: 12 }}>
                <span style={{ fontSize: 10, fontWeight: 400 }}>
                  starting at&nbsp;
                </span>
                {getPriceSymbol({
                  currency: data.currency,
                })}
                {String(data.price)}
              </Typography>
            </Box>
          </Box>
        </Box>

        <Box display="flex" flexDirection="row" alignItems="center">
          <span
            style={{
              height: 6,
              width: 6,
              borderRadius: 3,
              background: "rgba(249, 178, 56, 1)",
            }}
          />
          &nbsp;
          <Typography fontSize={14} color="rgba(109, 109, 109, 1)">
            ONLINE
          </Typography>
        </Box>

        <Typography
          textAlign="start"
          sx={{ fontWeight: "bolder", fontSize: 16 }}
        >
          {data.title}
        </Typography>
        <CreatorDetails creatorDetails={creatorDetails} />

        <Box
          display="flex"
          flexDirection="row"
          sx={{ overflow: "hidden" }}
          alignItems="center"
          mt={5}
        >
          {data.categories?.map((m) => (
            <Tag text={m} key={m} />
          ))}
        </Box>

        <Box
          mt={5}
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
        >
          {rating > 0 && (
            <Box display="flex" flexDirection="row" alignItems="center">
              <Rating ratings={rating} />
              &nbsp;
              <Typography fontSize={12}>rating</Typography>
            </Box>
          )}
          {+data?.noOfStudentsEnrolled > 0 && (
            <Typography color="rgba(129, 129, 129, 1)" fontSize={12}>
              {`${data?.noOfStudentsEnrolled} Students enrolled`}
            </Typography>
          )}
        </Box>

        {!isAdmin && (
          <Box display="flex" flexDirection="column">
            <CustomButton
              text="Subscribe"
              onClick={(e) => {
                e.stopPropagation();
                setIsModalOpen(true);
              }}
              sx={{
                width: "100%",
                mt: 3,
              }}
            />
          </Box>
        )}
      </Card>
      {isModalOpen && (
        <CheckoutClubsModal
          open={isModalOpen}
          setOpen={setIsModalOpen}
          redirectToSignup={redirectToSignup}
          data={data}
        />
      )}
    </>
  );
};

export default OnlineClubCard;
