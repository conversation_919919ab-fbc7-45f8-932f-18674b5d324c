import React from "react";
import {
  Container,
  Grid,
  Typography,
  Button,
  CardMedia,
  Card,
  CardContent,
  Box,
} from "@mui/material";

export default function Pricing({ classesPackages }) {
  return (
    <>
      <Typography
        sx={{
          marginBottom: { xs: "30px", sm: "50px" },
          fontSize: { xs: "32px", sm: "60px" },
          fontWeight: { xs: "700", sm: "500" },
          lineHeight: 1,
        }}
      >
        Elige tu Camino de Aprendizaje
      </Typography>

      <Typography
        variant="body1"
        sx={{
          padding: { xs: "0px 40px", sm: "0px 30px" },
          marginBottom: { xs: "40px", sm: "80px" },
          fontSize: { sm: "32px" },
          fontWeight: { xs: "500", sm: "400" },
          lineHeight: { lg: "1.2" },
        }}
      >
        Explora Nuestra Diversa Escuela de Idiomas en Español e Inglés: Cursos
        de Calidad con Inmersión Cultural.
      </Typography>

      {/* Pricing Section */}
      <Grid container justifyContent="left" sx={{ margin: { sm: "50px 0px" } }}>
        {classesPackages.map((classItem, index) => (
          <Grid
            key={index}
            item
            xs={12}
            sm={6}
            md={4}
            display="flex"
            justifyContent="center"
          >
            <Card
              sx={{
                textAlign: "center",

                outline:
                  index % 2 === 0 ? "3px solid #14A79C" : "3px solid #F9B238",
                margin: "10px",
                width: "100%",
                padding: { sm: "10px" },
                maxWidth: { xs: "350px" }, // Full width on mobile, max width on larger screens
              }}
            >
              <CardContent>
                <Typography
                  component="div"
                  sx={{
                    textAlign: "left",
                    fontSize: {
                      xs: "24px",
                      sm: "25px",
                      md: "20px",
                      lg: "25px",
                    },
                    fontWeight: { xs: "600", sm: "700" },
                  }}
                >
                  {classItem.title}
                </Typography>

                <Typography
                  sx={{
                    textAlign: "left",
                    fontSize: { xs: "14px", sm: "14px" },
                    fontWeight: { xs: "600", sm: "400" },
                  }}
                >
                  {classItem.desc}
                </Typography>
                <Box sx={{ display: "flex", margin: "20px 0px" }}>
                  <Typography
                    variant="h3"
                    sx={{
                      fontSize: { xs: "20px", sm: "32px" },
                      fontWeight: "500",
                      paddingRight: "10px",
                    }}
                  >
                    {classItem.price}
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: { xs: "10px", md: "14px" },
                    }}
                  >
                    {classItem.billing}
                  </Typography>
                </Box>
                <Button
                  size="small"
                  sx={{ width: "100%", marginBottom: "20px" }}
                >
                  Next
                </Button>
                <hr></hr>
                <Typography
                  sx={{
                    textAlign: "left",
                    padding: "10px 0px",
                    fontSize: "14px",
                    fontWeight: "500",
                  }}
                >
                  Includes:{" "}
                </Typography>
                {classItem.features.map((feature, index) => (
                  <Typography
                    key={index}
                    sx={{
                      textAlign: "left",
                      paddingBottom: "8px",
                      fontSize: "14px",
                      fontWeight: "500",
                    }}
                  >
                    {feature}
                  </Typography>
                ))}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </>
  );
}
