import React from "react";
import { Grid, Typography, CardMedia, Box } from "@mui/material";

const SectionHeader = ({ data, index = 0 }) => {
  const direction = +index % 2 === 0 ? "row" : "row-reverse";
  return (
    <Box
      justifyContent="center"
      display="flex"
      alignItems="center"
      flexDirection={direction}
      flexWrap="wrap"
      sx={{
        marginTop: "2rem",
        flexDirection: {
          xs: "column-reverse",
          md: direction,
        },
      }}
    >
      <ImgDetail image={data.image} />
      <InfoDetail section={data} />
    </Box>
  );
};

export default SectionHeader;

const ImgDetail = ({ image }) => {
  return (
    <Box
      sx={{
        width: {
          sm: "100%",
          md: "50%",
          lg: "35%",
        },
      }}
    >
      <CardMedia
        component="img"
        image={image}
        alt="Clases Lineares"
        sx={{
          maxWidth: { xs: "480px" },
          minHeight: { xs: "352px" },
          borderRadius: { xs: "40px 0px 40px 0px" },
          objectFit: "cover",
          margin: { xs: "auto" },
        }}
      />
    </Box>
  );
};

const InfoDetail = ({ section }) => {
  return (
    <Box
      sx={{
        width: {
          sm: "100%",
          md: "50%",
          lg: "65%",
        },
      }}
    >
      <Box
        sx={{
          width: "90%",
          margin: "auto",
        }}
      >
        <Typography
          sx={{
            textAlign: "center",
            fontSize: { xs: "24px", sm: "32px" },
            margin: { xs: "40px" },
          }}
        >
          {section.title}:<strong> {section.subtitle}</strong>
        </Typography>

        <Box sx={{ padding: { xs: "0px 40px 20px 40px " } }}>
          {section.body.map((content, index) => (
            <Typography
              key={index}
              variant="body1"
              sx={{
                textAlign: "center",
                fontSize: { sm: "1rem" },
                fontWeight: { xs: "500", sm: "400" },
                paddingBottom: { xs: "10px" },
              }}
            >
              {content}
            </Typography>
          ))}
        </Box>
      </Box>
    </Box>
  );
};
