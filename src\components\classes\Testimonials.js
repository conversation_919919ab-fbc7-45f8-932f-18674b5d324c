import React from "react";
import Image from "next/image";
import { Typo<PERSON>, Button, CardMedia, Box } from "@mui/material";

import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";

const carouselItems = [
  { id: 1, content: "Slide 1: Beautiful Landscape" },
  { id: 2, content: "Slide 2: City at Night" },
  { id: 3, content: "Slide 3: Mountains and Sky" },
  { id: 4, content: "Slide 4: Sunset Beach" },
];

// TODO: DOES NOT WORK - Need to implement the coursel.
export default function Testimonials({ data }) {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        alignItems: { xs: "center" },
        justifyContent: "space-between",
      }}
    >
      <Box>
        <ChevronLeftIcon
          sx={{
            borderRadius: "50px",
            backgroundColor: "#FFE8A3",
            color: "#323232",
            fontSize: { xs: "30px", md: "50px" },
          }}
        />
      </Box>

      {/* TODO: CONTENT TO ITERATE THROUGH */}
      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column" },
          alignItems: { xs: "center" },
        }}
      >
        <Typography sx={{ fontSize: { xs: "32px" } }}>{data.title}</Typography>
        <Typography
          sx={{
            fontSize: { xs: "24px", sm: "48px" },
            fontWeight: { xs: "700", sm: "500" },
          }}
        >
          {data.subtitle}
        </Typography>
        <CardMedia
          component="img"
          image={data.clients[0].image}
          alt="Banner"
          sx={{
            width: "80px",
            height: "80px",
            margin: { xs: "40px 0px" },
            borderRadius: "50px",
          }}
        />
        <Typography
          sx={{
            fontSize: { xs: "16px" },
            fontWeight: { xs: "400" },
            maxWidth: { xs: "300px", sm: "600px" },
            marginBottom: { xs: "40px" },
          }}
        >
          {data.clients[0].review}
        </Typography>
        <Typography
          sx={{ fontSize: { xs: "20px" }, fontWeight: { xs: "700" } }}
        >
          {data.clients[0].name}
        </Typography>
        <Typography
          sx={{ fontSize: { xs: "14px" }, fontWeight: { xs: "400" } }}
        >
          {data.clients[0].title}
        </Typography>
        <Button
          sx={{
            whiteSpace: "nowrap",
            width: "350px",
            backgroundColor: "#14A79C",
            marginTop: { xs: "20px" },
          }}
        >
          {data.buttonText}
        </Button>{" "}
      </Box>
      <Box>
        <ChevronRightIcon
          sx={{
            borderRadius: "50px",
            backgroundColor: "#FFE8A3",
            color: "#323232",
            fontSize: { xs: "30px", md: "50px" },
          }}
        />
      </Box>
    </Box>
  );
}
