import { Box, Typography } from "@mui/material";
import React from "react";

const ViewMoreButton = ({ isViewMore, toggle }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="center"
      mt={6}
      mb={10}
    >
      <Typography
        onClick={() => {
          toggle();
        }}
        sx={{
          fontSize: "0.9rem",
          color: "#14A79C",
          fontWeight: "700",
          cursor: "pointer",
        }}
      >
        {!isViewMore ? " View More Details" : "View Less Details"}
      </Typography>
    </Box>
  );
};

export default ViewMoreButton;
