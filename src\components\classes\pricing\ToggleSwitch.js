import React from "react";
import { Box, Typography, Container, Grid } from "@mui/material";

const boxContainerStyles = {
  backgroundColor: "#F5F5F5",
  padding: "15px",
  borderRadius: "25px",
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-evenly",
  width: "380px",
  marginBottom: "30px",
};

const selectedLabelStyles = {
  backgroundColor: "white",
  boxShadow: "0px 8px 10px 0px #0000000D",
  padding: "10px 0px",
  width: "160px",
  borderRadius: "25px",
};

const labelStyles = {
  padding: "10px 0px",
  width: "160px",
  borderRadius: "25px",
};

const selectionLabelStyles = { textAlign: "center" };
export default function ToggleSwitch({
  firstLabel,
  secondLabel,
  onClick,
  classType,
}) {
  return (
    <Container
      onClick={onClick}
      maxWidth="xl"
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <Box sx={boxContainerStyles}>
        <Box sx={classType === 0 ? selectedLabelStyles : labelStyles}>
          <Typography sx={selectionLabelStyles}>{firstLabel}</Typography>
        </Box>
        <Box sx={classType === 0 ? labelStyles : selectedLabelStyles}>
          <Typography sx={selectionLabelStyles}>{secondLabel}</Typography>
        </Box>
      </Box>
    </Container>
  );
}
