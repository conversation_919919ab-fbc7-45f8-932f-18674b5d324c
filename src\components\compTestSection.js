import React, { useEffect, useState } from "react";
import TextField from "@mui/material/TextField";
import { Button, Typography } from "@mui/material";

export function CompTestSection({ id, defaultLabel, answer }) {
  const [response, setResponse] = useState("");
  const [showAnswer, setShowAnswer] = useState(false);
  const [outcomeMessage, setOutcomeMessage] = useState(["", ""]);
  const [buttonMessage, setButtonMessage] = useState("Check Your Answer");

  const revealAnswer = () => {
    setShowAnswer(true);
  };

  const enterResponse = (e) => {
    setResponse(e.target.value);
    setShowAnswer(false);
  };

  const checkResponse = () => {
    const { pctCorrect, correctIndexes } = compareSentencesWithExistence(
      answer,
      response
    );
    sendResponseMessage(pctCorrect, correctIndexes);
  };

  const sendResponseMessage = (pctCorrect, correctIndexes = []) => {
    if (pctCorrect === 100) {
      setOutcomeMessage([`You are 100% correct!`, ""]);
    } else if (correctIndexes.length > 0) {
      let words = response.split(" ");
      let markedUpResponse = words.map((word, index) => {
        return correctIndexes.includes(index) ? (
          <span
            style={{ color: "#3aa3e0", fontWeight: 600, whiteSpace: "pre" }}
          >
            {word + " "}
          </span>
        ) : (
          <span style={{ whiteSpace: "pre" }}>{word + " "}</span>
        );
      });

      setOutcomeMessage([
        `About ${pctCorrect}% of your words were correct.`,
        markedUpResponse,
      ]);
    } else {
      setOutcomeMessage([`Not quite. You suck at spanish.`, ""]);
    }
  };

  function removeDiacritics(str) {
    return str
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/ñ/g, "ñ")
      .replace(/Ñ/g, "Ñ");
  }

  const compareSentencesWithExistence = (sentenceA, sentenceB) => {
    const punctuationRegEx = /[.,\/#!$%\^&\*;:{}=\-_`~()]/g;
    const extraSpacesRegEx = /\s{2,}/g;

    sentenceA = removeDiacritics(sentenceA);
    sentenceB = removeDiacritics(sentenceB);

    let wordsA = sentenceA
      .toLowerCase()
      .replace(punctuationRegEx, "")
      .replace(extraSpacesRegEx, " ")
      .split(" ");
    let wordsB = sentenceB
      .toLowerCase()
      .replace(punctuationRegEx, "")
      .replace(extraSpacesRegEx, " ")
      .split(" ");
    let correctIndexes = [];
    let matchedCount = 0;

    for (let i = 0; i < wordsB.length; i++) {
      if (wordsA.includes(wordsB[i])) {
        matchedCount++;
        correctIndexes.push(i);
      }
    }

    let pctCorrect = (matchedCount / wordsA.length) * 100;
    return { pctCorrect, correctIndexes };
  };

  return (
    <div
      id="comprehension-test-section"
      style={{
        display: "flex",
        flexDirection: "column",
        margin: "15px",
      }}
    >
      {outcomeMessage && (
        <div style={{ display: "flex", margin: "auto" }}>
          <Typography sx={{ fontWeight: 600 }}>{outcomeMessage[0]}</Typography>
        </div>
      )}
      {outcomeMessage[1] && (
        <div>
          <Typography
            sx={{
              textAlign: "center",
              flexShrink: 0,
              userSelect: "none",
              color: "gray",
            }}
          >
            Correct words are in blue:
          </Typography>
          <Typography
            sx={{
              textAlign: "center",
              marginLeft: "8px",
              marginTop: "10px",
              flexShrink: 0,
              userSelect: "none",
            }}
          >
            {outcomeMessage[1]}
          </Typography>
        </div>
      )}
      {showAnswer && (
        <Typography
          sx={{
            textAlign: "center",
            marginLeft: "8px",
            marginTop: "10px",
            flexShrink: 0,
            userSelect: "none",
          }}
        >
          {answer}
        </Typography>
      )}
      <TextField
        id={id}
        label={defaultLabel}
        multiline
        maxRows={4}
        onChange={enterResponse}
        style={{
          display: "flex",
          margin: "30px auto",
          width: "100%",
        }}
      />
      <div>
        <Button onClick={checkResponse}>{buttonMessage}</Button>
        <Button onClick={revealAnswer} sx={{ marginLeft: "10px" }}>
          showAnswer
        </Button>
      </div>
    </div>
  );
}
