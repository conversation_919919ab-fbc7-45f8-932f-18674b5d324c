import { Box, Typography } from "@mui/material";

import TagComponent from "./TagComponent";
import LikeComponent from "./LikeComponent";

const level = {
  color: "#B3B3B3",
  fontSize: "14px",
  fontWeight: "500",
  marginRight: "10px",
  display: "flex",
  alignItems: "center",
  justifyContent: "left",
};

const cardContainerStyles = {
  display: "flex",
  borderRadius: "15px",
  width: "298px",
  height: "366px",
  display: "flex",
  flexDirection: "column",
};

const likeComponentStyles = {
  top: 0,
  right: 0,
  position: "absolute",
};

const tagContainerStyles = {
  display: "flex",
  overflowX: "auto",
  whiteSpace: "nowrap",
  width: "265px",
  "::-webkit-scrollbar": {
    display: "none",
  },
  scrollbarWidth: "none",
  marginBottom: "20px",
};

const tagStyles = {
  marginRight: "8px" /* Adjust spacing as needed */,
  display: "inline-block",
  backgroundColor: "#D7F7F5", // light teal color
  color: "#14A79C", // teal color for text
  fontSize: "13px",
  fontWeight: "500",
  marginRight: "5px",
};

const descriptionStyles = {
  fontWeight: "400",
  fontSize: "14px",
  color: "white",
  marginBottom: "10px",
  display: "-webkit-box",
  overflow: "hidden",
  WebkitBoxOrient: "vertical",
  WebkitLineClamp: 3, // Limits text to 2 lines and truncates
};
export default function CollectionCard({ data }) {
  return (
    <Box sx={cardContainerStyles}>
      <Box sx={{ borderRadius: "15px" }}>
        {/* Top section with image and favorite button */}
        <Box
          position="relative"
          sx={{
            position: "relative",
            width: "100%",
            borderRadius: "15px",
            padding: "15px",
            height: "366px",
            backgroundImage: `url(${data.img})`,
            backgroundSize: "cover", // Ensures the image covers the entire Box
            backgroundPosition: "center", // Centers the image
            backgroundRepeat: "no-repeat", // Prevents tiling
          }}
        >
          <Box
            sx={{
              borderRadius: "15px",
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background:
                "linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 1))",
              zIndex: 1,
            }}
          />

          {/* Favorite Icon */}
          <Box
            sx={{
              position: "relative",
              zIndex: 2,
              color: "white",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "100%",
            }}
          >
            <LikeComponent styles={likeComponentStyles}></LikeComponent>

            {/* Card Content */}
            <Box sx={{ paddingTop: "130px", width: "268px" }}>
              <Box sx={{ paddingBottom: "20px" }}>
                <Typography
                  noWrap
                  sx={{ fontSize: "20px", color: "white" }}
                  fontWeight="600"
                >
                  {data.title}
                </Typography>
                <Typography sx={descriptionStyles} marginTop={1}>
                  {data.description}
                </Typography>
              </Box>

              {/* Skill Level */}
              <Box sx={tagContainerStyles}>
                {data.tags.map((tag, index) => (
                  <TagComponent
                    key={index}
                    label={tag}
                    tagStyles={tagStyles}
                  ></TagComponent>
                ))}
              </Box>
              <Typography sx={level}>BEGINNER</Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
