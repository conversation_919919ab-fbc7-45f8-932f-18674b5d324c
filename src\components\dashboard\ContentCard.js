import { Box, Typography, Avatar } from "@mui/material";
import TagComponent from "./TagComponent";
import Image from "next/image";
import LikeComponent from "./LikeComponent";

// this is temporary - need to pull image url from db
import avatar from "@/../../public/images/dashboard/avatar.webp";

const titleStyles = {
  fontSize: "20px",
  fontWeight: "500px",
  color: "black",
};
const imageStyles = {
  borderRadius: "15px",
};

const tagStyles = {
  backgroundColor: "#D7F7F5", // light teal color
  color: "#14A79C", // teal color for text
  fontSize: "13px",
  fontWeight: "500",
  marginRight: "5px",
};

const cardContainerStyles = {
  display: "flex",
  border: "1px solid #E6E6E6",
  borderRadius: "15px",
  padding: "15px",
  width: "298px",
  height: "366px",
  display: "flex",
  flexDirection: "column",
};

const AvatarOverlayStyles = {
  position: "absolute",
  bottom: 15,
  left: 8,
  display: "flex",
  alignItems: "center",
  bgcolor: "white",
  padding: "4px 8px",
  borderRadius: "16px",
  zIndex: "2",
};
const subtitleStyles = {
  fontSize: "14px",
  fontWeight: "400",
  color: "#6D6D6D",
};
const descriptionStyles = {
  fontSize: "16px",
  fontWeight: "400",
  color: "#6D6D6D",
  marginBottom: "10px",
  display: "-webkit-box",
  overflow: "hidden",
  WebkitBoxOrient: "vertical",
  WebkitLineClamp: 2, // Limits text to 2 lines and truncates
};

const GradientStyles = {
  borderRadius: "15px",
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: "linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.7))",
  zIndex: 1,
};

const videoStatusContainerStyles = {
  position: "absolute",
  top: 8,
  left: 20,
  right: 0,
  bottom: 0,
  zIndex: 1,
};

const videoStatusTitleStyles = {
  color: "white",
  fontWeight: "500",
  fontSize: "12px",
};

const likedComponentStyles = { top: 10, right: 10, position: "absolute" };
const tagContainerStyles = {
  display: "flex",
  overflow: "auto",
  marginBottom: "20px",
  overflowX: "scroll",
  scrollSnapType: "x mandatory",

  "::-webkit-scrollbar": {
    display: "none",
  },
  scrollbarWidth: "none",
};

const levelTitleStyles = {
  color: "#B3B3B3",
  fontSize: "14px",
  fontWeight: "500",
  marginRight: "10px",
  display: "flex",
  alignItems: "center",
  justifyContent: "left",
};

export default function ContentCard({ data }) {
  if (!data) {
    return null;
  }
  return (
    <Box sx={cardContainerStyles}>
      {/* Image container */}
      <Box position="relative">
        <Image
          src={data.img}
          width={269} // Ensures image takes up full width in responsive mode
          height={143}
          style={{
            ...imageStyles,
            width: "269px", // Ensures image takes up full width in responsive mode
            height: "143px", // Ensures responsive height
          }}
          alt="alt title"
        />
        {/* background gradient - only appears if watched or pending */}
        {(data.status === "WATCHED" || data.status === "IN PROGRESS") && (
          <>
            <Box sx={GradientStyles} />
            <Box sx={videoStatusContainerStyles}>
              <Typography sx={videoStatusTitleStyles}>
                &#x2022; {data.status}
              </Typography>
            </Box>
          </>
        )}

        {/* Avatar overlay */}
        <Box sx={AvatarOverlayStyles}>
          <Avatar
            alt="Alice"
            src={avatar.src}
            sx={{ width: 24, height: 24, marginRight: 1 }}
          />
          <Typography variant="body2" color="text.primary">
            Alice
          </Typography>
        </Box>

        {/* Favorite Icon */}
        <LikeComponent styles={likedComponentStyles}></LikeComponent>
      </Box>

      {/* Card Content Container */}
      <Box sx={{ width: "268px" }}>
        {/* Card Title Container */}
        <Box sx={{ paddingBottom: "15px" }}>
          <Typography noWrap sx={titleStyles}>
            {data.title}
          </Typography>
          <Typography sx={subtitleStyles}>{data.collection}</Typography>
        </Box>
        {/* Card Body container */}
        <Box>
          <Typography sx={descriptionStyles}>{data.description}</Typography>
          <Box sx={tagContainerStyles}>
            {data.tags.map((tag, index) => (
              <TagComponent
                key={index}
                label={tag}
                tagStyles={tagStyles}
              ></TagComponent>
            ))}
          </Box>
          <Typography sx={levelTitleStyles}>{data.level}</Typography>
        </Box>
      </Box>
    </Box>
  );
}
