import React from "react";
import { Box } from "@mui/material";
// import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
// import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import ContentCard from "./ContentCard";

export default function ContentCards({ data }) {
  return (
    <Box display="flex" alignItems="center">
      <Box
        sx={{
          display: "flex",
          gap: 2,
          overflowX: "scroll",
          scrollSnapType: "x mandatory",
          paddingBottom: 1,
          "::-webkit-scrollbar": {
            display: "none",
          },
          scrollbarWidth: "none",
        }}
      >
        {/* This is where i map through the list of video data that is being passed down */}
        {data.map((video, index) => {
          return (
            <Box sx={{ scrollSnapAlign: "start" }} key={index}>
              <Box
                sx={{
                  position: "relative",
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "center",
                  marginRight: "30px",
                  mb: 1,
                }}
              >
                <ContentCard data={video}></ContentCard>
              </Box>
            </Box>
          );
        })}
      </Box>
    </Box>
  );
}
