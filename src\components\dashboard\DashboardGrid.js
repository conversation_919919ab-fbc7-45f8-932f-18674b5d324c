import React from "react";
import { Box, Grid } from "@mui/material";
import SearchBarComponent from "./SearchBarComponent";
import DropDownComponent from "./DropDownComponent";
import TogglePillsComponent from "./TogglePillsComponent";
import GridCard from "./GridCard";

const filterContainerStyles = {
  marginTop: "28px",
  display: "flex",
  flexDirection: { xs: "column", sm: "row" },
  alignItems: { xs: "left", sm: "center" },
};

const searchBarStyles = {
  marginRight: "56px",
};

const statusContainerStyles = {
  marginTop: "28px",
  marginBottom: "28px",
};

const dropDownStyles = {
  m: 1,
  width: "170px",
  marginTop: { xs: "30px", sm: "0px" },
};

export default function DashboardGrid({ data }) {
  return (
    <Box>
      <Box sx={filterContainerStyles}>
        <SearchBarComponent styles={searchBarStyles}></SearchBarComponent>
        <DropDownComponent customStyles={dropDownStyles}></DropDownComponent>
      </Box>
      <Box sx={statusContainerStyles}>
        <TogglePillsComponent></TogglePillsComponent>
      </Box>
      <Grid container spacing={11}>
        {data.map((item, index) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
            <GridCard item={item}></GridCard>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}
