import * as React from "react";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";

export default function DropDownComponent({ customStyles }) {
  const [filter, setFilter] = React.useState("");

  const handleChange = (event) => {
    setFilter(event.target.value);
  };

  return (
    <FormControl sx={customStyles} size="small">
      <InputLabel id="demo-select-small-label">Filter</InputLabel>
      <Select
        labelId="demo-select-small-label"
        id="demo-select-small"
        value={filter}
        label="Age"
        onChange={handleChange}
      >
        <MenuItem value="">
          <em>All</em>
        </MenuItem>
        <MenuItem value="Option 1">Option 2</MenuItem>
        <MenuItem value="Option 2">Option 3</MenuItem>
        <MenuItem value="Option 3">Option 4</MenuItem>
      </Select>
    </FormControl>
  );
}
