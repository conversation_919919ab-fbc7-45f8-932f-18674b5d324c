import React from "react";
import { Box, Typography, Avatar, Hidden } from "@mui/material";

import Image from "next/image";
import ProgressBar from "./ProgressBar";
import TagComponent from "./TagComponent";
import LikeComponent from "./LikeComponent";

const subtitleStyles = {
  fontSize: { xs: "10px", sm: "14px" },
  fontWeight: { xs: "500", sm: "400" },
  color: "#6D6D6D",
};

const progressTitle = {
  fontSize: "12px",
  fontWeight: "500",
  // marginBottom: "32px",
};

const tagContainerStyles = {
  display: "flex",
  overflow: "auto",
  marginTop: { xs: "5px", sm: "32px" },
  marginBottom: { xs: "6px", sm: "12px" },
  overflowX: "scroll",
  scrollSnapType: "x mandatory",

  "::-webkit-scrollbar": {
    display: "none",
  },
  scrollbarWidth: "none",
};

const titleStyles = {
  fontSize: { xs: "16px", sm: "20px" },
  fontWeight: "500",
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  width: { xs: "80%", sm: "100%" }, // Takes the width of the parent container
};

const levelStyles = {
  fontSize: { xs: "9px", sm: "16px" },
  fontWeight: "400",
  color: { xs: "#6D6D6D", sm: "#B3B3B3" },
};

const tagStyles = {
  backgroundColor: "#D7F7F5", // light teal color
  color: "#14A79C", // teal color for text
  fontSize: { xs: "8px", sm: "13px" },
  fontWeight: { xs: "400", sm: "500" },
  marginRight: "5px",
};

const GradientStyles = {
  borderRadius: "15px",
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: "linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.7))",
  zIndex: 1,
};

const videoStatusContainerStyles = {
  position: "absolute",
  top: { xs: 10, sm: 8 },
  left: { xs: 10, sm: 20 },
  right: 0,
  bottom: 0,
  zIndex: 1,
};

const videoStatusTitleStyles = {
  color: "white",
  fontWeight: "500",
  fontSize: { xs: "8px", sm: "12px" },
};

const AvatarOverlayStyles = {
  position: "absolute",
  bottom: 15,
  left: 8,
  display: "flex",
  alignItems: "center",
  bgcolor: "white",
  padding: "4px 8px",
  borderRadius: "16px",
  zIndex: "2",
};

const likedComponentStyles = {
  top: 10,
  right: 10,
  position: "absolute",
  zIndex: "10",
};

const cardContainerStyles = {
  padding: { xs: "10px", sm: "16px 15px 24px 15px" },
  height: { xs: "155px", sm: "auto" },
  borderRadius: "22px",
  border: "1px solid #E6E6E6",
  display: "flex",
  flexDirection: { xs: "row", sm: "column" },
  overflow: { xs: "hidden", sm: "none" },
};

const cardImageContainerStyles = {
  position: "relative",
  width: {
    xs: "80px",
    sm: "auto",
  },
  minWidth: { xs: "80px" },
};

const cardBodyContainer = {
  position: "relative",
  padding: { xs: "0px 10px", sm: "none" },
  width: { xs: "75%", sm: "100%" },
};

const progressBarStyles = {
  marginTop: "5px",
  marginBottom: { xs: "0px", sm: "5px" },
};

const mobileAvatarContainerStyles = { display: "flex", alignContent: "center" };
const mobileAvatarStyles = { width: 15, height: 15, marginRight: 1 };
const mobileAvatarTextStyles = {
  fontSize: "10px",
  fontWeight: "500",
  color: "#6D6D6D",
};

export default function GridCard({ item }) {
  return (
    <Box sx={cardContainerStyles}>
      {/* Card Image Container */}
      <Box sx={cardImageContainerStyles}>
        <Image
          src={item.img}
          width={269}
          alt={item.title}
          height={135}
          style={{
            borderRadius: "15px",
            objectFit: "cover",
            width: "100%",
          }}
        ></Image>

        {/* background gradient - only appears if watched or pending */}
        {(item.status === "WATCHED" || item.status === "IN PROGRESS") && (
          <>
            <Box sx={GradientStyles} />
            <Box sx={videoStatusContainerStyles}>
              <Typography sx={videoStatusTitleStyles}>
                &#x2022; {item.status}
              </Typography>
            </Box>
          </>
        )}
        <Hidden smDown>
          {/* Avatar overlay */}
          <Box sx={AvatarOverlayStyles}>
            <Avatar
              alt="Alice"
              src={item.avatar}
              sx={{ width: 24, height: 24, marginRight: 1 }}
            />
            <Typography variant="body2" color="text.primary">
              {item.author}
            </Typography>
          </Box>

          {/* Favorite Icon */}
          <LikeComponent styles={likedComponentStyles}></LikeComponent>
        </Hidden>
      </Box>
      {/* Card Body Container */}
      <Box sx={cardBodyContainer}>
        {/* title */}
        <Hidden smUp>
          <Typography sx={levelStyles}>{item.level}</Typography>
        </Hidden>
        <Typography sx={titleStyles}>{item.title}</Typography>
        <Typography sx={subtitleStyles}>{item.collection}</Typography>
        {/* progress bar */}
        <ProgressBar
          value={item.progress}
          customStyles={progressBarStyles}
        ></ProgressBar>
        {/* progress subtitle */}
        <Typography sx={progressTitle}>{item.progress}% Complete</Typography>
        {/* Tag container */}
        <Box sx={tagContainerStyles}>
          {item.tags.map((tag, index) => (
            <TagComponent
              key={index}
              label={tag}
              tagStyles={tagStyles}
            ></TagComponent>
          ))}
        </Box>
        {/* Level */}
        <Hidden smDown>
          <Typography sx={levelStyles}>{item.level}</Typography>
        </Hidden>

        <Hidden smUp>
          <Box sx={mobileAvatarContainerStyles}>
            <Avatar alt="Alice" src={item.avatar} sx={mobileAvatarStyles} />
            <Typography sx={mobileAvatarTextStyles}>{item.author}</Typography>
          </Box>
        </Hidden>
        <Hidden smUp>
          <LikeComponent styles={likedComponentStyles}></LikeComponent>
        </Hidden>
      </Box>
    </Box>
  );
}
