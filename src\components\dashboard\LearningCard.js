import { Box, Hidden, Typography, Avatar } from "@mui/material";
import TagComponent from "./TagComponent";
import LikeComponent from "./LikeComponent";
import ProgressBar from "./ProgressBar";

// this is temporary - need to pull image url from db
import img from "@/../../public/images/dashboard/mylearning.webp";
import avatar from "@/../../public/images/dashboard/avatar.webp";

const videoComponentStyles = {
  display: "flex",
  width: { xs: "358px", sm: "574px" },
  height: { xs: "155px", sm: "205px" },
  border: "1px solid #E6E6E6",
  borderRadius: { xs: "18px", sm: "15px" },
  padding: { xs: "10px", sm: "15px" },
};

const imageContainerStyles = {
  display: "flex",
  alignItems: "center",
  borderRadius:
    "15px 0 0 15px" /* top-left, top-right, bottom-right, bottom-left */,
  // width: { xs: "79px", sm: "145px" },
  // height: { xs: "135px", sm: "180px" },
};

const levelStyles = {
  color: "#B3B3B3",
  fontSize: { xs: "9px", sm: "14px" },
  fontWeight: { xs: "400", sm: "500" },
  marginRight: "10px",
  display: "flex",
  alignItems: "center",
  justifyContent: "left",
};

const titleStyles = {
  fontSize: { xs: "16px", sm: "20px" },
  fontWeight: "500px",
};

const subTitleStyles = {
  color: "#6D6D6D",
  fontSize: { xs: "10px", sm: "14px" },
  fontWeight: "400",
};

const descriptionStyles = {
  fontSize: "14px",
  fontWeight: "500",
  color: "#6D6D6D",
  marginTop: "14px",
};
const tagStyles = {
  backgroundColor: "#D7F7F5", // light teal color
  color: "#14A79C", // teal color for text
  fontSize: { xs: "8px", sm: "13px" },
  fontWeight: { xs: "400", sm: "500" },
  marginRight: "5px",
};

const contentContainerStyles = {
  width: "100%",
  marginLeft: "15px",
  borderRadius: " 0 15px 15px 0",
};

const percentageTextStyles = {
  fontSize: { xs: "10px", sm: "14px" },
  fontWeight: { xs: "500", sm: "400" },
};

const imageStyles = {
  width: { xs: "79px", sm: "145px" },
  height: { xs: "135px", sm: "180px" },
  objectFit: "cover",
  borderRadius: "15px",
};

const AvatarOverlayStyles = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
};

const likedComponentStyles = { top: 10, right: 10, position: "absolute" };

export default function LearningCard({ data }) {
  return (
    <Box sx={videoComponentStyles}>
      <Box sx={imageContainerStyles}>
        <Box
          component="img"
          src={img.src}
          alt="Descriptive text"
          sx={imageStyles}
        />
      </Box>
      <Box sx={contentContainerStyles}>
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            flexWrap: "wrap",
            width: "100%",
          }}
        >
          <Typography sx={levelStyles}>{data.level}</Typography>
        </Box>

        <Box sx={{ width: { xs: "200px", sm: "375px" } }}>
          <Typography sx={titleStyles} noWrap>
            {data.title}
          </Typography>
          <Typography sx={subTitleStyles} noWrap>
            {data.subtitle}
          </Typography>
          <Hidden smDown>
            <Typography noWrap sx={descriptionStyles}>
              {data.description}
            </Typography>
          </Hidden>

          <ProgressBar value={data.progress} />
          <Typography sx={percentageTextStyles}>
            {data.progress}% Complete
          </Typography>
          {/* iterate through tags */}
          <Box
            sx={{
              marginBottom: { xs: "5px", sm: "0px" },
              marginTop: { xs: "5px", sm: "0px" },
            }}
          >
            <TagComponent label="TECH" tagStyles={tagStyles}></TagComponent>
            <TagComponent
              label="POLITENESS"
              tagStyles={tagStyles}
            ></TagComponent>
          </Box>
          {/* Video Creator */}
          <Hidden smUp>
            <Box sx={AvatarOverlayStyles}>
              <Avatar
                alt="Alice"
                src={avatar.src}
                sx={{ width: 12, height: 12, marginRight: 1 }}
              />
              <Typography
                sx={{ fontSize: "10px", fontWeight: "500", color: "#6D6D6D" }}
              >
                Alice
              </Typography>
            </Box>
          </Hidden>
        </Box>
      </Box>
      <Box>
        <LikeComponent styles={likedComponentStyles} />
      </Box>
    </Box>
  );
}
