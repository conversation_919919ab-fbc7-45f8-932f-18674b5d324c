import { IconButton } from "@mui/material";
import FavoriteIcon from "@mui/icons-material/Favorite";
import FavoriteBorderIcon from "@mui/icons-material/FavoriteBorder";
import { useState } from "react";

export default function LikeComponent({ styles }) {
  const [liked, setLiked] = useState(false);

  const handleToggle = () => {
    setLiked(!liked);
  };

  const computedStyles = {
    ...styles,
    border: "1px solid #E6E6E6",
    bgcolor: "white",
    padding: "4px",
    zIndex: 2,
  };

  return (
    <IconButton
      onClick={handleToggle}
      color="primary"
      aria-label="add to favorites"
      sx={computedStyles}
    >
      {liked ? (
        <FavoriteIcon style={{ color: "red" }} />
      ) : (
        <FavoriteBorderIcon style={{ color: "gray" }} />
      )}
    </IconButton>
  );
}
