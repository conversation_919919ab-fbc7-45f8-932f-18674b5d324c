import { Box, Avatar, Typography, IconButton } from "@mui/material";
import TagComponent from "./TagComponent";
import FavoriteBorderIcon from "@mui/icons-material/FavoriteBorder";
import Image from "next/image";
import avatar from "@/../../public/images/dashboard/avatar.webp";

const tagStyles = {
  backgroundColor: "#D7F7F5", // light teal color
  color: "#14A79C", // teal color for text
  fontSize: "13px",
  fontWeight: "500",
  marginRight: "5px",
};
// STYLINGS
const imageStyles = {
  borderRadius: "15px",
};

const titleStyles = {
  fontSize: "20px",
  fontWeight: "500px",
};

const level = {
  color: "#B3B3B3",
  fontSize: "14px",
  fontWeight: "500",
  marginRight: "10px",
  display: "flex",
  alignItems: "center",
  justifyContent: "left",
};

// TODO: - ADD OVERLAY FOR VIDEOS THAT HAVE BEEN WATCHED
export default function LikedVideoCard({ img }) {
  return (
    <Box>
      {/* Top section with image and favorite button */}
      <Box position="relative">
        <Image
          src={img}
          style={{
            ...imageStyles,
            width: "100%", // Ensures image takes up full width in responsive mode
            height: "auto", // Ensures responsive height
          }}
          alt="Video Background Image"
        />

        {/* background gradient */}
        <Box
          sx={{
            borderRadius: "15px",
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background:
              "linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 1))",
            zIndex: 1,
          }}
        />
        {/* Avatar overlay */}
        <Box
          sx={{
            position: "absolute",
            bottom: 15,
            left: 8,
            display: "flex",
            alignItems: "center",
            bgcolor: "white",
            padding: "4px 8px",
            borderRadius: "16px",
            zIndex: 2,
          }}
        >
          <Avatar
            alt="Alice"
            src={avatar.src}
            sx={{ width: 24, height: 24, marginRight: 1 }}
          />
          <Typography variant="body2" color="text.primary">
            Alice
          </Typography>
        </Box>
        {/* Favorite Icon */}
        <IconButton
          aria-label="add to favorites"
          sx={{
            position: "absolute",
            top: 8,
            right: 8,
            bgcolor: "white",
            padding: "4px",
            zIndex: 2,
          }}
        >
          <FavoriteBorderIcon />
        </IconButton>
        <Typography
          sx={{
            position: "absolute",
            top: 8,
            left: 8,
            color: "white",
            padding: "4px",
            zIndex: 2,
            fontSize: "12px",
            fontWeight: "500",
          }}
        >
          WATCHED
        </Typography>
      </Box>

      {/* Card Content */}
      <Box>
        <Box sx={{ paddingBottom: "15px", paddingTop: "10px" }}>
          <Typography sx={titleStyles}>Title</Typography>
        </Box>
        <Box>
          <Typography
            sx={{
              fontSize: "16px",
              fontWeight: "400",
              color: "#6D6D6D",
              marginBottom: "10px",
            }}
          >
            Description of the video will be show here
          </Typography>
          <Box sx={{ marginBottom: "20px" }}>
            <TagComponent label="TECH" tagStyles={tagStyles}></TagComponent>
            <TagComponent label="ART" tagStyles={tagStyles}></TagComponent>
            <TagComponent label="FITNESS" tagStyles={tagStyles}></TagComponent>
            <TagComponent label="A.." tagStyles={tagStyles}></TagComponent>
          </Box>
          <Typography sx={level}>BEGINNER</Typography>
        </Box>
      </Box>
    </Box>
  );
}
