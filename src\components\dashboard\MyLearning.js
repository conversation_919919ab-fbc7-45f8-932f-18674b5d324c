import { Box, Hidden } from "@mui/material";
import Link from "next/link";
import <PERSON><PERSON>ontainer from "./SectionContainer";
import LearningCard from "./LearningCard";

const myLearningComponentListStyles = {
  display: "flex",
  flexDirection: { xs: "column", md: "row" },
  justifyContent: "flex-start",
  paddingBottom: "10px",
};
const linkStyles = {
  color: "#72CAC4",
  fontSize: "14px",
  fontWeight: "500",
};

// MY LEARNING SECTION
export default function MyLearning({ data }) {
  return (
    <SectionContainer title="My Learning">
      <Box display="flex" alignItems="center">
        <Box
          sx={{
            display: "flex",
            gap: 2,
            overflowX: "scroll",
            scrollSnapType: "x mandatory",
            paddingBottom: 1,
            "::-webkit-scrollbar": {
              display: "none",
            },
            scrollbarWidth: "none",
          }}
        >
          {data.map((video, index) => (
            <Box sx={{ scrollSnapAlign: "start" }} key={index}>
              <Box
                sx={{
                  position: "relative",
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "center",
                  marginRight: "30px",
                  mb: 1,
                }}
              >
                <LearningCard data={video}></LearningCard>
              </Box>
            </Box>
          ))}
        </Box>
      </Box>

      <Hidden smDown>
        <Link href="#" style={linkStyles}>
          Visit My Learning Page
        </Link>
      </Hidden>
    </SectionContainer>
  );
}
