import { Box, LinearProgress } from "@mui/material";

export default function ProgressBar({ value, customStyles }) {
  const styles = {
    margin: "5px 0px",
    width: "100%",
    height: 8,
    borderRadius: 5,
    backgroundColor: "rgba(0, 0, 0, 0.1)",
    "& .MuiLinearProgress-bar": {
      borderRadius: 5,
      backgroundColor: "#F5A623", // Replace with your preferred color
    },
    height: 8,
    borderRadius: 5,
    backgroundColor: "rgba(0, 0, 0, 0.1)",
    "& .MuiLinearProgress-bar": {
      borderRadius: 5,
      backgroundColor: "#F5A623", // Replace with your preferred color
    },
    ...customStyles,
  };
  return (
    <Box>
      <LinearProgress variant="determinate" value={value} sx={styles} />
    </Box>
  );
}
