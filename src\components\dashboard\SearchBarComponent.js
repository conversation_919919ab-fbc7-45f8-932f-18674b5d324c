import React from "react";
import TextField from "@mui/material/TextField";
import InputAdornment from "@mui/material/InputAdornment";
import SearchIcon from "@mui/icons-material/Search";

function SearchBarComponent({ styles }) {
  return (
    <TextField
      variant="outlined"
      placeholder="Find the perfect course or instructor"
      InputProps={{
        endAdornment: (
          <InputAdornment position="end">
            <SearchIcon
              sx={{
                color: "#14A79C",
              }}
            />
          </InputAdornment>
        ),
        sx: {
          borderRadius: "10px",
        },
      }}
      sx={{
        ...styles,
        // margin: "1000px",
        width: "100%", // Adjust width as needed
        maxWidth: "680px", // Limit the maximum width
        "& .MuiOutlinedInput-root": {
          borderRadius: "30px", // Rounded corners
        },
      }}
    />
  );
}

export default SearchBarComponent;
