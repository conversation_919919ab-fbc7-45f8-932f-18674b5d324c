import * as React from "react";
import PropTypes from "prop-types";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";

import { Box } from "@mui/material";
import IndividualCollectionsSection from "./IndividualCollectionsSection";
import MyLearning from "./MyLearning";
import VideoCategoriesSection from "./VideoCategoriesSection";
import LikedSection from "./LikedSection";
import FeaturedSection from "./FeaturedSection";
import SearchBarComponent from "./SearchBarComponent";

import DashboardGrid from "./DashboardGrid";

import img from "@/../../public/images/dashboard/mylearning.webp";
import avatar from "@/../../public/images/dashboard/avatar.webp";
import { Dashboard } from "@mui/icons-material";
import DropDownComponent from "./DropDownComponent";

// TODO: document all image dimensions
// MOCK DATA IM TRYING TO PASS DOWN
const myLearningData = [
  {
    id: "1",
    title: "Modern Tech and Travel Vocabulary For Nomads",
    subtitle: "Language for the Modern Explorer",
    description:
      "Description for the Video will be shown here. This can also go into pretty great depths.",
    tags: ["TECH", "POLITENESS"],
    level: "BEGINNER",
    img: "https://placehold.co/130x13",
    progress: 75,
    favorite: "true",
  },
  {
    id: "2",
    title: "Modern Tech and Travel Vocabulary",
    subtitle: "Language for the Modern Explorer",
    description:
      "Description for the Video will be shown here. This can also go into pretty great depths.",
    tags: ["TECH", "POLITENESS"],
    level: "BEGINNER",
    img: "https://placehold.co/130x13",
    progress: 25,
    favorite: "true",
  },
];
const featuredVideoData = [
  {
    id: 2,
    title: "Technical Terms in Spanish",
    collection: "Spanish Grammar",
    description:
      "Video going into the ins and outs of the proper spanish language",
    tags: ["TECH", "POLITENESS", "ART", "TRAVELING"],
    level: "BEGINNER",
    img: `${img.src}`,
    avatar: `${avatar.src}`,
    author: "Jane Doe",
    favorite: "true",
    status: "",
    progress: 79,
  },
  {
    id: 1,
    title: "How To Order Food at A Restuarants",
    collection: "Restaurant Etiquette",
    description:
      "Basics of dinning etiquette where you will learn about the service industry culture",
    tags: ["TECH", "POLITENESS", "ART", "TRAVELING"],
    level: "ADVANCED",
    img: `${img.src}`,
    avatar: `${avatar.src}`,
    author: "Jane Doe",
    favorite: "true",
    status: "WATCHED",
    progress: 50,
  },
  {
    id: 3,
    title: "How To Order Food at A Restuarants",
    collection: "Restaurant Etiquette",
    description:
      "Description for the Collection will be shown here. We can show around 2-3 sentences here .Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nlladvfsdf",
    tags: ["TECH", "POLITENESS", "ART", "TRAVELING"],
    level: "BEGINNER",
    img: `${img.src}`,
    avatar: `${avatar.src}`,
    author: "Jane Doe",
    favorite: "true",
    status: "IN PROGRESS",
    progress: 28,
  },
  {
    id: 2,
    title: "Technical Terms in Spanish",
    collection: "Spanish Grammar",
    description:
      "Video going into the ins and outs of the proper spanish language",
    tags: ["TECH", "POLITENESS", "ART", "TRAVELING"],
    level: "INTERMEDIATE",
    img: `${img.src}`,
    avatar: `${avatar.src}`,
    author: "Jane Doe",
    favorite: "true",
    status: "",
    progress: 65,
  },
  {
    id: 1,
    title: "How To Order Food at A Restuarants",
    collection: "Restaurant Etiquette",
    description:
      "Basics of dinning etiquette where you will learn about the service industry culture",
    tags: ["TECH", "POLITENESS", "ART", "TRAVELING"],
    level: "BEGINNER",
    img: `${img.src}`,
    avatar: `${avatar.src}`,
    author: "Jane Doe",
    favorite: "true",
    status: "WATCHED",
    progress: 12,
  },
];

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

const searchBarStyles = {
  marginTop: "28px",
};

export default function BasicTabs() {
  const [value, setValue] = React.useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: "100%" }}>
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="basic tabs example"
        >
          <Tab label="Dashboard" {...a11yProps(0)} />
          <Tab label="My Learning" {...a11yProps(1)} />
          <Tab label="Liked" {...a11yProps(2)} />
        </Tabs>
      </Box>
      <CustomTabPanel value={value} index={0}>
        <SearchBarComponent styles={searchBarStyles}></SearchBarComponent>
        <MyLearning data={myLearningData}></MyLearning>
        <VideoCategoriesSection></VideoCategoriesSection>
        <FeaturedSection data={featuredVideoData}></FeaturedSection>
        <IndividualCollectionsSection
          data={featuredVideoData}
        ></IndividualCollectionsSection>
        <LikedSection data={featuredVideoData}></LikedSection>
      </CustomTabPanel>
      <CustomTabPanel value={value} index={1}>
        <DashboardGrid data={featuredVideoData}></DashboardGrid>
      </CustomTabPanel>
      <CustomTabPanel value={value} index={2}>
        <DashboardGrid data={featuredVideoData}></DashboardGrid>
      </CustomTabPanel>
    </Box>
  );
}
