import React, { useState } from "react";
import { Button, Stack } from "@mui/material";

function TogglePillsComponent() {
  const [isClickedBtn1, setIsClickedBtn1] = useState(false);
  const [isClickedBtn2, setIsClickedBtn2] = useState(false);

  const handleClickBtn1 = () => {
    setIsClickedBtn1((prev) => !prev);
  };

  const handleClickBtn2 = () => {
    setIsClickedBtn2((prev) => !prev);
  };

  const button1Styles = {
    width: "114px",
    fontSize: "14px",
    fontWeight: "400",
    backgroundColor: isClickedBtn1 ? "#14A79C" : "#FFFFFF",
    color: isClickedBtn1 ? "white" : "#000000",
    borderRadius: "20px",
    outline: isClickedBtn1 ? "None" : "1px solid #B3B3B3",
  };

  const button2Styles = {
    width: "114px",
    fontSize: "14px",
    fontWeight: "400",
    backgroundColor: isClickedBtn2 ? "#14A79C" : "#FFFFFF",
    color: isClickedBtn2 ? "white" : "#000000",
    borderRadius: "20px",
    outline: isClickedBtn2 ? "None" : "1px solid #B3B3B3",
  };

  return (
    <Stack direction="row" spacing={6}>
      <Button onClick={handleClickBtn1} size="small" sx={button1Styles}>
        In Progress
      </Button>
      <Button onClick={handleClickBtn2} size="small" sx={button2Styles}>
        Watched
      </Button>
    </Stack>
  );
}

export default TogglePillsComponent;
