import { Box } from "@mui/material";
import { categories } from "@/../../data/categories";
import SectionContainer from "./SectionContainer";
import VideoCategory from "./VideoCategory";

const categoriesStyles = {
  // border: "1px solid #E6E6E6",
  borderRadius: "15px",
  padding: "20px 40px",
  width: "100%",
  height: "170px",
  whiteSpace: "nowrap",
  position: "relative",
  overflowX: "scroll",
  overflowY: "hidden",
  "::-webkit-scrollbar": {
    display: "none",
  },
  scrollbarWidth: "none",
};

export default function VideoCategoriesSection() {
  return (
    <SectionContainer title="Video Categories">
      <Box sx={categoriesStyles}>
        {categories.map((category, index) => (
          <VideoCategory key={index} category={category} />
        ))}
      </Box>
    </SectionContainer>
  );
}
