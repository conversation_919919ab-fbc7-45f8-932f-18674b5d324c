import { Typography, Box } from "@mui/material";
import Image from "next/image";

const categoryStylesContainer = {
  borderRadius: "15px",
  boxShadow: "0px 0px 22px 0px #0000001A",
  float: "none",
  display: "inline-block",
  margin: "0px 10px",
};

const CategoryStyles = {
  display: "flex",
  width: "190px",
  height: "112px",
  flexDirection: "Column",
  justifyContent: "center",
  alignItems: "center",
  gap: "0px 40px",
};

const iconStyles = { marginBottom: "9px" };

export default function VideoCategory({ category }) {
  return (
    <Box sx={categoryStylesContainer}>
      <Box sx={CategoryStyles}>
        {/* <SmartphoneIcon sx={categoryIconStyles}></SmartphoneIcon> */}
        <Image
          src={category.img}
          alt={category.name}
          width={44}
          height={44}
          styles={iconStyles}
        ></Image>
        <Typography>{category.name}</Typography>
      </Box>
    </Box>
  );
}
