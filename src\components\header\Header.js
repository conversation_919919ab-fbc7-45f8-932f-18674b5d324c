import React, { useState, useEffect, useMemo } from "react";
import { useUserContext } from "../../contexts/UserContext";
import Image from "next/image";
import { useRouter } from "next/router";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Menu from "@mui/material/Menu";
import MenuIcon from "@mui/icons-material/Menu";
import Container from "@mui/material/Container";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import useScrollTrigger from "@mui/material/useScrollTrigger";
import Slide from "@mui/material/Slide";
import useIsDesktop from "../../hooks/useIsDesktop";
import LanguageSwitch from "@/components/LanguageSwitch";
import Link from "next/link";
import { SignedIn, SignedOut, UserButton } from "@clerk/nextjs";
import { checkIfUserHasEverSignedIn } from "../../utils/clerkUtils";
import patitoLogo from "@/../public/images/icons/patito-feo.svg";
import ShoppingCartOutlinedIcon from "@mui/icons-material/ShoppingCartOutlined";
import { Badge } from "@mui/material";
import { useCartCountContext } from "@/contexts/CartCountContext";
import DashboardIcon from "@mui/icons-material/Dashboard";
import ClassesTopNav, { navs } from "../classes/ClassesTopNav";
import PersonIcon from "@mui/icons-material/Person";
import { styled } from "@mui/material/styles";
const webHeight = 50;
const webWidth = 55;
const mobileHeight = 35;
const mobileWidth = 40;

const DotIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 512 512"
      fill="currentColor"
    >
      <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512z" />
    </svg>
  );
};

const PatitoIcon = ({ h, w }) => {
  return (
    <Image
      src={patitoLogo}
      alt="Patito Logo"
      width={w}
      height={h}
      unoptimized={true}
    />
  );
};

function HideOnScroll(props) {
  const isDesktop = useIsDesktop();
  const { children, window } = props;
  const trigger = useScrollTrigger({
    target: window ? window() : undefined,
  });

  return isDesktop ? (
    <>{children}</>
  ) : (
    <Slide appear={false} direction="down" in={!trigger}>
      {children}
    </Slide>
  );
}

const pages = [
  { href: "/classes", label: "Classes" },
  { href: "/about-us", label: "About Us" },
];

// const pages = [];

const PulsingDot = ({ size = "md" }) => {
  return (
    <Box
      sx={{
        position: "absolute",
        top: "-5px",
        right: 0,
        width: `${size == "md" ? "12px" : "6px"}`,
        height: `${size == "md" ? "12px" : "6px"}`,
        borderRadius: "50%",
        backgroundColor: "#ff0000",
        zIndex: "99",
        "@keyframes pulse": {
          "0%": {
            transform: "scale(1)",
            opacity: 1,
          },
          "50%": {
            transform: "scale(1.5)",
            opacity: 0.5,
          },
          "100%": {
            transform: "scale(1)",
            opacity: 1,
          },
        },
        animation: "pulse 2s infinite",
      }}
    />
  );
};

const ProfileLabelIcon = ({ isPulsing = false }) => {
  return (
    <Box
      sx={{
        position: "relative",
        display: "inline-flex",
        alignItems: "center",
      }}
    >
      <PersonIcon sx={{ height: 16, width: 16 }} />
      {isPulsing && <PulsingDot size="sm" />}
    </Box>
  );
};

function ResponsiveAppBar(props) {
  const [anchorElNav, setAnchorElNav] = useState(null);
  const [userHasSignedIn, setUserHasSignedIn] = useState(false);
  const [isWaitlist, setIsWaitlist] = useState(false);
  const { dbUser, isProfileCompeleted } = useUserContext();
  const { count } = useCartCountContext({});
  const [promptProfileCompletion, setPromptProfileCompletion] = useState(false);
  const router = useRouter();
  const pathname = router.pathname;

  console.log("promptProfileCompletion", promptProfileCompletion);

  const showClassesNav = navs.some((s) => s.href === pathname);

  const [isHovered, setIsHovered] = useState(false);

  const handleOpenNavMenu = (event) => {
    setAnchorElNav(event.currentTarget);
  };

  const handleCloseNavMenu = () => {
    setAnchorElNav(null);
  };

  useEffect(() => {
    if (checkIfUserHasEverSignedIn()) {
      setUserHasSignedIn(true);
    } else {
      return;
    }
  }, []);

  useEffect(() => {
    setPromptProfileCompletion(dbUser && !isProfileCompeleted);
  }, [dbUser, isProfileCompeleted]);

  console.log({
    dbUser,
    isProfileCompeleted,
  });

  return (
    <>
      <HideOnScroll {...props}>
        <AppBar
          position="sticky"
          color="background"
          elevation={0}
          sx={{
            minHeight: { xs: "80px" },
            justifyContent: "center",
            borderBottom: "1px solid",
            borderColor: "rgba(0, 0, 0, 0.5)",
          }}
        >
          <Container sx={{ p: 0, m: 0, minWidth: "100%" }}>
            <Toolbar
              sx={{
                justifyContent: "space-between",
                padding: 0,
                alignItems: "center",
              }}
            >
              <Box
                sx={{
                  display: { xs: "none", lg: "flex" },

                  overflow: "hidden",
                  width: "30vw",
                  justifyContent: "flex-start",
                }}
              >
                <PatitoIcon h={webHeight} w={webWidth} />

                <Typography
                  variant="h3"
                  noWrap
                  component="a"
                  href="/"
                  sx={{
                    marginLeft: "15px",
                    display: { xs: "none", lg: "flex" },
                    fontWeight: 300,
                    fontSize: "24px",
                    color: "inherit",
                    alignItems: "center",
                    textDecoration: "none",
                    userSelect: "none",
                  }}
                >
                  {isHovered ? "Ugly Duckling" : "Patito Feo"}
                </Typography>
              </Box>

              <Box
                sx={{
                  display: { xs: "flex", lg: "none" },
                  paddingLeft: { xs: "32px" },
                  width: { xs: "85%", md: "auto", lg: "85%" },
                }}
              >
                <PatitoIcon h={mobileHeight} w={mobileWidth} />
                <Typography
                  variant="h5"
                  noWrap
                  component="a"
                  href="/"
                  sx={{
                    margin: 0,
                    marginLeft: "15px",
                    justifyContent: "left",
                    flexGrow: 1,
                    fontWeight: 300,
                    fontSize: "24px",
                    display: { xs: "none", xsm: "flex" },
                    color: "inherit",
                    textDecoration: "none",
                    userSelect: "none",
                  }}
                >
                  {isHovered ? "Ugly Duckling" : "Patito Feo"}
                </Typography>
              </Box>
              <Box
                sx={{
                  display: { xs: "flex", md: "none" }, // This makes the box a flex container
                  alignItems: "center",
                  flexGrow: 1,
                  justifyContent: "flex-end",
                }}
              >
                <Box
                  sx={{
                    margin: {
                      xs: "5px 30px 0px 30px",
                      md: "0px 30px 0px 0px",
                    },
                  }}
                >
                  <Link href="/cart">
                    <Badge
                      badgeContent={<BadgeContent count={count} />}
                      color="primary"
                    >
                      <ShoppingCartOutlinedIcon
                        sx={{
                          fontSize: { xs: "25px", sm: "25px", md: "25px" },
                          color: "black",
                        }}
                      />
                    </Badge>
                  </Link>
                </Box>
                <IconButton
                  size="large"
                  aria-label="account of current user"
                  aria-controls="menu-appbar"
                  aria-haspopup="true"
                  onClick={handleOpenNavMenu}
                  color="inherit"
                >
                  <MenuIcon />
                </IconButton>

                <Menu
                  id="menu-appbar"
                  anchorEl={anchorElNav}
                  anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "left",
                  }}
                  keepMounted
                  transformOrigin={{
                    vertical: "top",
                    horizontal: "left",
                  }}
                  open={Boolean(anchorElNav)}
                  onClose={handleCloseNavMenu}
                  sx={{
                    display: { xs: "block", lg: "none" },
                  }}
                >
                  {pages.map((page, index) => {
                    return (
                      <MenuItem key={page.label} onClick={handleCloseNavMenu}>
                        <Link
                          key={page.href}
                          href={page.href}
                          style={{ textDecoration: "none", color: "black" }}
                        >
                          {page.label}
                        </Link>
                      </MenuItem>
                    );
                  })}
                  <AuthOption
                    isMobile
                    handleCloseNavMenu={handleCloseNavMenu}
                    isWaitlist={isWaitlist}
                    userHasSignedIn={userHasSignedIn}
                    promptProfileCompletion={promptProfileCompletion}
                  />
                </Menu>
              </Box>
              <Box
                sx={{
                  display: { xs: "none", md: "flex" },
                  justifyContent: "center",
                  gap: (theme) => theme.spacing(12),
                  width: { lg: "40vw" },
                  flexGrow: 1,
                }}
              >
                {pages.map((page) => (
                  <Link
                    key={page.href}
                    href={page.href}
                    style={{ textDecoration: "none" }}
                    className="text-black hover:text-teal-500"
                  >
                    {page.label}
                  </Link>
                ))}
              </Box>

              <Box
                sx={{
                  display: { xs: "none", md: "flex" },
                  justifyContent: "flex-end",
                  alignItems: "center",
                  width: { lg: "30vw" },
                  mr: { lg: 12 },
                }}
              >
                {/** Language Switch Tablet - Desktop starts */}
                <Box
                  sx={{
                    margin: {
                      md: "5px 40px 0px 0px",
                    },
                  }}
                >
                  <LanguageSwitch />
                </Box>
                {/** Language Switch Tablet - Desktop ends */}

                <Box
                  sx={{
                    margin: {
                      md: "5px 40px 0px 0px",
                    },
                  }}
                >
                  <Link href="/cart">
                    <Badge
                      badgeContent={<BadgeContent count={count} />}
                      color="primary"
                    >
                      <ShoppingCartOutlinedIcon
                        sx={{
                          fontSize: { xs: "25px", sm: "25px", md: "25px" },
                          color: "black",
                        }}
                      />
                    </Badge>
                  </Link>
                </Box>
                <AuthOption
                  handleCloseNavMenu={handleCloseNavMenu}
                  isWaitlist={isWaitlist}
                  userHasSignedIn={userHasSignedIn}
                  promptProfileCompletion={promptProfileCompletion}
                />
              </Box>
            </Toolbar>
          </Container>
          {showClassesNav && <ClassesTopNav />}
        </AppBar>
      </HideOnScroll>
    </>
  );
}

const AuthOption = ({
  isMobile = false,
  handleCloseNavMenu,
  isWaitlist,
  userHasSignedIn,
  promptProfileCompletion,
}) => {
  const { title, href } = useMemo(() => {
    return {
      href: isWaitlist
        ? "/waitlist"
        : userHasSignedIn
        ? "/sign-in"
        : "/sign-up",
      title: isWaitlist ? "Waitlist" : userHasSignedIn ? "Sign In" : "Sign Up",
    };
  }, [isWaitlist, userHasSignedIn]);

  return (
    <>
      <SignedOut>
        {isMobile ? (
          <MenuItem onClick={handleCloseNavMenu}>
            <Link
              href={href}
              passHref
              style={{ textDecoration: "none", color: "black" }}
            >
              <Typography style={{ textDecoration: "none", color: "black" }}>
                {title}
              </Typography>
            </Link>
          </MenuItem>
        ) : (
          <Link href={href} passHref>
            <Button
              sx={{
                fontWeight: 600,
                color: "white",
                width: "120px",
                borderRadius: "10px",
                backgroundColor: "#F3B358",
                border: "none",
                "&:hover": {
                  backgroundColor: "#02AC9F",
                },
              }}
            >
              {title}
            </Button>
          </Link>
        )}
      </SignedOut>
      <SignedIn>
        <Box sx={{ position: "relative" }}>
          {promptProfileCompletion ? <PulsingDot /> : null}
          {promptProfileCompletion && (
            <UserButton>
              <UserButton.MenuItems>
                <UserButton.Link
                  label="Complete profile"
                  labelIcon={
                    <ProfileLabelIcon isPulsing={promptProfileCompletion} />
                  }
                  href="/profile"
                />
              </UserButton.MenuItems>
              <UserButton.MenuItems>
                <UserButton.Link
                  label="My Dashboard"
                  labelIcon={<DashboardIcon sx={{ height: 16, width: 16 }} />}
                  href="/profile/dashboard"
                />
              </UserButton.MenuItems>
            </UserButton>
          )}
          {!promptProfileCompletion && (
            <>
              <UserButton>
                <UserButton.MenuItems>
                  <UserButton.Link
                    label="My Profile"
                    labelIcon={
                      <ProfileLabelIcon isPulsing={promptProfileCompletion} />
                    }
                    href="/manage-profile"
                  />
                </UserButton.MenuItems>
                <UserButton.MenuItems>
                  <UserButton.Link
                    label="My Dashboard"
                    labelIcon={<DashboardIcon sx={{ height: 16, width: 16 }} />}
                    href="/profile/dashboard"
                  />
                </UserButton.MenuItems>
              </UserButton>
            </>
          )}
        </Box>
      </SignedIn>
    </>
  );
};

const BadgeContent = ({ count }) => {
  return <p>{count}</p>;
};

export default ResponsiveAppBar;
