import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import ActivityCard from "./ActivityCard";
import img from "../../../public/images/home/<USER>";
import { Container, But<PERSON> } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/router";

const SeeMoreURL = "classes/experiencias-libres";

const getEarliestDate = (numDays) => {
  const today = new Date();
  const futureDate = new Date();
  futureDate.setDate(today.getDate() + numDays);

  return futureDate.toLocaleString("en-US", {
    month: "long",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });
};

export default function ActivitiesSection() {
  const router = useRouter();

  const handleClick = () => {
    router.push(SeeMoreURL);
  };
  return (
    <Box
      sx={{
        padding: {
          xs: "60px 15px 30px 15px",
          sm: "60px 20px 60px 20px",
          md: "60px 30px 60px 30px",
          lg: "40px 25px 40px 25px",
        },
      }}
    >
      <Container>
        <Grid
          container
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {/* title/banner section */}
          <Grid
            className=" md:flex md:flex-col md:justify-start"
            item
            xs={12}
            md={4}
            sx={{
              flexDirection: "column",
              paddingBottom: { xs: "40px", md: "0px" },
              marginLeft: { xs: "0px", md: "10px" },
            }}
          >
            <Grid>
              <Typography
                variant="h3"
                sx={{
                  fontSize: "48px",
                  fontWeight: "500",
                  marginBottom: "30px",
                }}
              >
                Upcoming Activites
              </Typography>
            </Grid>
            <Grid>
              <Typography
                variant="h3"
                sx={{
                  fontSize: { xs: "24px", lg: "24px" },
                  fontWeight: "500",
                  marginBottom: "30px",
                  paddingRight: "0px",
                }}
              >
                We look forward to seeing you at our next events and sharing
                these memorable experiences with you!
              </Typography>
              <Button onClick={handleClick}>View More</Button>
            </Grid>
          </Grid>

          <Grid
            item
            xs={12}
            md={7}
            sx={{
              aspectRatio: "2 / 1",
              position: "relative",
              marginRight: { xs: "0px", md: "10px" },
              height: { xs: "200px", sm: "400px", md: "350px" }, // Fixed height for the banner image
            }}
          >
            <Image
              src={img}
              blurDataURL={img}
              alt="Image of upcoming activities"
              fill
              style={{
                borderRadius: "8px",
                objectFit: "cover",
                objectPosition: "center bottom",
              }}
            />
          </Grid>

          {/* events container */}
          <Grid container spacing={2} sx={{ paddingTop: "30px" }}>
            <ActivityCard
              image="/images/home/<USER>"
              title="Chocolate Tasting"
              date={getEarliestDate(3)}
              description="Indulge in the finest chocolates and learn about their origins, processing, and flavor profiles."
              index={0}
            />
            <ActivityCard
              image="/images/home/<USER>"
              title="Movie Night"
              date={getEarliestDate(5)}
              description="Join us for a screening of a classic film. Popcorn and drinks will be provided."
              index={1}
            />
            <ActivityCard
              image="/images/home/<USER>"
              title="Mezcal Tasting"
              date={getEarliestDate(6)}
              description="Discover the rich history and flavors of Mezcal in an expert-led tasting."
              index={2}
            />
            <ActivityCard
              image="/images/home/<USER>"
              title="Trivia Night"
              date={getEarliestDate(7)}
              description="Test your knowledge on a range of topics from history to pop culture. Great prizes up for grabs!"
              index={3}
            />
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}
