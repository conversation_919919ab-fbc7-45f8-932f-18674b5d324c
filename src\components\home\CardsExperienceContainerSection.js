import React, { useState } from "react";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import ExperienceCard from "./ExperienceCard";
import { Container } from "@mui/material";
import { useUserContext } from "@/contexts/UserContext";

import logo from "@/../public/images/icons/patito-feo.svg";
import Image from "next/image";

const experiences = [
  {
    id: 1,
    title: "Videos",
    image: "/images/home/<USER>",
    position: "top",
  },
  {
    id: 2,
    title: "Clubs",
    image: "/images/home/<USER>",
    position: "center",
  },
  {
    id: 3,
    title: "Podcasts",
    image: "/images/home/<USER>",
    position: "top",
  },
  {
    id: 4,
    title: "Games",
    image: "/images/home/<USER>",
    position: "center",
  },
];

export default function CardsExperienceContainerSection() {
  const { translate } = useUserContext();

  return (
    <Box
      sx={{
        width: "100%",
        backgroundColor: "#F2F2F2",
      }}
    >
      <Container>
        <Grid
          container
          sx={{
            padding: { xs: "0px", sm: "20px", md: "30px" },
          }}
        >
          <Grid
            item
            xs={12}
            sm={4}
            md={3}
            lg={4}
            sx={{
              display: { xs: "none", sm: "flex" },
              flexDirection: { xs: "row", md: "column" },
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Image
              src={logo}
              width={200}
              height={200}
              alt="Patito Feo Logo"
            ></Image>
          </Grid>
          <Grid
            item
            xs={12}
            sm={8}
            md={9}
            lg={8}
            sx={{
              padding: "20px",
            }}
          >
            <Typography
              variant="h3"
              sx={{
                fontSize: { xs: "28px", md: "32px", lg: "32px" },
                fontWeight: { xs: "400", sm: "500" },
                paddingBottom: "15px",
              }}
            >
              { translate('home.experience.heading') }
            </Typography>
            <Typography
              variant="h2"
              sx={{
                fontWeight: "500",
                fontSize: { xs: "34px", sm: "40px", md: "48px", lg: "48px" },
              }}
            >
              { translate('home.experience.desc') }
            </Typography>
          </Grid>
        </Grid>

        <Grid container spacing={5} sx={{ padding: "40px 40px 40px 40px" }}>
          {experiences.map((m) => (
            <ExperienceCard
              key={m.id}
              index={experiences.length - m.id}
              title={m.title}
              image={m.image}
              objectPosition={m.position}
            />
          ))}
        </Grid>
      </Container>
    </Box>
  );
}
