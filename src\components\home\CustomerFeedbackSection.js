import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import { Container } from "@mui/material";
import Marquee from "react-fast-marquee";
import { testimonials } from "@/constant/about/Testimonials";

export default function CustomerFeedbackSection() {
  return (
    <Box
      sx={{
        padding: {
          xs: "30px 20px",
          sm: "0px 35px 60px",
          md: "0px 50px 100px",
          lg: "40px 50px 100px 50px",
        },
      }}
    >
      <Container
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center", // Center vertically
        }}
      >
        <Typography
          variant="h3"
          sx={{
            fontSize: { xs: "48px" },
            fontWeight: "500",
            textAlign: "center",
            paddingBottom: {
              xs: "20px",
              sm: "20px",
              md: "20px",
            },
          }}
        >
          What Our{" "}
          <span
            style={{
              color: "#F9B238",
              fontWeight: "500",
            }}
          >
            Customers
          </span>{" "}
          Say
        </Typography>

        <Typography
          variant="body1"
          sx={{
            fontSize: { xs: "24px" },
            textAlign: "center",
            paddingBottom: { xs: "40px", sm: "40px", md: "40px", lg: "50px" },
          }}
        >
          Hear what our students have to say!
        </Typography>
        <Box
          display="flex"
          flexDirection="row"
          py={6}
          sx={{ overflow: "hidden", width: "100%" }}
        >
          <Marquee pauseOnHover pauseOnClick>
            {testimonials.map((item, index) => (
              <FeedbackBox key={`testimonial-${index}`} data={item} />
            ))}
          </Marquee>
        </Box>
      </Container>
    </Box>
  );
}

const FeedbackBox = ({ data }) => {
  return (
    <Box
      sx={{
        border: "2px solid #000",
        width: { xs: "200px", xsm: "300px", md: "415px", lg: "615px" },
        flex: "0 0 auto",
        fontSize: { xs: "10px", md: "14px" },
        padding: {
          xs: "20px 40px 20px 40px",
          sm: "20px 40px 20px 40px",
          md: "20px 40px 20px 40px",
          lg: "20px 40px 20px 40px",
        },
        borderRadius: "5px",
        m: 1,
        mx: 4,
      }}
    >
      <Typography
        sx={{
          textAlign: "center",
        }}
      >
        &quot;{data.review}&quot;
      </Typography>

      <Typography
        sx={{
          textAlign: "center",
          fontSize: { sm: "20px" },
          fontWeight: { sm: "500" },
        }}
      >
        {data.name}
      </Typography>
    </Box>
  );
};
