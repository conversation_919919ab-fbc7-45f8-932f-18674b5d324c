import React from "react";
import Grid from "@mui/material/Grid";
import Image from "next/image";
import { useSpring, animated, useInView } from "react-spring";
import { Card, CardContent, Typography, Box } from "@mui/material";
export default function ExperienceCard({ title, image, index, objectPosition }) {
  const [ref, springs] = useInView(
    () => ({
      from: { opacity: 0, transform: "translateY(50px)" },
      to: { opacity: 1, transform: "translateY(0px)" },
      config: { tension: 40, friction: 40 },
      delay: index * 2000,
      once: true,
    }),
    { rootMargin: "-10% 0px" }
  );
  return (
    <Grid item xs={12} sm={6} md={3} sx={{}}>
      <animated.div style={springs} ref={ref}>
        <Card
          sx={{
            maxWidth: { xs: 600, sm: 345 },
            backgroundColor: "#F2F2F2",
            borderRadius: 5,
            boxShadow: "0px 4px 4px 0px #00000040",
            margin: {
              xs: "15px auto 15px auto",
              sm: "15px auto 15px auto",
              md: "15px auto 15px auto",
            },
          }}
        >
          <Box
            sx={{
              borderRadius: 5,
              height: {
                xs: "200px",
                lg: "338px",
              },
              width: {
                xs: "100%",
              },
              position: "relative",
            }}
          >
            <Image
              fill
              src={image}
              alt="Card image"
              style={{
                objectFit: "cover",
                objectPosition,
                borderRadius: "5px",
              }}
            />
          </Box>
          <CardContent>
            <Typography
              variant="h4"
              component="div"
              sx={{
                textAlign: "center",
                fontWeight: "600",
                fontSize: "32px",
              }}
            >
              {title}
            </Typography>
          </CardContent>
        </Card>
      </animated.div>
    </Grid>
  );
}
