import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { Container } from '@mui/material';
import { antonioFont } from '@/constant/font';
import Image from 'next/image';

export default function HomeVideoSection() {
  const showVideo = process.env.NEXT_PUBLIC_HERO_VIDEO === 'true';

  return (
    <Container
      sx={{
        position: 'relative',
        margin: '0 !important',
        padding: '0 !important',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        minWidth: '100%',
        width: '100%',
        minHeight: '100vh',
        overflow: 'hidden',
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: -1,
          overflow: 'hidden',
          width: '100%',
          minHeight: '100vh',
          overflow: 'hidden',
        }}
      >
        {showVideo ? (
          <video
            id='hero-video'
            autoPlay
            muted
            loop
            playsInline
            controls={false}
            poster='/assets/home-vid-poster.jpg'
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              top: 0,
              left: 0,
            }}
          >
            <source src='/assets/homepageVideoEg.mp4' type='video/mp4' />
          </video>
        ) : (
          <Image
            src='/assets/home-hero-image.webp'
            alt='Hero background'
            fill
            priority
            style={{
              objectFit: 'cover',
            }}
          />
        )}
      </Box>
      <Box
        sx={{
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          opacity: 0.8,
        }}
      >
        {!showVideo ? (
          <Box
            sx={{
              position: 'relative',
              width: { xs: '150px', sm: '200px' },
              height: { xs: '150px', sm: '200px' },
              marginBottom: 2,
            }}
          >
            <Image
              src='/images/icons/patito-feo.svg'
              alt='Logo'
              fill
              style={{
                objectFit: 'contain',
              }}
            />
          </Box>
        ) : null}
        <Typography
          component='span'
          sx={{
            width: { xs: 'auto', md: '100vw' },
            fontFamily: antonioFont,
            fontWeight: 700,
            flexShrink: 0,
            fontSize: { xs: '2.5rem', sm: '3.1rem', md: '4.3rem' },
            textAlign: 'center',
            userSelect: 'none',
            color: 'white',
            letterSpacing: 1,
            textShadow: '2px 2px 8px rgba(0, 0, 0, 0.6)',
          }}
        >
          WE ARE MORE
        </Typography>
        <Typography
          component='span'
          sx={{
            width: { xs: 'auto', md: '100vw' },
            fontFamily: antonioFont,
            fontWeight: 300,
            flexShrink: 0,
            fontSize: { xs: '2rem', sm: '2.5rem', md: '3.5rem' },
            textAlign: 'center',
            userSelect: 'none',
            color: 'white',
            letterSpacing: 1,
            textShadow: '2px 2px 8px rgba(0, 0, 0, 0.6)',
          }}
        >
          THAN A SCHOOL
        </Typography>
      </Box>
    </Container>
  );
}
