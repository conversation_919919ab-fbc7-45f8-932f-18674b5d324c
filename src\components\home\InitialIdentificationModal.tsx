import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { Modal, Box, Button, Typography } from "@mui/material";
import CustomButton from "../CustomButton";
import Image from "next/image";
import { useUserContext } from "@/contexts/UserContext";

export default function InitialIdentificationModal() {
  const { user, isLoaded } = useUserContext();
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (!user && isLoaded) {
      const lang = Cookies.get("siteLang");
      if (!lang) setOpen(true);
    }
  }, [user, isLoaded]);

  const handleSelect = (lang: string) => {
    Cookies.set("siteLang", lang, { expires: 365 });
    setOpen(false);
  };

  return (
    <Modal open={open}>
      <Box
        sx={{
          bgcolor: "white",
          p: 4,
          mx: "auto",
          mt: "20%",
          maxWidth: 400,
          borderRadius: 2,
          boxShadow: 4,
          textAlign: "center",
        }}
      >
        <Typography fontSize="1.2rem" fontWeight={700} mb={2}>
          Select your preference
        </Typography>
        <Typography fontSize="0.9rem" color="text.secondary" sx={{ mb: 3 }}>
          Choose the language you’d like to learn so we can personalize your
          experience and show the most relevant content.
        </Typography>
        <Image height={150} width={150} alt="patito" src="/patitoB.png" />
        <Box display="flex" flexDirection="column" width="100%" mt={2}>
          <CustomButton
            text="I want to learn Spanish"
            onClick={() => handleSelect("spanish")}
            sx={{
              width: "100%",
            }}
          />
          <CustomButton
            colortype="secondary"
            text="Quiero aprender ingles"
            onClick={() => handleSelect("english")}
            sx={{
              width: "100%",
              mt: 2,
            }}
          />
        </Box>
      </Box>
    </Modal>
  );
}
