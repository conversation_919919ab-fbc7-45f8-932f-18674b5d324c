import { antonioFont } from "@/constant/font";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";

export default function OurMission() {
  return (
    <Box
      sx={{
        width: "100%",
        borderBottom: "1px solid",
        borderColor: "rgba(0, 0, 0, 0.5)",
        padding: { xs: "30px 20px", sm: "45px 35px", md: "50px 40px" },
        background:
          "linear-gradient(#F4B35782, #F4B35782), url('/bg-game.jpg')",
        backgroundPosition: "center",
        backgroundSize: "cover",
        textAlign: "center",
      }}
    >
      <Typography
        component="span"
        sx={{
          fontFamily: antonioFont,
          fontWeight: 400,
          flexShrink: 0,
          fontSize: { xs: "80px", md: "227px" },
          userSelect: "none",
          color: "#F4B357",
        }}
      >
        Our mission.
      </Typography>
    </Box>
  );
}
