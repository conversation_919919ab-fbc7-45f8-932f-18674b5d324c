import React, { useState } from "react";
import { Box, Paper, Grid, Button } from "@mui/material";

const WordCategoryGrid = ({ gameData, handleSuccess }) => {
  const [selectedTiles, setSelectedTiles] = useState([]);

  const handleTileClick = (word) => {
    setSelectedTiles((prev) => {
      const isSelected = prev.includes(word);
      if (isSelected) {
        return prev.filter((tile) => tile !== word);
      } else {
        return prev.length < 4 ? [...prev, word] : prev;
      }
    });
  };

  const handleSubmit = () => {
    if (selectedTiles.length === 4) {
      const correctGroup = gameData.groups.find((group) =>
        selectedTiles.every((tile) =>
          group.words.map((w) => w.en).includes(tile)
        )
      );

      if (correctGroup) {
        handleSuccess();
        alert("Correct! All selected items belong to the same group.");
      } else {
        alert(
          "Incorrect selection. The selected items do not belong to the same group."
        );
      }
    } else {
      alert("Please select exactly 4 tiles.");
    }
  };

  if (!gameData || !gameData.groups) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <Box sx={{ display: "flex", justifyContent: "center" }}>
        <Grid container spacing={2} sx={{ maxWidth: 420, margin: "auto" }}>
          {gameData.map((wordObj, index) => (
            <Grid item key={index} sx={{ width: 100, height: 100 }}>
              <Paper
                elevation={selectedTiles.includes(wordObj.en) ? 8 : 1}
                onClick={() => handleTileClick(wordObj.en)}
                sx={{
                  width: "100%",
                  height: "100%",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  cursor: "pointer",
                  backgroundColor: selectedTiles.includes(wordObj.en)
                    ? "#ED7DA0"
                    : "inherit",
                  color: selectedTiles.includes(wordObj.en) ? "white" : "black",
                }}
              >
                {wordObj.en}
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Box>
      <Box sx={{ display: "flex", justifyContent: "center", marginTop: 2 }}>
        <Button variant="contained" onClick={handleSubmit}>
          Submit
        </Button>
      </Box>
    </>
  );
};

export default WordCategoryGrid;
