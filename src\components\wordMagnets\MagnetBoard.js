import React, { useEffect, useState } from "react";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { Button } from "@mui/material";
import { BaseModal } from "@/components/Modals/BaseModal";

//https://www.fluentu.com/blog/spanish/mexican-quotes-sayings-proverbs/
const Sentences = [
  "Al mal tiempo, buena cara.",
  "Cuentas claras, amistades largas",
  "El que con lobos anda, a aullar se enseña.",
  "A la mejor cocinera se le queman los frijoles.",
  "Barriga llena, corazón contento.",
  "Lo barato cuesta caro.",
  "No todo lo que brilla es oro.",
  "Honra y dinero se ganan despacio y se pierden ligero.",
  "A quien madruga, Dios lo ayuda.",
];

const AlertModal = ({ isOpen, isWinMode, handleClose }) => {
  const title = isWinMode ? "You Win!" : "You Lose!";
  return (
    <BaseModal title={title} isOpen={isOpen} handleClose={handleClose}>
      <p className="text-sm text-gray-500 dark:text-gray-300">
        {isWinMode ? "You have won the game!" : "You have lost the game!"}
      </p>
    </BaseModal>
  );
};
export const PetCard = ({ id, name }) => {
  const [{ isDragging }, dragRef] = useDrag({
    type: "word",
    item: { id, name },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  return (
    <div
      className="pet-card p-4 m-4 bg-yellow-300 rounded-md border-solid border-1 h-fit"
      ref={dragRef}
    >
      {name}
      {isDragging && "😱"}
    </div>
  );
};

const Basket = () => {
  const [basket, setBasket] = useState([]);
  const [words, setWords] = useState([]);
  const [originalWords, setOriginalWords] = useState([]);
  const [sentence, setSentence] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [isWin, setIsWin] = useState(false);
  const [isShowSolution, setIsShowSolution] = useState(false);
  const [solution, setSolution] = useState("");
  const [isRestart, setIsRestart] = useState(false);

  useEffect(() => {
    const _sentence = Sentences[Math.floor(Math.random() * Sentences.length)];
    setSentence(_sentence);
  }, [isRestart]);

  useEffect(() => {
    const _words = sentence
      .split(" ")
      .map((word, index) => ({ id: index, name: word }));
    const randomizedWords = [..._words];
    randomizedWords.sort((a, b) => a.name.localeCompare(b.name));
    randomizedWords.sort(() => Math.random() - 0.5);
    setWords(randomizedWords);
    setOriginalWords(_words);
  }, [sentence]);

  useEffect(() => {
    if (basket.length && basket.length === originalWords.length) {
      if (checkWin(basket, originalWords)) {
        setIsWin(true);
      } else {
        setIsWin(false);
      }
      setIsOpen(true);
    }
  }, [basket, originalWords]);

  const checkWin = (basket, originalWords) => {
    if (basket.length && basket.length === originalWords.length) {
      return !originalWords.some(
        (word, index) => word.name !== basket[index].name
      );
    }
    return false;
  };

  const [{ isOver }, dropRef] = useDrop({
    accept: "word",
    drop: (item) => {
      setBasket((basket) => {
        return !basket.map((item) => item.id).includes(item.id)
          ? [...basket, item]
          : basket;
      });
      setWords((words) => words.filter((pet) => pet.id !== item.id));
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  const [, dropRefTop] = useDrop({
    accept: "word",
    drop: (item) => {
      setWords((words) => {
        return !words.map((item) => item.id).includes(item.id)
          ? [...words, item]
          : words;
      });
      setBasket((basket) => basket.filter((pet) => pet.id !== item.id));
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  const handleRestart = () => {
    setIsShowSolution(false);
    setBasket([]);
    setIsRestart(!isRestart);
  };
  const onShowSolution = () => {
    setIsShowSolution(true);
    setSolution(sentence);
  };

  return (
    <React.Fragment>
      <div
        ref={dropRefTop}
        className="flex justify-center "
        style={{ minHeight: "10em" }}
      >
        <div className="words flex flex-wrap">
          {words.map((pet) => (
            <PetCard key={pet.id} draggable id={pet.id} name={pet.name} />
          ))}
          {basket.map((item, index) => (
            <div
              key={item.id}
              className="pet-card p-4 m-4 bg-grey-100 rounded-md border-solid border-1 h-fit"
            >
              Drop Here!
            </div>
          ))}
        </div>
      </div>
      <div
        className="basket flex flex-wrap justify-center items-center h-fit bg-lime-100"
        style={{ minHeight: "10em" }}
        ref={dropRef}
      >
        {basket.map((pet) => (
          <PetCard key={pet.id} id={pet.id} name={pet.name} />
        ))}
        {words.map((item, index) => (
          <div
            key={item.id}
            className="pet-card p-4 m-4 bg-grey-100 rounded-md border-solid border-1 h-fit"
          >
            Drop Here!
          </div>
        ))}
      </div>
      <div className="flex grow flex-col justify-center pb-6 short:pb-2 mt-4">
        <div className="flex justify-center mb-4">
          <Button
            onClick={handleRestart}
            className="ml-2"
            variant="contained"
            color="primary"
          >
            Restart
          </Button>
          <Button
            onClick={onShowSolution}
            className="ml-2"
            variant="contained"
            color="primary"
          >
            Show Answer
          </Button>
        </div>

        <div className="flex justify-center mx-4">
          {isShowSolution && (
            <p className="text-green-400 text-4xl font-bold">{solution}</p>
          )}
        </div>
      </div>
      <AlertModal
        isOpen={isOpen}
        isWinMode={isWin}
        handleClose={() => {
          setIsOpen(!isOpen);
        }}
      />
    </React.Fragment>
  );
};

export const App = () => {
  return (
    <DndProvider backend={HTML5Backend}>
      <Basket />
    </DndProvider>
  );
};

export default App;
