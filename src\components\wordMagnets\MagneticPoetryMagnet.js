import React from "react";
import { useSpring, animated, to } from "react-spring";
import { useDrag } from "react-use-gesture";
import { Box } from "@mui/material";

const MagneticPoetryMagnet = ({ text }) => {
  const [{ x, y }, set] = useSpring(() => ({ x: 0, y: 0 }));

  const bind = useDrag(({ down, movement: [mx, my] }) => {
    set({ x: down ? mx : 0, y: down ? my : 0 });
    console.log("dragging", down);
    console.log("x value", x.get());
    console.log("y value", y.get());
  });

  return (
    <animated.div
      {...bind()}
      style={{
        transform: to([x, y], (x, y) => `translate3d(${x}px, ${y}px, 0)`),
        // Add additional styles...
      }}
    >
      <Box
        sx={{
          display: "inline-flex",
          alignItems: "center",
          justifyContent: "center",
          borderRadius: "50%",
          backgroundColor: "#F4D03F",
          color: "#333",
          padding: 1,
          cursor: "grab",
          userSelect: "none",
          boxShadow: "0 2px 5px rgba(0, 0, 0, 0.15)",
          fontFamily: "Arial, sans-serif",
          fontSize: "14px",
          fontWeight: "bold",
          "&:active": {
            cursor: "grabbing",
          },
        }}
      >
        {text}
      </Box>
    </animated.div>
  );
};

export default MagneticPoetryMagnet;
