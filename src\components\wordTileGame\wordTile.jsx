import * as React from "react";
import { styled } from "@mui/system";
import { animated } from "react-spring";

const BlockWrapper = styled(animated.div)`
  height: ${(props) => props.height}px;
  position: absolute;
  align-items: center;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
  width: 108px;
  border-radius: 6px;
  background: red;
  margin-bottom: 7px;
  margin-right: 7px;
  box-shadow: 2px 3px 4px rgba(0, 0, 0, 0.5);
  ${({ background }) => background && `background: ${background};`}
`;

const StyledText = styled("p")`
  color: white;
  font-weight: 700;
  font-size: 16px;
  user-select: none;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
`;

const Tile = ({ label, ...props }) => {
  return (
    <BlockWrapper {...props}>
      <StyledText>{label}</StyledText>
    </BlockWrapper>
  );
};

export default Tile;
