import React, { useCallback, useRef, useEffect, useState } from "react";
import useDraggable from "../../hooks/useDraggable";
import Tile from "./wordTile";
import { BlockContainer, Wrapper } from "./wordTileStyled";
import {
  getBlockCoordinates,
  getColor,
  shuffleArray,
  swapListItems,
} from "./wordTileUtils";
import { useSprings } from "react-spring";

const tileWidth = 108;
const tileGridMargin = 6;
const immediateMotionsProsp = {
  x: true,
  y: true,
};

const WordTileGrid = ({
  phrase,
  handleSuccess,
  wrapperHeight,
  tileHeight,
  tilesInRow: blockInRow,
}) => {
  const [gameOver, setGameOver] = useState(false);
  const [shuffledWords, setShuffledWords] = useState([]);
  const parentRef = useRef(null);
  const {
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    movingBlockIndex,
    block: movingBlock,
  } = useDraggable({
    parentRef,
  });

  console.log("mels pharse", phrase);

  const checkOrderCorrect = () => {
    const currentPhrase = blocks.current.reduce((sentence, currentIndex) => {
      const word = shuffledWords[currentIndex];
      return sentence ? `${sentence} ${word}` : word;
    }, "");

    return currentPhrase === phrase;
  };

  const calculateTileHeight = () => {
    const totalBlocks = phrase.split(" ").length;
    const rows = Math.ceil(totalBlocks / blockInRow);
    const height = Math.floor(wrapperHeight / rows);
    return height < 90 ? height : 90;
  };

  const blocks = useRef(
    new Array(phrase.split(" ").length).fill(0).map((_, i) => i)
  );

  const initialCoordinates = useRef(
    blocks.current.map((i) =>
      getBlockCoordinates(i, tileWidth, tileHeight, tileGridMargin, blockInRow)
    )
  );
  const bgColors = useRef(blocks.current.map((i) => getColor(i)));

  const updateBlockOrder = () => {
    const oldPosition = blocks.current.indexOf(movingBlockIndex); // Find previous position of the block being moved.
    if (oldPosition !== -1) {
      const coordinatesMoved = {
        x: movingBlock.x - initialCoordinates.current[oldPosition].x, // Calculate the x moved.
        y: movingBlock.y - initialCoordinates.current[oldPosition].y, // Calculate the y moved.
      };
      let y = Math.round(coordinatesMoved.y / tileHeight); // Calculate number of vertical blocks moved.
      if (Math.abs(y) > 0.5) {
        y = y * blockInRow; // If moved vertically, adjust for number of blocks per row.
      }
      const x = Math.round(coordinatesMoved.x / tileWidth); // Calculate number of horizontal blocks moved.

      const newPosition = y + x + oldPosition; // Calculate new position based on the movement.
      if (newPosition !== oldPosition) {
        blocks.current = swapListItems(
          blocks.current,
          oldPosition,
          newPosition
        );
      }
    }
  };

  const animate = useCallback(
    (index) => {
      const blockIndex = blocks.current.indexOf(index); // Find the index of the block being processed.
      const blockCoordinate = initialCoordinates.current[blockIndex]; // Retrieve the initial coordinates of the block.
      return {
        x: index === movingBlockIndex ? movingBlock.x : blockCoordinate.x, // If block moving use current x else use og.
        y: index === movingBlockIndex ? movingBlock.y : blockCoordinate.y, // If block moving use current y else use og.
        scale: index === movingBlockIndex ? 1.2 : 1,
        zIndex: index === movingBlockIndex ? 10 : 1,
        immediate:
          movingBlockIndex === index
            ? (n) => immediateMotionsProsp[n] // If the block is the one being moved, apply immediate motion properties.
            : undefined,
      };
    },
    [movingBlock, initialCoordinates, movingBlockIndex]
  );

  const [springs, api] = useSprings(blocks.current.length, animate);

  useEffect(() => {
    calculateTileHeight();
    const newShuffledWords = shuffleArray(phrase.split(" "));
    setShuffledWords(newShuffledWords);
  }, [phrase]);

  useEffect(() => {
    updateBlockOrder(movingBlock, movingBlockIndex, blocks, initialCoordinates);
    api.start(animate); // Restart animation with updated block order.
  }, [api, animate, initialCoordinates, movingBlock, movingBlockIndex]);

  useEffect(() => {
    if (checkOrderCorrect() && !gameOver) {
      handleSuccess();
      setGameOver(true);
    }
  }, [blocks.current, gameOver, handleSuccess]);

  return (
    <BlockContainer
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <Wrapper
        ref={parentRef}
        sx={{
          height: wrapperHeight,
          width: 4 + blockInRow * tileWidth + (blockInRow - 1) * tileGridMargin,
        }}
      >
        {springs.map((style, index) => {
          const blockIndex = blocks.current.indexOf(index);
          return (
            <Tile
              background={bgColors.current[index]}
              key={index}
              label={shuffledWords[index]}
              style={style}
              sx={{ height: tileHeight }}
              onMouseDown={(e) =>
                handleMouseDown(
                  e,
                  initialCoordinates.current[blockIndex],
                  index
                )
              }
              onTouchStart={(e) =>
                handleTouchStart(
                  e,
                  initialCoordinates.current[blockIndex],
                  index
                )
              }
            />
          );
        })}
      </Wrapper>
    </BlockContainer>
  );
};

export default WordTileGrid;
