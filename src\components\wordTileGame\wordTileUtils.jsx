export const getBlockCoordinates = (
  index,
  width,
  height,
  margin,
  blocksInRow
) => {
  const col = Math.floor(index % blocksInRow);
  const row = Math.floor(index / blocksInRow);
  return { x: col * width + col * margin, y: height * row + row * margin };
};

export const getColor = (i) => {
  const colors = [
    "linear-gradient(135deg, #f6d365 0%, #fda085 100%)",
    "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
    "linear-gradient(135deg, #5ee7df 0%, #b490ca 100%)",
    "linear-gradient(135deg, #c3cfe2 0%, #c3cfe2 100%)",
  ];

  return colors[i % 4];
};

export const shuffleArray = (originalArray) => {
  const array = originalArray.slice();
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
};

export const swapListItems = (workingArray, oldPosition, newPosition) => {
  let newOrder = [...workingArray];
  const [movingItem] = newOrder.splice(oldPosition, 1); // Remove the block being moved from old position.
  newOrder.splice(newPosition, 0, movingItem); // Insert the block being moved into its new position.
  return newOrder;
};
