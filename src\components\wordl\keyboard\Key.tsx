import classnames from "classnames";
import { ReactNode } from "react";

import { REVEAL_TIME_MS } from "@/wordl/constants/settings";
import { getStoredIsHighContrastMode } from "@/wordl/lib/localStorage";
import { CharStatus } from "@/wordl/lib/statuses";
import { solution } from "@/wordl/lib/words";

type Props = {
  children?: ReactNode;
  value: string;
  width?: number;
  status?: CharStatus;
  onClick: (value: string) => void;
  isRevealing?: boolean;
};

export const Key = ({
  children,
  status,
  width = 40,
  value,
  onClick,
  isRevealing,
}: Props) => {
  const keyDelayMs = REVEAL_TIME_MS * solution.length;
  const isHighContrast = getStoredIsHighContrastMode();

  const handleClick: React.MouseEventHandler<HTMLButtonElement> = (event) => {
    onClick(value);
    event.currentTarget.blur();
  };

  const styles = {
    transitionDelay: isRevealing ? `${keyDelayMs}ms` : "unset",
    width: `${width}px`,
  };

  const classes = classnames(
    "xxshort:w-8 xxshort:text-xxs xshort:w-10 flex items-center justify-center rounded mx-0.5 text-xs font-bold cursor-pointer select-none ",
    {
      "transition ease-in-out": isRevealing,
      "bg-[#4DAA9F] text-white hover:bg-slate-300 active:bg-[#4DAA9F]":
        !status,
      "bg-[#B2B5B7] text-white": status === "absent",
      "bg-orange-500 hover:bg-orange-600 active:bg-orange-700 text-white":
        status === "correct" && isHighContrast,
      "bg-cyan-500 hover:bg-cyan-600 active:bg-cyan-700 text-white":
        status === "present" && isHighContrast,
      "bg-[#ED7DA0] hover:bg-[#ED7DA0] active:bg-green-700 text-white":
        status === "correct" && !isHighContrast,
      "bg-[#B5DF9C] hover:bg-[#B5DF9C] active:bg-yellow-700 text-white":
        status === "present" && !isHighContrast,
      "h-8 sm:h-12": true, // Adjust the height for different breakpoints
    }
  );

  return (
    <button
      style={styles}
      aria-label={`${value}${status ? " " + status : ""}`}
      className={classes}
      onClick={handleClick}
    >
      {children || value}
    </button>
  );
};
