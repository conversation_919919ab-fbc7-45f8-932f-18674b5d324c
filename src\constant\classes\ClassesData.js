import clasesLineares from "@/../public/images/classes/classesInPerson.webp";
import experienciasLibres from "@/../public/images/classes/classesExperiences.webp";
import clasesOnline from "@/../public/images/classes/classesOnline.webp";
import onlineClub from "@/../public/images/classes/onlineClub.webp";

export const ClassesDataES = [
  {
    title: "Online Clubs",
    description: "Dynamic lessons with speaking practice and teacher feedback.",
    image: onlineClub.src,
    color: "rgba(168, 213, 186, 1)",
    buttonText: "Learn More",
    url: "classes/online-clubs",
  },
  {
    title: "Clases Lineares",
    description:
      "Aprende español con actividades prácticas y conversatorios interactivos. ¡Únete ya!",
    image: clasesLineares.src,
    color: "#FFE8C1",
    buttonText: "Learn More",
    url: "classes/clases-lineares",
  },
  {
    title: "Experiencias Libres",
    description:
      "Clases de baile, talleres de arte, y más. ¡Elige y aprende con tutores expertos!",
    image: experienciasLibres.src,
    color: "#BCEDEA",
    buttonText: "Learn More",
    url: "classes/experiencias-libres",
  },
  {
    title: "Clases Online",
    description:
      "Aprende español desde la comodidad de tu hogar. Clases interactivas y dinámicas con tutores expertos.",
    image: clasesOnline.src,
    color: "#FAD8C9",
    buttonText: "Learn More",
    url: "classes/online-clases",
  },
];

export const ClassesData = [
  {
    title: "Online Clubs",
    description: "Dynamic lessons with speaking practice and teacher feedback.",
    image: onlineClub.src,
    color: "rgba(168, 213, 186, 1)",
    buttonText: "Learn More",
    url: "classes/online-clubs",
  },
  {
    title: "In-Person Classes",
    description:
      "Learn Spanish quickly through games, practical activities, and conversations.",
    image: clasesLineares.src,
    color: "#FFE8C1",
    buttonText: "Learn More",
    url: "classes/clases-lineares",
  },
  {
    title: "Community Experiences",
    description: "Dance classes, art workshops, and more with expert tutors.",
    image: experienciasLibres.src,
    color: "#BCEDEA",
    buttonText: "Learn More",
    url: "classes/experiencias-libres",
  },
  {
    title: "Online Classes",
    description:
      "Classes custom-made for your interests, learning style, and schedule.",
    image: clasesOnline.src,
    color: "#FAD8C9",
    buttonText: "Learn More",
    url: "classes/online-clases",
  },
];
