/**
 * Generate an email template to notify stakeholder of new user sign ups.
 * 
 * @param name The user's first and lastname (if it applies)
 * @param email The user's email
 * @param date An optional datetime of when they signed up
 * 
 * @returns {string}
 */
export default function SignupNoticeEmail(name: string, email: string, date?: string) {
  const datetime = date || (new Date()).toLocaleString();

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>User Registration Alert</title>
        <style>
            body {
                font-family: 'Google Sans', Arial, sans-serif;
                line-height: 1.6;
                color: #202124;
                margin: 0;
                padding: 0;
                background-color: #f8f9fa;
            }
            table {
                border-collapse: collapse;
                mso-table-lspace: 0pt;
                mso-table-rspace: 0pt;
            }
            td {
                padding: 0;
            }
            .container {
                width: 100%;
                max-width: 600px;
                margin: 0 auto;
                background-color: #ffffff;
                border-radius: 8px;
            }
            .header {
                text-align: center;
                padding: 20px;
                border-bottom: 1px solid #dadce0;
            }
            .logo {
                max-height: 50px;
                margin-bottom: 15px;
            }
            h1 {
                color: rgb(2, 177, 153);
                font-size: 24px;
                font-weight: 500;
                margin: 0;
            }
            .content {
                padding: 20px;
            }
            .user-info-container {
                background-color: rgba(2, 177, 153, 0.1);
                border-radius: 8px;
                margin: 20px 0;
                border-left: 4px solid rgb(2, 177, 153);
            }
            .info-label {
                font-weight: 500;
                color: rgb(2, 177, 153);
                padding: 10px;
                width: 120px;
                vertical-align: top;
            }
            .info-value {
                padding: 10px;
                vertical-align: top;
            }
            .footer {
                text-align: center;
                color: #5f6368;
                font-size: 12px;
                padding: 20px;
                border-top: 1px solid #dadce0;
            }
            .highlight {
                font-weight: bold;
                color: rgb(2, 177, 153);
            }
            .button-container {
                text-align: center;
                padding: 15px 0;
            }
            .button {
                background-color: rgb(2, 177, 153);
                color: white;
                padding: 10px 24px;
                text-decoration: none;
                font-weight: 500;
                border-radius: 4px;
                display: inline-block;
            }
        </style>
    </head>
    <body style="margin: 0; padding: 0; background-color: #f8f9fa;">
        <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td align="center" valign="top" style="padding: 20px 0;">
                    <table class="container" cellpadding="0" cellspacing="0" border="0" width="600">
                        <tr>
                            <td class="header">
                                <img src="https://www.patitofeo.com/patitoB.png" alt="Patito Feo" class="logo" />
                                <h1>New User Registration Alert</h1>
                            </td>
                        </tr>
                        <tr>
                            <td class="content">
                                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                    <tr>
                                        <td style="padding-bottom: 15px;">
                                            Hello Team,
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding-bottom: 15px;">
                                            We have a <span class="highlight">new user registration</span> on our platform. Please find the details below:
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td>
                                            <table class="user-info-container" width="100%" cellpadding="0" cellspacing="0" border="0">
                                                <tr>
                                                    <td class="info-label">Name:</td>
                                                    <td class="info-value">${name}</td>
                                                </tr>
                                                <tr>
                                                    <td class="info-label">Email:</td>
                                                    <td class="info-value">${email}</td>
                                                </tr>
                                                <tr>
                                                    <td class="info-label">Registered:</td>
                                                    <td class="info-value">${datetime}</td>
                                                </tr>
                                                <tr>
                                                    <td class="info-label">App Environment:</td>
                                                    <td class="info-value">${process.env.NEXT_PUBLIC_ENVIRONMENT}</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px 0;">
                                            This user has successfully completed the registration process and their account has been created in our system.
                                        </td>
                                    </tr>
                                    
                                    <!-- <tr>
                                        <td class="button-container">
                                            <table cellpadding="0" cellspacing="0" border="0">
                                                <tr>
                                                    <td align="center" bgcolor="rgb(2, 177, 153)" style="border-radius: 4px;">
                                                        <a href="#" class="button" style="display: inline-block; color: white; text-decoration: none; padding: 10px 24px;">View User Details</a>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr> -->
                                </table>
                            </td>
                        </tr>
                        
                        <!-- Footer -->
                        <tr>
                            <td class="footer">
                                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                    <tr>
                                        <td align="center" style="padding-bottom: 5px;">
                                            This is an automated notification. Please do not reply to this email.
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center">
                                            &copy; 2025 Patito Feo LLC. All rights reserved.
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
  `;
}