import { COLOR, getEmailSkeleton } from "./utils";
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

type getCourseGiftBodyProps = {
  userName: string;
  senderName: string;
  description: string;
};
const getCourseGiftBody = ({
  userName,
  senderName,
  description,
}: getCourseGiftBodyProps) => {
  const body = `
    <table 
        align="center" 
        width="100%" 
        border="0" 
        cellPadding="0" 
        cellSpacing="0" 
        role="presentation" 
        style="padding:1rem"
      >
        <tbody>
            <tr>
                <td>
                    <h1 style="color:rgb(0,0,0);font-size:24px;font-weight:400;text-align:center;padding:0px;margin-top:30px;margin-bottom:30px;margin-left:0px;margin-right:0px">
                        <strong>Dear <!-- -->${
                          userName ?? "user"
                        }<!-- --> ,</strong>
                    </h1>
                    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:1rem">
                        <tbody>
                            <tr>
                                <td>
                                    <p 
                                        style="color:rgb(0,0,0);font-size:14px;font-weight:400;line-height:24px;margin:16px 0">
                                            🐥 Surprise! 🐥 ${senderName} has sent you a special gift from Patito Feo! We’re thrilled to bring a little smile to your day with our unique offerings. Before you dive in, here’s a peek at what’s been sent your way:
                                    </p>
                                    <p 
                                        style="color:rgb(0,0,0);font-size:14px;font-weight:400;line-height:24px;margin:16px 0">

                                        ${description}. It’s sure to inspire and delight as you embark on this new journey.

                                    </p>
                                    <hr 
                                        style="margin-top:16px;margin-bottom:16px;border-top-width:2px;border-color:rgb(209,213,219);width:100%;border:none;border-top:1px solid #eaeaea" 
                                    />
                                    <p 
                                        style="color:rgb(0,0,0);font-size:14px;font-weight:400;line-height:24px;margin:16px 0"
                                    >
                                        Your Next Step To access your gift, simply [signup / register / create an account] using the link below:<!-- --> <a href="${BASE_URL}/sign-up" style="color:${COLOR};text-decoration:none" target="_blank"> 👉 Sign Up Here</a> (If you already have an account, just log in to enjoy your gift!)
                                    </p>
                                    <p 
                                        style="color:rgb(0,0,0);font-size:14px;font-weight:400;line-height:24px;margin:16px 0"
                                    >
                                        We’re excited to have you join our Patito Feo family. Feel free to reach out if you need any assistance.
                                    </p>
                                    <p 
                                        style="color:rgb(0,0,0);font-size:14px;font-weight:400;line-height:24px;margin:16px 0"
                                    >
                                        Warm regards,
                                    </p>
                                    <p 
                                        style="color:rgb(0,0,0);font-size:14px;font-weight:400;line-height:24px;margin:16px 0"
                                    >
                                        The Patito Feo Team
                                    </p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
  `;

  return getEmailSkeleton({ body });
};

export { getCourseGiftBody };
