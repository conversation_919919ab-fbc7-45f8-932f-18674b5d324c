import { getF<PERSON><PERSON>mount, getPriceSymbol } from "@/utils/classes";
import { COLOR, getEmailSkeleton } from "./utils";
import { CURRENCY_ENUM } from "../Enums";

const contact = process.env.NEXT_PUBLIC_WHATSAPP_PHONE;
const emailId = process.env.SENDER_EMAIL;
const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
const signinUrl = `${baseUrl}/sign-in`;

const phoneWithPlus = `+${contact}`;
const telephone = `tel:${contact}`;

const COMMON_FOOTER = `
 <p
    class="m-0 mt-0 mb-0  leading-[24px] text-gray-500"
    style="font-size:0.75rem;line-height:24px;margin-bottom:16px;margin-top:16px;margin:0px">
                          Support: If you have any questions or need assistance,
                          please contact our support team at <a
                            href="mailto:${emailId}"
                            style="color:${COLOR};text-decoration-line:none"
                            target="_blank"
                            >${emailId}</a
                          > or call us at<a
                            href="tel:${phoneWithPlus}"
                            style="color:${COLOR};text-decoration-line:none"
                            target="_blank"
                            > ${phoneWithPlus}</a
                          >
                        </p>
`;

const SUCCESS_FOOTER = `
    <p
                      style="font-size:0.79rem;line-height:24px;margin-bottom:10px;margin-top:16px;color:#000;text-align:left;font-weight:600">
                      Next Steps:
                    </p>
                    <ol style="font-size:0.75rem">
                      <li>
                        <p
                          class="m-0 mt-0 mb-0 leading-[24px] text-gray-500"
                          style="font-size:0.75rem;line-height:24px;margin-bottom:16px;margin-top:16px;margin:0px">
                          Access Your Account: Log in to your account <a
                            href="${signinUrl}"
                            style="color:${COLOR};text-decoration-line:none"
                            target="_blank"
                            >here.</a
                          > To manage your service and access exclusive
                          resources.
                        </p>
                      </li>
                      <li>
                        <p
                          class="m-0 mt-0 mb-0  leading-[24px] text-gray-500"
                          style="font-size:0.75rem;line-height:24px;margin-bottom:16px;margin-top:16px;margin:0px">
                          Support: If you have any questions or need assistance,
                          please contact our support team at <a
                            href="mailto:${emailId}"
                            style="color:${COLOR};text-decoration-line:none"
                            target="_blank"
                            >${emailId}</a
                          > or call us at<a
                            href="tel:${phoneWithPlus}"
                            style="color:${COLOR};text-decoration-line:none"
                            target="_blank"
                            > ${phoneWithPlus}</a
                          >
                        </p>
                      </li>
                    </ol>

`;

const ERROR_FOOTER = `
    <p
              class="m-0 mt-0 mb-0 leading-[24px] text-gray-500"
              style="font-size:0.75rem;line-height:24px;margin-bottom:16px;margin-top:16px;margin:0px">
              If you need assistance or have any questions, feel free to contact
              our support team at <a
                href="mailto:${emailId}"
                style="color:${COLOR};text-decoration-line:none"
                target="_blank">
                ${emailId}
                </a> or call us at <a
                href="tel:${phoneWithPlus}"
                style="color:${COLOR};text-decoration-line:none"
                target="_blank"
              > ${phoneWithPlus}</a>
            </p>
`;

type PaymentDetailsType = {
  amount: number;
  title: string;
  type: string;
  currency: CURRENCY_ENUM;
};

type getPaymentEmailBodyProps = {
  isError: boolean;
  isSubscription: boolean;
  name: string;
  receiptUrl: string;
  transactionId: string;
  paymentDetails: PaymentDetailsType[];
  totalAmount: number;
};
const getPaymentEmailBody = ({
  isError,
  name,
  receiptUrl,
  transactionId,
  paymentDetails,
  totalAmount,
  isSubscription,
}: getPaymentEmailBodyProps) => {
  const info = (() => {
    if (isSubscription) {
      if (isError) {
        return `We regret to inform you that your recent subscription payment attempt was unsuccessful. Please try again or contact support. Below are the specifics of your subscription:`;
      } else {
        return "Thank you for subscribing to our service! Your subscription is now active. Below are the specifics of your subscription and the next steps to get you started.";
      }
    } else {
      if (isError) {
        return `We regret to inform you that your recent payment attempt was unsuccessful. Please try again or contact support. Below are the specifics of each service:`;
      } else {
        return `Thank you for choosing Patito Feo! We&#x27;re excited to confirm your recent service purchases. Below are the specifics of each service and the next steps to get you started.`;
      }
    }
  })();

  const body = `
   <table 
      align="center" 
      width="100%" 
      border="0" 
      cellPadding="0" 
      cellSpacing="0" 
      role="presentation" 
      style="padding:1rem"
    >
    <tbody>
        <tr style="width:100%">
          <td>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="padding:0 48px">
              <tbody>
                <tr>
                  <td>
                    <p
                      style="font-size:16px;line-height:24px;margin-bottom:16px;margin-top:16px;color:#000;text-align:center;font-weight:700">
                      Hi ${name}
                    </p>
                    <p
                      style="font-size:16px;line-height:24px;margin-bottom:16px;margin-top:16px;color:#000;text-align:left">
                        ${info}
                    </p>
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="border:1px solid rgba(238, 238, 238, 1);border-radius:10px;padding:10px">
                      <tbody>
                        <tr>
                          <td>
                            <p
                              style="font-size:0.9rem;line-height:24px;margin-bottom:16px;margin-top:0;color:#000;text-align:left;font-weight:600">
${isSubscription ? "Subscription" : "Services"} Details
                            </p>
   
                            ${paymentDetails
                              .map((m) =>
                                getPaymentRow({
                                  classType: m.type,
                                  classTitle: m.title,
                                  amount: m.amount,
                                  currency: m.currency,
                                })
                              )
                              .join("")}

                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="border-top:1px solid rgba(238, 238, 238, 1);padding-top:10px">
                              <tbody style="width:100%">
                                <tr style="width:100%">
                                  <td data-id="__react-email-column">
                                    <p
                                      style="font-size:0.8rem;line-height:24px;margin-bottom:0;margin-top:0;color:#000;text-align:left;font-weight:600">
                                      Total Amount
                                    </p>
                                  </td>
                                  <td data-id="__react-email-column">
                                    <p
                                      style="font-size:0.8rem;line-height:24px;margin-bottom:0;margin-top:0;color:#000;text-align:end;font-weight:600">
                                      ${getPriceSymbol({
                                        currency: paymentDetails[0].currency,
                                      })}${getFinalAmount(totalAmount)}
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <p
                      style="font-size:0.7rem;line-height:24px;margin-bottom:16px;margin-top:16px;color:rgba(109, 109, 109, 1);text-align:left">
                      <b style="color:#000">Transaction ID</b>  ${transactionId}
                    </p>
${
  isError
    ? ""
    : `
<p
                      style="font-size:0.7rem;line-height:24px;margin-bottom:16px;margin-top:16px;color:#000;text-align:left">
                      You can download your receipt<!-- -->
                      <a
                        href="${receiptUrl}"
                        style="color:${COLOR};text-decoration-line:none"
                        target="_blank"
                        >here.</a
                      >
                    </p>

`
}
                    
                
                    ${isError ? ERROR_FOOTER : SUCCESS_FOOTER}
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  `;
  return getEmailSkeleton({ name: "", body });
};

const getPaymentRow = ({ classType, classTitle, amount, currency }) => {
  return `
     <table
        align="center"
        width="100%"
        border="0"
        cellpadding="0"
        cellspacing="0"
        role="presentation"
        style="margin-bottom:10px">
        <tbody>
          <tr>
            <td>
              <table
                align="center"
                width="100%"
                border="0"
                cellpadding="0"
                cellspacing="0"
                role="presentation">
                <tbody style="width:100%">
                  <tr style="width:100%">
                    <td data-id="__react-email-column">
                      <p
                        style="font-size:0.78rem;line-height:24px;margin-bottom:0;margin-top:0;color:#000;text-align:left;font-weight:500">
                        ${classType}
                      </p>
                    </td>
                    <td data-id="__react-email-column">
                      <p
                        style="font-size:0.78rem;line-height:24px;margin-bottom:0;margin-top:0;color:#000;text-align:end;font-weight:600">
                         ${getPriceSymbol({ currency })}${amount}
                      </p>
                    </td>
                  </tr>
                </tbody>
              </table>
              <table
                align="center"
                width="100%"
                border="0"
                cellpadding="0"
                cellspacing="0"
                role="presentation">
                <tbody style="width:100%">
                  <tr style="width:100%">
                    <p
                      style="font-size:0.72rem;line-height:24px;margin-bottom:0;margin-top:0;color:rgba(163, 163, 163, 1);text-align:left;font-weight:600">
                        ${classTitle}
                    </p>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
        </tbody>
      </table>
    `;
};

export { getPaymentEmailBody };
