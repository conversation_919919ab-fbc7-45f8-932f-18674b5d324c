import { createContext, useContext, useEffect, useState } from "react";
import { useUserContext } from "./UserContext";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { getLocalCarts } from "@/utils/classes";

const CartCountContext = createContext({
  count: 0,
  isFetching: true,
  addOneCart: () => {},
  removeOneCart: () => {},
});

export const CartCountProvider = ({ children }) => {
  const [isFetching, setIsFetching] = useState(true);
  const [count, setCount] = useState(0);
  const { dbUser } = useUserContext();
  const isLoggedIn = !!dbUser;
  const { showSnackbar } = useSnackbar();

  const getLocalCartCount = () => {
    const cartArray = getLocalCarts();
    const cartArrayLength = cartArray.length;

    const val = localStorage.getItem("CART_COUNT");
    const final = val ? +val : 0;

    if (+final > +cartArrayLength) {
      return final;
    } else {
      return cartArrayLength;
    }
  };

  const getCartCount = async () => {
    try {
      setIsFetching(true);
      const { data: respData } = await axiosInstance.get("cart/count");
      const { data, success } = respData;
      if (success) {
        const apiCount = data;
        const localCount = getLocalCartCount();
        const totalCount = +data + +localCount;
        console.log({ totalCount, apiCount, localCount });
        setCount(totalCount);
        localStorage.setItem("CART_COUNT", data);
      } else {
        setCount(0);
        localStorage.setItem("CART_COUNT", String(0));
      }
      setIsFetching(false);
    } catch (error) {
      setCount(0);
      setIsFetching(false);
      showSnackbar("Failed to get count of the cart", {
        type: "error",
      });
    }
  };

  useEffect(() => {
    if (isLoggedIn) {
      getCartCount();
    } else {
      setCount(getLocalCartCount());
    }
  }, [isLoggedIn]);

  const addOneCart = () => {
    setCount((prev) => prev + 1);
  };

  const removeOneCart = () => {
    setCount((prev) => prev - 1);
  };

  const value = { count, isFetching, addOneCart, removeOneCart };

  return (
    <CartCountContext.Provider value={value}>
      {children}
    </CartCountContext.Provider>
  );
};

export const useCartCountContext = () => useContext(CartCountContext);
