import React, { createContext, useState, ReactNode } from "react";
import Snackbar from "@mui/material/Snackbar";
import { Alert, AlertColor } from "@mui/material";

interface SnackbarContextType {
  showSnackbar: (
    message: string,
    options?: {
      vertical?: "top" | "bottom";
      horizontal?: "center" | "left" | "right";
      type?: AlertColor;
    },
    duration?: number
  ) => void;
}

export const SnackbarContext = createContext<SnackbarContextType | undefined>(
  undefined
);

export const SnackbarProvider = ({ children }: { children: ReactNode }) => {
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    vertical: "top" | "bottom";
    horizontal: "center" | "left" | "right";
    type?: AlertColor;
    duration?: Number;
  }>({
    open: false,
    message: "",
    vertical: "top",
    horizontal: "center",
    type: "info",
    duration: 3000,
  });

  const showSnackbar = (
    message: string,
    options: {
      vertical?: "top";
      horizontal?: "center" | "left" | "right";
      type?: "info";
    } = {},
    duration?: number
  ) => {
    setSnackbar({
      open: true,
      message,
      vertical: options.vertical || "top",
      horizontal: options.horizontal || "right",
      type: options.type || "info",
      duration: duration ?? 3000,
    });
  };

  const handleClose = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  return (
    <SnackbarContext.Provider value={{ showSnackbar }}>
      {children}
      <Snackbar
        anchorOrigin={{
          vertical: snackbar.vertical,
          horizontal: snackbar.horizontal,
        }}
        open={snackbar.open}
        onClose={handleClose}
        message={snackbar.message}
        autoHideDuration={snackbar.duration ? +snackbar.duration : 3000}
      >
        <Alert
          onClose={handleClose}
          severity={snackbar.type}
          variant="filled"
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </SnackbarContext.Provider>
  );
};
