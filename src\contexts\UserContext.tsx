import React, { createContext, useContext, useEffect, useState } from "react";
import { useUser } from "@clerk/nextjs";
import axios from "axios";
import { useSnackbar } from "@/hooks/useSnackbar";
import { useRouter } from "next/router";
import __lang from "@/constant/translations";
import { handleReturnToCart, needToRedirect } from "@/utils/classes";
import { Maybe } from "@/types";
import { UserType } from "@/api/mongoTypes";

const UserContext = createContext({
  preferredLanguage: `en`,
  isLoaded: false,
  dbUser: null,
  isfetching: false,
  isRetrying: false,
  user: null,
  isProfileCompeleted: true,
  translate: (k: string) => {},
  setPreferredLanguage: (k: string) => {},
  setUserInfo: (user: Maybe<UserType>) => {},
  setProfileCompleted: (status: boolean) => {},
});

export const UserProvider = ({ children }) => {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const [isfetching, setIsfetching] = useState(true);
  const [isRetrying, setIsRetrying] = useState(true);
  const [isProfileCompeleted, setProfileCompleted] = useState(true);
  const [userInfo, setUserInfo] = useState<Maybe<UserType>>(null);
  const [preferredLanguage, setLanguageOption] = useState("en");

  const { isLoaded, user } = useUser();

  const fetchUserInfo = async (retryCount = 0) => {
    try {
      const { data } = await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/user/get-user`,
        {
          clerkId: user.id,
        }
      );

      if (data?.data) {
        setUserInfo(data.data);
        localStorage.setItem("user", JSON.stringify(data.data));
        const isProfileCompeleted = data?.data?.profileCreated;
        const completeItLater = data?.data?.completeLater;

        const profileCompleted =
          completeItLater === false && isProfileCompeleted;

        setProfileCompleted(profileCompleted);

        if (completeItLater || isProfileCompeleted) {
          const open = needToRedirect();
          if (open) {
            const wasRedirected = localStorage.getItem("was_redirected");
            if (!wasRedirected) {
              localStorage.setItem("was_redirected", "true");
              handleReturnToCart(router);
              showSnackbar("Please complete your transaction", {
                type: "info",
              });
            }
          }
        }

        setIsRetrying(false);
        setIsfetching(false);
      } else if (retryCount < 3) {
        setIsRetrying(true);
        setTimeout(() => fetchUserInfo(retryCount + 1), 2000);
      } else {
        setIsRetrying(false);
        setIsfetching(false);
      }
    } catch (error) {
      if (retryCount < 3) {
        setIsRetrying(true);
        setTimeout(() => fetchUserInfo(retryCount + 1), 2000);
      } else {
        setIsRetrying(false);
        setIsfetching(false);
      }
    }
  };

  const handleClearStorage = () => {
    // localStorage.removeItem("CART");
    // localStorage.removeItem("BUY_CLASS");
    // localStorage.removeItem("BUY_EVENT");
    localStorage.removeItem("CART_COUNT");
    localStorage.removeItem("user");
    localStorage.removeItem("was_redirected");
  };

  /**
   * Get the persisted language option selected by the user.
   * @returns {String}
   */
  const getPreferredLanguage = () => {
    return localStorage.getItem(`LANGUAGE_OPTION`) || "en";
  };

  /**
   * Update and persist user selected language to the localStorage. This enables us
   * to pick their right language selection in the next visit.
   *
   * @param {String} language
   */
  const setPreferredLanguage = (language = `en`) => {
    setLanguageOption(language);
    localStorage.setItem(`LANGUAGE_OPTION`, language);
  };

  useEffect(() => {
    if (user?.id) {
      fetchUserInfo();
    } else {
      setIsfetching(false);
      setUserInfo(null);
      handleClearStorage();
    }
  }, [user]);

  useEffect(() => {
    setPreferredLanguage(getPreferredLanguage());
  }, []);

  /**
   * Get translation available for the preferred language.
   *
   * @param {String} key translation mapKey
   * @returns {String}
   */
  const translate = (key: string) => __lang(key, preferredLanguage);

  const value = {
    isLoaded,
    dbUser: userInfo,
    isfetching,
    isRetrying,
    user,
    isProfileCompeleted,
    translate,
    preferredLanguage,
    setPreferredLanguage,
    setProfileCompleted,
    setUserInfo,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

export const useUserContext = () => useContext(UserContext);
