import { useState } from "react";
import { useSnackbar } from "./useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { useRouter } from "next/router";
import { useUserContext } from "@/contexts/UserContext";
import { localPlanType, Maybe, MaybeEmptytArray, MaybeObjectId } from "@/types";
import {
  ClassesPricingType,
  ClubType,
  EventOnType,
  MembershipType,
} from "@/api/mongoTypes";
import { getLocalUser } from "@/utils/common";
import { CURRENCY_ENUM } from "@/constant/Enums";

type productDataType = {
  name: string;
  description?: string;
  images?: string[];
  price?: number;
};

type makePaymentProps = {
  cartId: MaybeEmptytArray<string>;
  eventId: MaybeEmptytArray<string>;
  productData: productDataType[];
  price: number;
  clubsDetails:
    | {
        clubInfo: string;
        memberships: MembershipType;
      }[]
    | [];
  classesDetails:
    | {
        classInfo: ClassesPricingType;
        plans: localPlanType[];
      }[]
    | [];
  eventsPriceDetails:
    | {
        eventInfo: string;
        price: number;
        currency: CURRENCY_ENUM;
        eventOn: string;
      }[]
    | [];
  currency: CURRENCY_ENUM;
};

const usePayment = () => {
  const { dbUser } = useUserContext();
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const [isMaking, setIsMaking] = useState(false);
  const [url, seturl] = useState(false);
  const [isError, setIsError] = useState(false);
  const userDetails = getLocalUser();
  const email = userDetails?.email ?? dbUser?.email;
  const [isSubscribing, setIsSubscribing] = useState(false);

  const makePayment = async ({
    cartId = [],
    eventId = [],
    classesDetails = [],
    clubsDetails = [],
    price,
    productData = [],
    eventsPriceDetails = [],
    currency,
  }: makePaymentProps) => {
    try {
      setIsMaking(true);
      const { data } = await axiosInstance.post("payment/initiate", {
        cartId,
        emailId: email,
        price,
        productData,
        eventId,
        classesDetails,
        clubsDetails,
        eventsPriceDetails,
        currency,
      });
      const paymentUrl = data.data.paymentUrl;
      const transactionId = data?.data?.transactionDetails?._id;
      if (paymentUrl) {
        localStorage.setItem("lastTransactionId", transactionId);
        window.history.pushState({}, "", `/payment/${transactionId}`);
        router.push(paymentUrl);
        seturl(paymentUrl);
        setIsError(false);
      } else {
        setIsError(true);
        showSnackbar("Failed to Make payment.Please try again", {
          type: "error",
        });
      }
      setIsMaking(false);
    } catch (error) {
      setIsMaking(false);
      console.error(`Something went wrong in makePayment due to`, error);
      showSnackbar("Failed to Make payment.Please try again", {
        type: "error",
      });
    }
  };

  const subscribeClub = async ({ clubId, memberships }) => {
    try {
      setIsSubscribing(true);
      const { data } = await axiosInstance.post("payment/subscribe-club", {
        emailId: email,
        clubId,
        memberships,
      });
      const paymentUrl = data.data.paymentUrl;
      if (paymentUrl) {
        router.push(paymentUrl);
        seturl(paymentUrl);
        setIsError(false);
      } else {
        setIsError(true);
        showSnackbar("Failed to subscribe the club.Please try again", {
          type: "error",
        });
      }
      setIsMaking(false);
      setIsSubscribing(false);
    } catch (error) {
      setIsSubscribing(false);
      showSnackbar("Failed to subscribe the club.Please try again", {
        type: "error",
      });
    }
  };

  return {
    makePayment,
    isMaking,
    url,
    isError,
    subscribeClub,
    isSubscribing,
  };
};

export default usePayment;
