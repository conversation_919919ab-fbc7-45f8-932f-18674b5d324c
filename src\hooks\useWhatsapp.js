import { useState, useCallback } from "react";

export const useWhatsApp = (phoneNumber, defaultMessage) => {
  const [error, setError] = useState(null);

  const openWhatsAppChat = useCallback(() => {
    try {
      const encodedMessage = encodeURIComponent(defaultMessage);
      const url = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
      window.open(url, "_blank");
      setError(null); // Reset error state if the operation was successful
    } catch (err) {
      setError(err.message); // Update error state if an error occurs
    }
  }, [phoneNumber, defaultMessage]);

  return { openWhatsAppChat, error };
};
