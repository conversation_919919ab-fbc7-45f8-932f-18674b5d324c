import { withIronSessionApiRoute, withIronSessionSsr } from "iron-session/next";

const sessionOptions = {
  cookieName: "patitofeo__session",
  password: process.env.COOKIE_SECRET,
  cookieOptions: {
    secure: process.env.NODE_ENV === "production",
  },
};

export function withSessionSSR(handler) {
  return withIronSessionSsr(handler, sessionOptions);
}

export function withSessionAPI(handler) {
  return withIronSessionApiRoute(handler, sessionOptions);
}
