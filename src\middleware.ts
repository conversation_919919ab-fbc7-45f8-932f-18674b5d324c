import {
  clerkClient,
  clerkMiddleware,
  createRouteMatcher,
} from "@clerk/nextjs/server";
import { ROLE_TYPES } from "./constant/Enums";

// Allow signed out users to access the specified routes:

const webhooks = ["/api/stripe/webhook", "/api/clerk-signup"];

const apiEndpoints = [
  "/api/user/(.*)",
  "/api/collection/(.*)",
  "/api/learning/(.*)",
  "/api/seed/insert/(.*)",
  "/api/dashboard/(.*)",
  "/api/getCategory",
  "/api/getLanguages",
  "/api/launch-waitlist",
  "/api/learning/(.*)",
  "/api/utils/(.*)",
  "/api/event/(.*)",
  "/api/schedule/(.*)",
  "/api/video/(.*)",
  "/api/club/all",
  "/api/club/get/(.*)",
  "/api/payment/(.*)",
  "/api/cart/(.*)",
  "/api/stripe/(.*)",
  "/api/scripts/(.*)",
  "/api/student/(.*)",
  "/api/classes/(.*)",

  // "/api/seed/insert/(.*)",
  // "/api/seed/upsert/(.*)",
  "/api/seed/replace-all/(.*)",
  // "/api/seed",
];

const publicRoutes = createRouteMatcher([
  ...webhooks,
  ...apiEndpoints,

  "/",
  "/about-us",
  "/about-us/(.*)",
  "/blog",
  "/cart",
  "/blog/(.*)",
  "/classes",
  "/classes/(.*)",
  "/evaluation/(.*)",
  "/events",
  "/forgot-password",
  "/games",
  "/games/(.*)",
  "/games/adivina-game",
  "/podcasts",
  "/profile",
  "/reset-password",
  "/sign-up(.*)",
  "/sign-in(.*)",
  "/upload-file",
  "/user/get-user",
  "/user/update",
  "/videos",
  "/videos/(.*)",
  "/waitlist",
]);

const adminRoutes = createRouteMatcher(["/create", "/create/(.*)"]);

export default clerkMiddleware(async (auth, req) => {
  let userId = null;
  if (!publicRoutes(req)) {
    const { userId: clerkId } = await auth.protect();
    userId = clerkId;
  }
  if (adminRoutes(req)) {
    let clerkId = null;
    if (userId) {
      clerkId = userId;
    } else {
      const { userId } = await auth.protect();
      clerkId = userId;
    }
    try {
      if (clerkId) {
        const clerk = await clerkClient();
        const user = await clerk.users.getUser(clerkId);

        if (user) {
          const patitoEmailAddress = user?.emailAddresses
            ?.map((f) => f.emailAddress)
            .filter((f) => f.includes("@patitofeo.com"));
          console.info(
            `userEmailAddress`,
            JSON.stringify(user?.emailAddresses)
          );
          console.info(
            `patitoEmailAddress`,
            JSON.stringify(patitoEmailAddress)
          );
          if (patitoEmailAddress.length > 0) {
            console.log(
              "its a user with patito email so we are allowing him to access",
              JSON.stringify(user)
            );
          } else {
            const roles = (() => {
              const role = user.publicMetadata.role;
              if (typeof role === "string") {
                const rolesArray = role.split(",");
                return rolesArray;
              }
              return [];
            })();

            const isTeacher = roles.includes(ROLE_TYPES.TEAHCER);
            const isStudent = roles.includes(ROLE_TYPES.STUDENT);
            const isAdmin = roles.includes(ROLE_TYPES.ADMIN);

            if (isAdmin) {
              console.log("allowing user to access /create route");
            } else {
              const isStudentRoute = req?.url?.includes(
                "/create/student-dashboard"
              );
              if (isTeacher) {
                // if the role is teacher we allow teacher to visit the student dashboard
                if (!isStudentRoute) {
                  return new Response("Unauthorized", {
                    status: 403,
                    statusText: "Forbidden",
                  });
                }
              } else {
                return new Response("Unauthorized", {
                  status: 403,
                  statusText: "Forbidden",
                });
              }
            }
          }
        } else {
          console.warn(
            "user with clerkId doesnt exists hence returning unauthorized clerkId",
            clerkId
          );
          return new Response("Unauthorized", {
            status: 403,
            statusText: "Forbidden",
          });
        }
      } else {
        console.warn(
          "clerkId doesnt exists hence we are returnig unauthorized"
        );
        return new Response("Unauthorized", {
          status: 403,
          statusText: "Forbidden",
        });
      }
    } catch (error) {
      console.error(
        "Something went wrong while checking the clerk user role due to ",
        error
      );
      return new Response("Unauthorized", {
        status: 403,
        statusText: "Forbidden",
      });
    }
  }
});

export const config = {
  matcher: [
    // Exclude files with a "." followed by an extension, which are typically static files.
    // Exclude files in the _next directory, which are Next.js internals.
    "/((?!.+\\.[\\w]+$|_next).*)",
    // Re-include any files in the api or trpc folders that might have an extension
    "/(api|trpc)(.*)",
    // "/((?!.*\\..*|_next).*)",
  ],
};
