import '../styles/globals.css';
import 'react-phone-number-input/style.css';
import { <PERSON><PERSON>rov<PERSON> } from '@clerk/nextjs';
import { ThemeProvider } from '@mui/material';
import { CssBaseline } from '@mui/material/';
import { theme } from '../styles/theme';
import createEmotionCache from '../utils/createEmotionCache';
import { CacheProvider } from '@emotion/react';
import { UserProvider } from '../contexts/UserContext';
import Header from '../components/header/Header';
import Footer from '../components/Footer';
import WorkInProgress from '@/components/WIP/WorkInProgress';
import '../styles/fonts';
import './index.css';
import { SnackbarProvider } from '@/contexts/SnackbarContext';
import { <PERSON>, <PERSON>, <PERSON> } from 'next/font/google';
import { CartCountProvider } from '@/contexts/CartCountContext';

const antonio = Antonio({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700'],
  variable: '--font-antonio',
});

const barlow = Barlow({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  style: ['italic', 'normal'],
  variable: '--font-barlow',
});

const inter = Inter({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  variable: '--font-inter',
});

const clientSideEmotionCache = createEmotionCache();

function MyApp({
  Component,
  emotionCache = clientSideEmotionCache,
  pageProps,
}) {
  return (
    <ClerkProvider>
      <CacheProvider value={emotionCache}>
        <ThemeProvider theme={theme}>
          <SnackbarProvider>
            <UserProvider>
              <CartCountProvider>
                <CssBaseline />
                <main
                // className={`${antonio.variable} ${barlow.variable} ${inter.variable}`}
                >
                  <Header />
                  <Component {...pageProps} />
                </main>
                <Footer />
              </CartCountProvider>
            </UserProvider>
          </SnackbarProvider>
        </ThemeProvider>
      </CacheProvider>
    </ClerkProvider>
  );
}

export default MyApp;
