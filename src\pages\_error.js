import React from "react";
import PropTypes from "prop-types";

function Error({ statusCode }) {
  return (
    <p>
      {statusCode
        ? `An server error ${statusCode} occurred`
        : "An error occurred on client"}
    </p>
  );
}

Error.getInitialProps = ({ res, err }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
  return { statusCode };
  console.log("errrrrror", statusCode);
};

Error.propTypes = {
  statusCode: PropTypes.number,
};

export default Error;
