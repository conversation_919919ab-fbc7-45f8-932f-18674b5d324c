import AboutUsLayout from "@/components/Aboutus";
import StackedCard from "@/components/StackedCard";
import { WhoWeAreData } from "@/constant/about/WhoWeAreData";
import { Box, Grid, Typography } from "@mui/material";
import Image from "next/image";
import React from "react";

const AboutUs = () => {
  return (
    <>
      <AboutUsLayout>
        <Box>
          {WhoWeAreData.map((m, i) => (
            <Grid
              key={i}
              container
              spacing={3}
              flexDirection={i % 2 === 0 ? "row" : "row-reverse"}
              justifyContent="center"
              alignItems="center"
              mt={5}
            >
              <Grid item xs={12} lg={6} order={{ xs: 2, lg: 2 }}>
                <Box
                  display="flex"
                  flexDirection="row"
                  alignItems="center"
                  justifyContent="center"
                >
                  <StackedCard
                    isRight={i % 2 === 0}
                    sx={{
                      width: { xs: "310px", sm: "100%" },
                      height: { xs: "310px", sm: "350px" },
                      maxWidth: { xs: "480px" },
                      maxHeight: { xs: "352px" },
                    }}
                  >
                    <Image src={m.image} fill alt="" objectFit="cover" />
                  </StackedCard>
                </Box>
              </Grid>
              <Grid item xs={12} lg={6} order={{ xs: 1, lg: 1 }}>
                <Box
                  sx={{
                    maxWidth: {
                      xs: "300px",
                      sm: "400px",
                      md: "500px",
                    },
                    margin: "auto",
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{
                      textAlign: "left",
                      fontSize: { sm: "18px" },
                      fontWeight: { xs: "500", sm: "400" },
                      paddingBottom: { xs: "10px" },
                    }}
                  >
                    {m.description}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          ))}
        </Box>
      </AboutUsLayout>
    </>
  );
};

export default AboutUs;
