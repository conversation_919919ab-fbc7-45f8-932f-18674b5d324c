import AboutUsLayout from "@/components/Aboutus";
import OurCommunitySupport from "@/components/Aboutus/OurCommunitySupport";
import TeamUser from "@/components/Aboutus/TeamUser";
import { teamData } from "@/constant/about/Team";
import useWindowDimensions from "@/hooks/useWindowDimension";
import { Box, Grid } from "@mui/material";
import React, { useMemo } from "react";

const Team = () => {
  const { width } = useWindowDimensions();

  const numberOfCoulmns = (() => {
    if (width > 0 && width < 600) {
      return 1;
    }
    if (width > 600 && width < 900) {
      return 2;
    }
    if (width > 900 && width < 1200) {
      return 4;
    }
    return 4;
  })();

  const heightOfTheCard = useMemo(() => {
    if (width > 0 && width < 600) {
      return 400;
    }
    return 300;
  }, [width]);

  return (
    <>
      <AboutUsLayout>
        <Grid
          container
          spacing={10}
          mt={1}
          sx={{
            position: "relative",
          }}
        >
          {teamData.map((m, i) => (
            <TeamUser
              index={i}
              data={m}
              key={i}
              numberOfCoulmns={numberOfCoulmns}
              heightOfTheCard={heightOfTheCard}
              width={width}
            />
          ))}
        </Grid>
      </AboutUsLayout>
      <OurCommunitySupport />
    </>
  );
};

export default Team;
