import AboutUsLayout from "@/components/Aboutus";
import { testimonials, TestimonialType } from "@/constant/about/Testimonials";
import { Box, Typography } from "@mui/material";
import React from "react";
import Marquee from "react-fast-marquee";

const Testimonials = () => {
  return (
    <AboutUsLayout>
      <Marquee>
        <Box my={10} sx={{ overflow: "hidden", width: "100%" }}>
          {testimonials.map((item, index) => (
            <TestimonialCard key={`testimonial-${index}`} data={item} />
          ))}
        </Box>
      </Marquee>
    </AboutUsLayout>
  );
};

export default Testimonials;

type TestimonialCardProps = {
  data: TestimonialType;
};

const TestimonialCard: React.FC<TestimonialCardProps> = ({ data }) => {
  return (
    <Box
      sx={{
        flex: "0 0 auto",
        width: 250,
        marginRight: {
          xs: 2,
          md: 8,
        },
        display: "inline-block",
        background: "rgba(241, 249, 249, 1)",
        borderRadius: 2,
        p: 2,
      }}
    >
      <Typography
        textAlign="left"
        color="rgba(109, 109, 109, 1)"
        fontSize="0.85rem"
      >
        &quot;{data.review}&quot;
      </Typography>
      <Typography mt={3} textAlign="left" fontWeight="800" fontSize="0.9rem">
        {data.name}
      </Typography>
      {/* <Typography
        textAlign="left"
        color="rgba(100, 116, 139, 1)"
        fontSize="0.75rem"
      >
        {data.profession}
      </Typography> */}
    </Box>
  );
};
