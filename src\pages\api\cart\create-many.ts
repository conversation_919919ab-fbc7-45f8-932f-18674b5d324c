import { Cart } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const payload = req.body;
      const userId = req.headers.userid;

      const input = payload.map((m) => {
        if (!m?.eventId) {
          delete m?.payload?.eventId;
        }
        if (!m?.classesId) {
          delete m?.classesId;
        }
        if (m?.plans?.length === 0) {
          delete m?.plans;
        }
        return {
          ...m,
          userId,
        };
      });

      const createdCart = await Cart.insertMany(input);
      if (createdCart) {
        return res.status(201).json({
          data: createdCart,
          message: "Added to cart successfully",
          success: true,
        });
      }
      res.status(400).json({
        data: null,
        message: "Failed to add it into cart",
        success: false,
      });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in cart/create due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
