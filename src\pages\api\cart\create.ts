import { Cart } from "@/api/mongo";
import { CartType } from "@/api/mongoTypes";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { classesId, plans, eventId, clubId, memberships, eventOn } =
        req.body;
      const userId = req.headers.userid;

      const payload = {
        classesId,
        plans,
        userId,
        eventId,
        clubId,
        memberships,
        eventOn,
      };

      if (!eventOn) {
        delete payload.eventOn;
      }
      if (!clubId) {
        delete payload.clubId;
      }
      if (!eventId) {
        delete payload.eventId;
      }
      if (!classesId) {
        delete payload.classesId;
      }
      if (plans?.length === 0) {
        delete payload.plans;
      }
      if (memberships?.length === 0) {
        delete payload.memberships;
      }

      const createdCart = await Cart.create(payload);
      if (createdCart) {
        return res.status(201).json({
          data: createdCart,
          message: "Added to cart successfully",
          success: true,
        });
      }
      res.status(400).json({
        data: null,
        message: "Failed to add it into cart",
        success: false,
      });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in cart/create due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
