import { Club } from "@/api/mongo";
import { getAllClubDetails } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const { skip = 0, limit = 10, needClubImage = false, search } = req.query;
      const userId = req.headers.userid;
      const payload = {
        title: { $regex: search, $options: "i" },
      };
      if (!search) {
        delete payload.title;
      }
      let clubs = await Club.find(payload)
        .skip(+skip)
        .limit(+limit)
        .populate("proficiencyLevel")
        .populate({
          path: "teachers",
          select:
            "firstName lastName profileImageKey profileImageId profileImageUrl",
        })
        .sort({ createdAt: -1 });

      if (needClubImage) {
        clubs = (await Promise.all(
          clubs.map(async (m) =>
            getAllClubDetails({
              data: m,
              needClubImage: true,
              needCreatorImage: true,
            })
          )
        )) as any;
      }

      if (clubs) {
        res.status(200).json({
          message: "Clubs fetched successfully",
          data: clubs,
          success: true,
        });
      } else {
        res.status(404).json({
          message: "Something went wrong while fetching clubs",
          data: null,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in club/all due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
