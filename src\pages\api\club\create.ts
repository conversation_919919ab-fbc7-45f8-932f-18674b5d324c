import { Club } from "@/api/mongo";
import { createClubPriceAndProduct } from "@/api/mongoHelpers";
import { stripe } from "@/api/stripe";
import { AMOUNT_CONVERTOR } from "@/utils/classes";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const {
        title,
        about,
        price,
        theme,
        proficiencyLevel,
        categories,
        highlights,
        imagesKeysAndIds,
        teachers,
        currency,
        targetLanguage
      } = req.body;

      const createClub = await Club.create({
        title,
        about,
        price,
        theme,
        proficiencyLevel,
        categories,
        highlights,
        imagesKeysAndIds,
        teachers,
        currency,
        targetLanguage
      });

      if (createClub) {
        const updatedClub = await createClubPriceAndProduct({
          clubDetails: createClub,
        });

        res.status(201).json({
          data: updatedClub,
          message: "Club created successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: "Something went wrong while creating Club",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in club/create due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
