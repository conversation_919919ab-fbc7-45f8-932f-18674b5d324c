import { VideoCollection } from "@/api/mongo";
import { getAllCollectionDetails } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const {
        skip = 0,
        limit = 10,
        needCoverImages = false,
        needLikeStatus = false,
        needProgress = false,
      } = req.query;
      const userId = req.headers.userid;
      let collections = await VideoCollection.find().skip(+skip).limit(+limit);

      collections = await Promise.all(
        collections.map(async (m) => {
          const collectionInfo = await getAllCollectionDetails({
            data: m,
            userId: String(userId),
            needCoverImages: <PERSON><PERSON><PERSON>(needCoverImages),
            needLikeStatus: <PERSON><PERSON><PERSON>(needLikeStatus),
            needProgress: <PERSON><PERSON><PERSON>(needProgress),
          });
          return collectionInfo;
        })
      );

      if (collections) {
        res.status(200).json({
          message: "Collections fetched successfully",
          data: collections,
          success: true,
        });
      } else {
        res.status(404).json({
          message: "Something went wrong while fetching collections",
          data: null,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in get-user due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
