import { VideoCollection } from "@/api/mongo";
import { getAllCollectionDetails } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  const {
    id,
    needCoverImages = false,
    needLikeStatus = false,
    needProgress = false,
  } = req.query;
  const userId = req.headers.userid;

  if (!id) {
    res.status(400).json({
      data: null,
      message: "id cannot be empty",
      success: false,
    });
    return;
  }
  try {
    if (req.method === "GET") {
      const singleCollection = await VideoCollection.findById(id);
      if (singleCollection) {
        const collectionInfo = await getAllCollectionDetails({
          data: singleCollection,
          userId: String(userId),
          needCoverImages: <PERSON><PERSON><PERSON>(needCoverImages),
          needLikeStatus: <PERSON><PERSON><PERSON>(needLikeStatus),
          needProgress: <PERSON><PERSON><PERSON>(needProgress),
        });
        res.status(200).json({
          data: collectionInfo,
          message: "fetched collection successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: `Something went wrong while fetching collection using id ${id}`,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in collection/get/${id} due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message ?? "Something went wrong",
      success: false,
    });
  }
}
