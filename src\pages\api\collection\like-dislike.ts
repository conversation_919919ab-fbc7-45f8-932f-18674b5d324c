import { VideoCollection, VideoCollectionLikesMap } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { isLiked = false, id } = req.body;
      const likedBy = req.headers.userid;
      if (isLiked) {
        const likedCollection = await VideoCollectionLikesMap.create({
          likedBy,
          collectionId: id,
        });
        if (likedCollection) {
          handleLikedCount({
            collectionId: id,
            isLiked,
          });
          res.status(200).json({
            data: likedCollection,
            message: "liked collection successfully",
            success: true,
          });
        } else {
          res.status(400).json({
            data: null,
            message: "Something went wrong while liking collection",
            success: false,
          });
        }
      } else {
        const likedCollection = await VideoCollectionLikesMap.findOneAndDelete({
          likedBy,
          collectionId: id,
        });
        if (likedCollection) {
          handleLikedCount({
            collectionId: id,
            isLiked,
          });
          res.status(200).json({
            data: likedCollection,
            message: "disliked collection successfully",
            success: true,
          });
        } else {
          res.status(400).json({
            data: null,
            message: "Something went wrong while disliking collection",
            success: false,
          });
        }
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in /api/collection/like-dislike due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

type handleLikedCountProps = {
  collectionId: String;
  isLiked: Boolean;
};
const handleLikedCount = async ({
  collectionId,
  isLiked = false,
}: handleLikedCountProps) => {
  const updatedCollection = await VideoCollection.findByIdAndUpdate(
    collectionId,
    { $inc: { likedCount: isLiked ? 1 : -1 } }
  );
  return updatedCollection;
};
