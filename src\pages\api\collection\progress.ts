import { VideoCollectionProgress } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const userId = req.headers.userid;
      const { id, state = 0, collectionId } = req.body;

      if (!collectionId) {
        res.status(400).json({
          data: null,
          message: "collectionId cannot be null",
          success: false,
        });
      }

      if (id) {
        const updatedProgress = await VideoCollectionProgress.findByIdAndUpdate(
          id,
          {
            userId,
            collectionId,
            state: +state,
          },
          { new: true }
        );
        if (updatedProgress) {
          res.status(200).json({
            data: updatedProgress,
            message: "Updated the progress successfully",
            success: true,
          });
        } else {
          res.status(400).json({
            data: null,
            message: "Failed to update progress",
            success: false,
          });
        }
      } else {
        const updatedProgress = await VideoCollectionProgress.findOneAndUpdate(
          { collectionId, userId },
          { userId, state: +state, collectionId },
          { new: true, upsert: true }
        );

        if (updatedProgress) {
          res.status(200).json({
            data: updatedProgress,
            message: "Created the progress successfully",
            success: true,
          });
        } else {
          res.status(400).json({
            data: null,
            message: "Failed to create progress",
            success: false,
          });
        }
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in /api/collection/progress due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
