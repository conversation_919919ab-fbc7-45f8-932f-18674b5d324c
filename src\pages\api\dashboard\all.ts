import { Video, VideoCollection, VideoCollectionLikesMap } from "@/api/mongo";
import {
  getAllCollectionDetails,
  getAllVideoDetails,
} from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const { skip = 0, limit = 10 } = req.query;
      const userId = req.headers.userid;
      let results = await VideoCollection.aggregate([
        {
          $project: {
            type: { $literal: "collection" },
          },
        },
        {
          $unionWith: {
            coll: "videos",
            pipeline: [
              {
                $project: {
                  type: { $literal: "video" },
                },
              },
            ],
          },
        },
        { $sort: { createdAt: -1 } },
        { $skip: +skip },
        { $limit: +limit },
      ]);

      const data = await Promise.all(
        results.map(async (m) => {
          if (m.type === "collection") {
            const collection = await VideoCollection.findById(m._id);
            if (collection) {
              const collectionInfo = await getAllCollectionDetails({
                data: collection,
                userId: String(userId),
                needCoverImages: true,
                needLikeStatus: true,
                needProgress: true,
              });
              return collectionInfo;
            }
            return null;
          } else {
            const video = await Video.findById(m.videoId).populate("creator");
            if (video) {
              const videoInfo = await getAllVideoDetails({
                needProgress: true,
                needLikeStatus: true,
                needCreator: true,
                userId: String(userId),
                data: video.toObject(),
              });
              return videoInfo;
            }
            return null;
          }
        })
      );
      const filtered = data.filter((f) => f !== null);

      res.status(200).json({
        data: filtered,
        message: "fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(`Something went wrong in dashboard/all due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
