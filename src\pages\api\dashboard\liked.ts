import { Video, VideoCollection, VideoCollectionLikesMap } from "@/api/mongo";
import {
  getAllCollectionDetails,
  getAllVideoDetails,
} from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const { skip = 0, limit = 10 } = req.query;
      const userId = req.headers.userid;
      let results = await VideoCollectionLikesMap.aggregate([
        {
          $project: {
            likedBy: 1,
            collectionId: 1,
            videoId: { $literal: null },
            createdAt: 1,
            updatedAt: 1,
            type: { $literal: "collection" },
          },
        },
        {
          $unionWith: {
            coll: "videolikes",
            pipeline: [
              {
                $project: {
                  likedBy: 1,
                  videoId: 1,
                  collectionId: { $literal: null },
                  createdAt: 1,
                  updatedAt: 1,
                  type: { $literal: "video" },
                },
              },
            ],
          },
        },
        { $sort: { createdAt: -1 } },
        { $skip: +skip },
        { $limit: +limit },
      ]);

      const data = await Promise.all(
        results.map(async (m) => {
          const info = m;
          if (m.collectionId) {
            const collection = await VideoCollection.findById(m.collectionId);
            const collectionInfo = await getAllCollectionDetails({
              data: collection,
              userId: String(userId),
              needCoverImages: true,
              needLikeStatus: true,
              needProgress: true,
            });
            info.collectionInfo = collectionInfo;
            return info;
          }
          if (m.videoId) {
            const video = await Video.findById(m.videoId).populate("creator");
            if (video) {
              const videoInfo = await getAllVideoDetails({
                needProgress: true,
                needLikeStatus: true,
                needCreator: true,
                userId: String(userId),
                data: video.toObject(),
              });
              info.videoInfo = videoInfo;
            } else {
              info.videoInfo = null;
            }
            return info;
          }
        })
      );

      res.status(200).json({
        data,
        message: "fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(`Something went wrong in dashboard/liked due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
