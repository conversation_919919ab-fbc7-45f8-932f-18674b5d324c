import { Event, Transaction } from "@/api/mongo";
import { getAllEventDetails } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import mongoose from "mongoose";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  const { id, needEventImage = false } = req.query;
  const userId = req.headers.userid;

  if (!id) {
    res.status(400).json({
      data: null,
      message: "id cannot be empty",
      success: false,
    });
    return;
  }
  try {
    if (req.method === "GET") {
      const singleEvent = await Event.findById(id).populate([
        {
          path: "proficiencyLevel",
        },
        {
          path: "eventOn",
        },
        {
          path: "targetLanguage",
        },
      ]);
      if (singleEvent) {
        const boughtCountAgg = await Transaction.aggregate([
          {
            $match: {
              status: "SUCCESS",
              "eventsPriceDetails.eventInfo": new mongoose.Types.ObjectId(
                id as string
              ),
            },
          },
          { $unwind: "$eventsPriceDetails" },
          {
            $match: {
              "eventsPriceDetails.eventInfo": new mongoose.Types.ObjectId(
                id as string
              ),
            },
          },
          {
            $group: {
              _id: "$eventsPriceDetails.eventOn",
              count: { $sum: 1 },
            },
          },
          {
            $project: {
              _id: 0,
              eventOn: "$_id",
              count: 1,
            },
          },
        ]);
        const boughtCount: Record<string, number> = {};
        boughtCountAgg.forEach((entry) => {
          boughtCount[entry.eventOn.toString()] = entry.count;
        });
        const collectionInfo = await getAllEventDetails({
          data: singleEvent,
          needEventImage: Boolean(needEventImage),
        });
        res.status(200).json({
          data: { ...collectionInfo, boughtCount },
          message: "fetched event successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: `Something went wrong while fetching events using id ${id}`,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in event/get/${id} due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message ?? "Something went wrong",
      success: false,
    });
  }
}
