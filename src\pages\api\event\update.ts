import { deleteAwsFile } from "@/api/aws";
import { Event, EventOn, Video } from "@/api/mongo";
import { combineDateNTime } from "@/utils/dateTime";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "PUT") {
      const {
        id,
        title,
        description,
        mode,
        proficiencyLevel,
        categories,
        location,
        url,
        price,
        currency,
        maxNumberOfRegistrations,
        targetLanguage,
        eventOn,
        imagesKeysAndIds,
        deletedImageKeys,
      } = req.body;

      const payload = {
        title,
        description,
        mode,
        proficiencyLevel,
        categories,
        location,
        url,
        price,
        currency,
        maxNumberOfRegistrations,
        targetLanguage,
        imagesKeysAndIds,
      };

      if (eventOn?.length === 0) {
        return res.status(400).send({
          data: null,
          message: "Please add at least one date and time",
          success: false,
        });
      }

      if (!maxNumberOfRegistrations) {
        delete payload.maxNumberOfRegistrations;
      }
      if (!targetLanguage) {
        delete payload.targetLanguage;
      }
      if (!title) {
        delete payload.title;
      }
      if (!description) {
        delete payload.description;
      }
      if (!mode) {
        delete payload.mode;
      }
      if (!proficiencyLevel) {
        delete payload.proficiencyLevel;
      }
      if (!categories) {
        delete payload.categories;
      }
      if (!location) {
        delete payload.location;
      }
      if (!url) {
        delete payload.url;
      }
      if (!currency) {
        delete payload.currency;
      }
      if (!price) {
        delete payload.price;
      }

      const formattedEventOn = eventOn.map((m) => ({
        ...m,
        startDateTime: combineDateNTime({
          date: m.startDate,
          time: m.startTime,
          timezone: m.timezone,
        }),
        endDateTime: combineDateNTime({
          date: m.endDate,
          time: m.endTime,
          timezone: m.timezone,
        }),
        eventId: id,
      }));

      const eventsOnIds = await handleUpdateInsertEventOn({
        eventsOn: formattedEventOn,
        id,
      });

      const updatedVideo = await Event.findByIdAndUpdate(
        id,
        {
          ...payload,
          eventOn: eventsOnIds,
        },
        {
          new: true,
        }
      );
      if (updatedVideo) {
        if (deletedImageKeys.length > 0) {
          deletedImageKeys.map((m) => deleteAwsImage(m));
        }

        res.send({
          data: updatedVideo,
          message: "Updated the event successfully",
          success: true,
        });
      } else {
        res.send({
          data: null,
          message: "Failed to update the event",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in update-event due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

const handleUpdateInsertEventOn = async ({ eventsOn, id }) => {
  try {
    const eventDetails = await Event.findById(id);
    if (eventDetails?.eventOn?.length > 0) {
      await EventOn.deleteMany({
        _id: { $in: eventDetails.eventOn },
      });
    }
    const newEventsOn = await EventOn.insertMany(eventsOn);
    return newEventsOn.map((m) => m._id);
  } catch (error) {
    console.error(
      `Something went wrong in handleUpdateInsertEventOn due to`,
      error
    );
    throw error;
  }
};

const deleteAwsImage = (key: string) => {
  deleteAwsFile(key);
};
