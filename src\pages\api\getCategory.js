const videos = require("../../../data/data.json");

export default async function handler(req, res) {
  // get, post, delete, put
  if (req.method == "GET") {
    res.status(200).json({
      success: true,
      categories: ["food", "cars", "videos", "animals"],
      videos: videos,
      method: "GET",
    });
  } else if (req.method == "POST") {
    res.status(200).json({
      success: true,
      categories: ["food", "cars", "videos", "animals"],
      method: "POST",
    });
  }
}
