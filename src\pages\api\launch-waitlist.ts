import { NextApiRequest, NextApiResponse } from "next";
import { addToLaunchWaitlist } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  if (req.method === "POST") {
    const { email } = req.body;

    try {
      const result = await addToLaunchWaitlist(email);
      if (result.status) {
        res.status(200).json({ message: result.message });
      } else {
        res.status(400).json({ message: result.message });
      }
    } catch (error) {
      console.error("Error in addToLaunchWaitlist:", error);
      const { message, status } = getMessageAndStatusCode(error);
      res.status(status).json({
        data: null,
        message: message,
        success: false,
      });
    }
  } else {
    res.setHeader("Allow", ["POST"]);
    res.status(405).json({ message: `Method ${req.method} Not Allowed` });
  }
}
