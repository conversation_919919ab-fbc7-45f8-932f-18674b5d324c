import { VideosAndCollectionMyLearningMap } from "@/api/mongo";
import { PROGRESS_STATUS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "POST") {
      const { videoId = "", collectionId = "" } = req.body;
      const userId = req.headers.userid;
      const findPayload = {
        videoId,
        collectionId,
        consumerId: userId,
      };
      if (!videoId) {
        delete findPayload.videoId;
      }
      if (!collectionId) {
        delete findPayload.collectionId;
      }
      const learning = await VideosAndCollectionMyLearningMap.findOneAndUpdate(
        findPayload,
        { $setOnInsert: { videoState: PROGRESS_STATUS.NOT_STARTED } },
        { new: true, upsert: true }
      );
      if (learning) {
        res.status(200).json({
          data: learning,
          message: "Added successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: "Failed to add to learning",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(`Something went wrong in dashboard/mylearning due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
