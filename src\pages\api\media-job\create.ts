import { MediaJob } from "@/api/mongo";
import { FormatResponse, getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const mediaJob = await MediaJob.create(req.body);
      if (mediaJob) {
        res.status(201).json(
          FormatResponse({
            data: mediaJob,
            message: "Media Job created successfully",
            success: true,
          })
        );
      } else {
        res.status(400).json(
          FormatResponse({
            data: null,
            message: "Failed to create Media Job",
            success: false,
          })
        );
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in media-job/create due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
