import { Cart, Transaction } from "@/api/mongo";
import { getPlanString } from "@/utils/common";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { updatedPlan, currentPlan, transactionId } = req.body;
      const userId = req.headers.userid;

      const transactionDetails = await Transaction.findOne({
        _id: transactionId,
        userId,
      });

      if (transactionDetails) {
        const updatedClassesDetails = transactionDetails.classesDetails.map(
          (m) => {
            const loopingplans = m.plans;
            const needsToBeChanged = (() => {
              return loopingplans.some((f) => {
                return getPlanString(f) === getPlanString(currentPlan);
              });
            })();
            if (needsToBeChanged) {
              const updatedPlans = loopingplans.map((k) => {
                if (k.planId === currentPlan.planId) {
                  return updatedPlan;
                } else {
                  return k;
                }
              });
              return {
                classInfo: m.classInfo,
                plans: updatedPlans,
              };
            } else {
              return m;
            }
          }
        );
        const updatedTransaction = await Transaction.findByIdAndUpdate(
          transactionId,
          {
            classesDetails: updatedClassesDetails,
          },
          {
            new: true,
          }
        );
        if (updatedTransaction) {
          res.status(200).json({
            data: updatedTransaction,
            message: "Activated the plan",
            success: true,
          });
        } else {
          res.status(400).json({
            data: null,
            message: "Failed to activate the plan",
            success: false,
          });
        }
      } else {
        res.status(400).json({
          data: null,
          message: "Failed to activate the plan",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in payment/activate-plan due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
