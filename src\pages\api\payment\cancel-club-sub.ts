import { Transaction } from "@/api/mongo";
import { stripe } from "@/api/stripe";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { transactionId } = req.body;
      const userId = req.headers.userid;

      if (!transactionId) {
        return res.status(400).json({
          message: `transactionId is required`,
          success: false,
          data: null,
        });
      }

      const transactionDetails = await Transaction.findById(transactionId);
      if (transactionDetails) {
        const stripeSubscriptionId = transactionDetails.stripeSubscriptionId;
        await stripe.subscriptions.update(stripeSubscriptionId, {
          cancel_at_period_end: true,
        });
        const updatedTransaction = await Transaction.findByIdAndUpdate(
          transactionDetails._id,
          {
            unsubscribe: {
              value: true,
              date: new Date(),
            },
          },
          {
            new: true,
          }
        );
        res.status(200).json({
          message: `Cancelled subscription`,
          data: updatedTransaction,
          success: true,
        });
      } else {
        res.status(400).json({
          message: `Invalid transaction`,
          data: null,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in payment/cancel-club-sub due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
