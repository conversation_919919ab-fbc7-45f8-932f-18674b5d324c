import { Cart } from "@/api/mongo";
import { getAllClubDetails, getAllEventDetails } from "@/api/mongoHelpers";
import { CLASSES_SORT, CLASSES_TYPE } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";
import mongoose from "mongoose";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const {
        skip = 0,
        limit = 10,
        filters = "",
        isAscending = false,
        search,
      } = req.query;
      const userId = req.headers.userid;

      const filterArray = String(filters).split(",");
      const isCommunityExperience = filterArray.includes(
        CLASSES_SORT.COMMUNITY
      );
      const isClub = filterArray.includes(CLASSES_SORT.ONLINE_CLUB);
      const isInPersonClass = filterArray.includes(
        CLASSES_SORT.IN_PERSON_CLASS
      );
      const isOnlineClass = filterArray.includes(CLASSES_SORT.ONLINE_CLASS);

      const matchConditions: Record<string, any>[] = [
        { userId: new mongoose.Types.ObjectId(userId as string) },
        { isCheckedOut: true },
      ];

      const orConditions: any[] = [];

      if (isCommunityExperience) {
        orConditions.push({ eventId: { $exists: true, $ne: null } });
      }

      if (isClub) {
        orConditions.push({ clubId: { $exists: true, $ne: null } });
      }

      if (isInPersonClass) {
        orConditions.push({ "classesId.type": CLASSES_TYPE.IN_PERSON });
      }

      if (isOnlineClass) {
        orConditions.push({ "classesId.type": CLASSES_TYPE.ONLINE });
      }

      if (orConditions.length > 0) {
        matchConditions.push({ $or: orConditions });
      }

      const pipeline: any[] = [
        { $match: { $and: matchConditions } },
        {
          $lookup: {
            from: "transactions",
            localField: "transactionId",
            foreignField: "_id",
            as: "transaction",
          },
        },
        {
          $unwind: {
            path: "$transaction",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $match: {
            "transaction.status": "SUCCESS",
          },
        },
        {
          $lookup: {
            from: "clubs",
            localField: "clubId",
            foreignField: "_id",
            as: "clubId",
          },
        },
        {
          $unwind: {
            path: "$clubId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clubId.teachers",
            foreignField: "_id",
            as: "clubId.teachers",
          },
        },
        {
          $lookup: {
            from: "events",
            localField: "eventId",
            foreignField: "_id",
            as: "eventId",
          },
        },
        {
          $unwind: {
            path: "$eventId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "transactions",
            localField: "transactionId",
            foreignField: "_id",
            as: "transactionId",
          },
        },
        {
          $unwind: {
            path: "$transactionId",
            preserveNullAndEmptyArrays: true,
          },
        },
      ];

      if (search) {
        const regex = new RegExp(search as string, "i");
        pipeline.push({
          $match: {
            $or: [
              { "clubId.title": regex },
              { "eventId.title": regex },
              { "classesId.title": regex },
            ],
          },
        });
      }

      pipeline.push(
        { $sort: { createdAt: isAscending === "true" ? 1 : -1 } },
        { $skip: Number(skip) },
        { $limit: Number(limit) }
      );

      const boughtCarts = await Cart.aggregate(pipeline);

      const dataWithImages = await Promise.all(
        boughtCarts.map(async (cart: any) => {
          if (cart.clubId && "price" in cart.clubId) {
            const expandedClubDetails = await getAllClubDetails({
              data: cart.clubId,
              needClubImage: true,
              needCreatorImage: true,
            });
            return {
              ...cart,
              clubId: expandedClubDetails,
            };
          }
          if (cart.eventId && "price" in cart.eventId) {
            const expandedEventDetails = await getAllEventDetails({
              data: cart.eventId,
              needEventImage: true,
            });
            return {
              ...cart,
              eventId: expandedEventDetails,
            };
          }
          return cart;
        })
      );

      let finalData = [];
      dataWithImages.forEach((cart) => {
        if (cart?.plans?.length > 0) {
          cart.plans.forEach((plan) => {
            finalData.push({
              ...cart,
              currentPlan: plan,
            });
          });
        } else if (cart?.memberships?.length > 0) {
          cart.memberships.forEach((membership) => {
            finalData.push({
              ...cart,
              currentMembership: membership,
            });
          });
        } else {
          finalData.push(cart);
        }
      });

      res.status(200).json({
        data: finalData,
        message: "Transactions fetched successfully",
        success: true,
        nextSkipCount: boughtCarts.length,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(
      `Something went wrong in payment/purchases/my-purchases`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
