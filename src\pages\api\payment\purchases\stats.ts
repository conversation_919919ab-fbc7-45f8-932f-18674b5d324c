import { Transaction } from "@/api/mongo";
import { CLASSES_TYPE, PAYMENT_STATUS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import mongoose from "mongoose";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const userId = req.headers.userid;
      console.log("userId", userId);
      const result = await Transaction.aggregate([
        {
          $match: {
            status: PAYMENT_STATUS.SUCCESS,
            userId: new mongoose.Types.ObjectId(String(userId)),
            $or: [
              { "unsubscribe.value": false },
              { unsubscribe: { $exists: false } },
            ],
          },
        },
        {
          $project: {
            eventIds: 1,
            classesDetails: 1,
            clubsDetails: 1,
          },
        },
        {
          $addFields: {
            eventCount: {
              $size: {
                $ifNull: ["$eventIds", []],
              },
            },
            onlineClubsCount: {
              $sum: {
                $map: {
                  input: { $ifNull: ["$clubsDetails", []] },
                  as: "club",
                  in: { $size: { $ifNull: ["$$club.memberships", []] } },
                },
              },
            },
            onlineClassesCount: {
              $sum: {
                $map: {
                  input: {
                    $filter: {
                      input: { $ifNull: ["$classesDetails", []] },
                      as: "cls",
                      cond: {
                        $eq: ["$$cls.classInfo.type", CLASSES_TYPE.ONLINE],
                      },
                    },
                  },
                  as: "cls",
                  in: { $size: { $ifNull: ["$$cls.plans", []] } },
                },
              },
            },
            inPersonClassesCount: {
              $sum: {
                $map: {
                  input: {
                    $filter: {
                      input: { $ifNull: ["$classesDetails", []] },
                      as: "cls",
                      cond: {
                        $eq: ["$$cls.classInfo.type", CLASSES_TYPE.IN_PERSON],
                      },
                    },
                  },
                  as: "cls",
                  in: { $size: { $ifNull: ["$$cls.plans", []] } },
                },
              },
            },
          },
        },
        {
          $group: {
            _id: null,
            onlineClubsCount: { $sum: "$onlineClubsCount" },
            eventCount: { $sum: "$eventCount" },
            onlineClassesCount: { $sum: "$onlineClassesCount" },
            inPersonClassesCount: { $sum: "$inPersonClassesCount" },
          },
        },
        {
          $project: {
            _id: 0,
            onlineClubsCount: 1,
            eventCount: 1,
            onlineClassesCount: 1,
            inPersonClassesCount: 1,
          },
        },
      ]);

      res.status(200).json({
        data: result[0] ?? null,
        message: "Transactions fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(
      `Something went wrong in payment/purchases/stats due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}