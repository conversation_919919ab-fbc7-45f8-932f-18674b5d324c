import { Cart, Club, Transaction, User } from "@/api/mongo";
import { createClubPriceAndProduct } from "@/api/mongoHelpers";
import { createStripeCustomer, makeStripePayment } from "@/api/stripe";
import { CURRENCY_ENUM, PAYMENT_MODE, PAYMENT_STATUS } from "@/constant/Enums";
import { AMOUNT_CONVERTOR } from "@/utils/classes";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { clubId, emailId, memberships } = req.body;
      const userId = req.headers.userid;
      if (clubId) {
        const clubData = await Club.findById(clubId);
        const clubDetails = await createClubPriceAndProduct({
          clubDetails: clubData,
        });
        if (clubDetails) {
          const finalAmount = +clubDetails.price * AMOUNT_CONVERTOR;

          const payload = {
            clubId: clubDetails._id,
            userId,
            isBuying: true,
          };
          const newCart = await Cart.create(payload);
          const cartId = newCart._id;

          const transaction = await Transaction.create({
            userId,
            status: PAYMENT_STATUS.INITIATED,
            email: emailId,
            stripeCheckoutId: "",
            price: finalAmount,
            discount: 0,
            finalAmount,
            transactionDate: new Date(),
            mode: PAYMENT_MODE.SUBSCRIPTION,

            eventIds: [],
            classesId: [],
            cartId: [cartId],
            clubsDetails: [
              {
                clubInfo: clubDetails._id,
                memberships,
                currency: clubDetails.currency,
                price: finalAmount,
              },
            ],
          });

          const isMexican = clubDetails.currency === CURRENCY_ENUM.MXN;
          const user = await User.findById(userId).select({
            stripeCustomerIdMxn: 1,
            stripeCustomerId: 1,
            firstName: 1,
          });

          let customerId = (() => {
            if (isMexican) {
              return user.stripeCustomerIdMxn ?? "";
            } else {
              return user.stripeCustomerId ?? "";
            }
          })();

          if (!customerId) {
            customerId = await createStripeCustomer({
              email: emailId,
              name: user?.firstName,
              isMexican,
              userId,
            });
          }

          const currency = (() => {
            if (!clubDetails.currency) {
              return CURRENCY_ENUM.USD.toLowerCase();
            }
            return clubDetails?.currency?.toLowerCase();
          })();

          const transactionId = transaction._id;

          const payment = await makeStripePayment({
            price: finalAmount,
            metadata: {
              // cartId: JSON.stringify(cartId),
              // classesId: JSON.stringify(classesId),
              // eventId: JSON.stringify(eventId),
              userId,
              emailId,
            },
            lineItems: [
              {
                price: clubDetails.stripePriceId,
                quantity: 1,
              },
            ],
            email: emailId,
            // currency: "mxn",
            // currency: "usd",
            currency,
            customerId,
            success_url: `${process.env.NEXT_PUBLIC_BASE_URL}payment/${transactionId}`,
            cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}payment/${transactionId}`,
            isPayment: false,
          });
          const paymentId = payment?.id;
          if (paymentId) {
            const url = payment?.url;
            const updatedTransaction = await Transaction.findByIdAndUpdate(
              transactionId,
              {
                stripeCheckoutId: paymentId,
              },
              { new: true }
            );
            res.status(201).json({
              message: `Initiated payment successfully`,
              data: {
                paymentUrl: url,
                transactionDetails: updatedTransaction,
              },
              success: true,
            });
          } else {
            await Transaction.findByIdAndUpdate(
              transactionId,
              {
                status: PAYMENT_STATUS.FAILED,
              },
              { new: true }
            );
            res.status(400).json({
              message: `Failed to initiate payment.Please try again`,
              data: null,
              success: false,
            });
          }
        } else {
          res.status(400).json({
            message: `No club with such Id found. Please try again`,
            data: null,
            success: false,
          });
        }
      } else {
        res.status(400).json({
          message: `Club id required.Please try again`,
          data: null,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in payment/subscribe-club due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
