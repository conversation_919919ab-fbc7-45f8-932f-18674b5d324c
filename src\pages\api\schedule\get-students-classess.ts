import { Transaction } from "@/api/mongo";
import { CLASSES_TYPE, IN_PERSON_TYPE, PAYMENT_STATUS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const {
        type = CLASSES_TYPE.IN_PERSON,
        subType = IN_PERSON_TYPE.GROUP,
        startDate,
        endDate,
      } = req.query;

      const pipeline = [
        {
          $match: {
            status: PAYMENT_STATUS.SUCCESS,
            classesDetails: { $exists: true, $ne: [] },
          },
        },
        { $unwind: "$classesDetails" },
        {
          $match: {
            "classesDetails.classInfo.type": type,
            "classesDetails.classInfo.subType": subType,
          },
        },
        { $unwind: "$classesDetails.plans" },
        {
          $addFields: {
            "classesDetails.plans.startDateConverted": {
              $cond: {
                if: {
                  $eq: [{ $type: "$classesDetails.plans.startDate" }, "date"],
                },
                then: "$classesDetails.plans.startDate",
                else: {
                  $dateFromString: {
                    dateString: "$classesDetails.plans.startDate",
                  },
                },
              },
            },
          },
        },
        {
          $match: {
            "classesDetails.plans.startDateConverted": {
              $lte: new Date(endDate as string),
            },
            "classesDetails.plans.endDate": {
              $gte: new Date(startDate as string),
            },
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "user",
          },
        },
        { $unwind: "$user" },
        {
          $project: {
            _id: 1,
            user: {
              _id: "$user._id",
              firstName: "$user.firstName",
              lastName: "$user.lastName",
              level: "$user.level",
              email: "$user.email",
              clerkId: "$user.clerkId",
            },
            classInfo: "$classesDetails.classInfo",
            currentPlan: "$classesDetails.plans",
            transactionDate: 1,
            price: 1,
            finalAmount: 1,
          },
        },
      ];
      const results = await Transaction.aggregate(pipeline);

      res.status(200).json({
        message: "Students fetched successfully",
        data: results,
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in schedule/all due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
