import { Event, Schedule } from "@/api/mongo";
import { getAllEventDetails } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  const { id, needTeacherDetails } = req.query;
  const userId = req.headers.userid;

  if (!id) {
    res.status(400).json({
      data: null,
      message: "id cannot be empty",
      success: false,
    });
    return;
  }
  try {
    if (req.method === "GET") {
      const fetchTeachersDetails = needTeacherDetails === "true";
      const defaultPopulate = [
        {
          path: "students",
          populate: {
            path: "userId",
            select: "firstName lastName email",
          },
        },
      ];
      if (fetchTeachersDetails) {
        defaultPopulate.push({
          path: "teachers",
          populate: {
            path: "teacherId",
            select: "firstName lastName email",
          },
        });
      }
      const singleEvent = await Schedule.findById(id).populate(defaultPopulate);
      if (singleEvent) {
        res.status(200).json({
          data: singleEvent,
          message: "Scheduled class fetched successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: `Something went wrong while fetching schedule using id ${id}`,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in schedule/get/${id} due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message ?? "Something went wrong",
      success: false,
    });
  }
}
