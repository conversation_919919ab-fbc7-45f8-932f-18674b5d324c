import { Club, Event } from "@/api/mongo";
import { CURRENCY_ENUM } from "@/constant/Enums";
import { combineDateNTime } from "@/utils/dateTime";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const events = await Event.find({ currency: { $exists: false } });

      const eventsResult = {
        successCount: 0,
        failureCount: 0,
        eventsWithoutCurrency: events.length,
      };

      await Promise.all(
        events.map(async (event) => {
          try {
            event.currency = CURRENCY_ENUM.USD;
            await event.save();
            eventsResult.successCount = eventsResult.successCount + 1;
          } catch (err) {
            eventsResult.failureCount = eventsResult.failureCount + 1;
          }
        })
      );

      const clubs = await Club.find({ currency: { $exists: false } });
      const clubsResult = {
        successCount: 0,
        failureCount: 0,
        eventsWithoutCurrency: clubs.length,
      };

      await Promise.all(
        clubs.map(async (club) => {
          try {
            club.currency = CURRENCY_ENUM.USD;
            await club.save();
            clubsResult.successCount = clubsResult.successCount + 1;
          } catch (err) {
            clubsResult.failureCount = clubsResult.failureCount + 1;
          }
        })
      );

      res.status(200).json({
        success: true,
        message: "Updated currency successfully",
        eventsResult,
        clubsResult,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in scripts/add-currency due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
