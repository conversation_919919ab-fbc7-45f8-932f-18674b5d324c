import { Interest, User } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const interestsLength = await Interest.find({}).countDocuments();
      const result = await User.updateMany(
        {
          $expr: {
            $lt: [{ $size: { $ifNull: ["$interest", []] } }, interestsLength],
          },
        },
        {
          $set: { completeLater: true },
        }
      );
      res.status(200).json({
        success: true,
        message: `${result.modifiedCount} users updated`,
        data: null,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in scripts/check-interests due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
