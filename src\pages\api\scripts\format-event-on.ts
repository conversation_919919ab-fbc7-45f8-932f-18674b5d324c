import { Event, EventOn } from "@/api/mongo";
import { combineDateNTime } from "@/utils/dateTime";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const events = await Event.find().lean();
      console.log("Events found:", events.length);

      let successCount = 0;
      let failureCount = 0;

      await Promise.all(
        events.map(async (singleEvent) => {
          try {
            const startDateTime = combineDateNTime({
              date: singleEvent.startDate,
              time: singleEvent.startTime,
              timezone: singleEvent.timezone,
            });
            const endDateTime = combineDateNTime({
              date: singleEvent.endDate,
              time: singleEvent.endTime,
              timezone: singleEvent.timezone,
            });

            const newEventOn = await EventOn.create({
              startDate: singleEvent.startDate,
              startTime: singleEvent.startTime,
              timezone: singleEvent.timezone,
              endDate: singleEvent.endDate,
              endTime: singleEvent.endTime,
              eventId: singleEvent._id,
              startDateTime,
              endDateTime,
            });

            await Event.updateOne(
              { _id: singleEvent._id },
              { $set: { eventOn: [newEventOn._id] } }
            );

            successCount++;
          } catch (err) {
            console.error(`Error processing event ${singleEvent._id}:`, err);
            failureCount++;
          }
        })
      );

      res.status(200).json({
        success: true,
        message: "Events updated successfully",
        updated: successCount,
        failed: failureCount,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error("Error in scripts/format-event-on:", error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
