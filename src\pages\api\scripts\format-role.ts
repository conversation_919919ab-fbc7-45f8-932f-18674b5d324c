// as per xime in the slack, we will allow user to be an admin/creator only
// if the email ends with @patitofeo.com this script will remove the role of
// the user to student if its role is admin/creator
import { Role, User } from "@/api/mongo";
import { ROLE_TYPES } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";
import { RoleType } from "@/api/mongoTypes";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      // Find users who have admin/creator roles but don't have @patitofeo.com email
      const usersToUpdate = await User.find({
        email: /@patitofeo\.com$/i,
        allowedRoles: { $exists: true, $ne: [] },
      }).populate("allowedRoles");

      console.log(
        `Found ${usersToUpdate.length} users to check for role updates`
      );

      let updatedUsersCount = 0;
      const results = [];

      for (const user of usersToUpdate) {
        const userRoles = user.allowedRoles as RoleType[];

        console.log("user", user);
        console.log("userRoles", userRoles);

        // Check if user has admin or creator roles
        const hasAdminOrCreator = userRoles.some(
          (role) =>
            role.type === ROLE_TYPES.ADMIN || role.type === ROLE_TYPES.CREATOR
        );

        if (hasAdminOrCreator) {
          console.log(
            `Processing user: ${user.email} with roles: ${userRoles
              .map((r) => r.type)
              .join(", ")}`
          );

          // Find admin/creator roles to delete
          const rolesToDelete = userRoles.filter(
            (role) =>
              role.type === ROLE_TYPES.ADMIN || role.type === ROLE_TYPES.CREATOR
          );

          // Find other roles to keep
          const rolesToKeep = userRoles.filter(
            (role) =>
              role.type !== ROLE_TYPES.ADMIN && role.type !== ROLE_TYPES.CREATOR
          );

          // Check if user already has student role
          const hasStudentRole = rolesToKeep.some(
            (role) => role.type === ROLE_TYPES.STUDENT
          );

          // Only create student role if user will have no roles left after deletion
          const willHaveNoRoles = rolesToKeep.length === 0;

          try {
            // Delete admin/creator roles
            const roleIdsToDelete = rolesToDelete.map((role) => role._id);
            if (roleIdsToDelete.length > 0) {
              await Role.deleteMany({ _id: { $in: roleIdsToDelete } });
              console.log(
                `Deleted ${roleIdsToDelete.length} admin/creator roles for user ${user.email}`
              );
            }

            // Create student role only if user will have no roles left and doesn't already have student role
            let newStudentRoleId = null;
            if (willHaveNoRoles && !hasStudentRole) {
              const newStudentRole = await Role.create({
                type: ROLE_TYPES.STUDENT,
                user: user._id,
              });
              newStudentRoleId = newStudentRole._id;
              console.log(
                `Created new student role for user ${user.email} (had no roles left)`
              );
            }

            // Update user's allowedRoles array
            const finalAllowedRoleIds = [
              ...rolesToKeep.map((role) => role._id),
              ...(newStudentRoleId ? [newStudentRoleId] : []),
            ];

            const updatedUser = await User.findByIdAndUpdate(
              user._id,
              { allowedRoles: finalAllowedRoleIds },
              { new: true }
            ).populate("allowedRoles");

            updatedUsersCount++;
            results.push({
              userId: user._id,
              email: user.email,
              previousRoles: userRoles.map((r) => r.type),
              newRoles: (updatedUser.allowedRoles as RoleType[]).map(
                (r) => r.type
              ),
              deletedRoles: rolesToDelete.map((r) => r.type),
              addedStudentRole: willHaveNoRoles && !hasStudentRole,
              hadOtherRoles: !willHaveNoRoles,
            });

            console.log(`Successfully updated user ${user.email}`);
          } catch (updateError) {
            console.error(`Error updating user ${user.email}:`, updateError);
            results.push({
              userId: user._id,
              email: user.email,
              error: updateError.message,
            });
          }
        }
      }

      res.status(200).json({
        success: true,
        message: `Role formatting completed. Updated ${updatedUsersCount} users.`,
        data: {
          totalUsersChecked: usersToUpdate.length,
          usersUpdated: updatedUsersCount,
          results,
        },
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in scripts/format-role due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
