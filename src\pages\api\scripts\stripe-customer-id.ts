import { User } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      /// fetch the users whose stripeCustomerId is same as stripeCustomerIdMxn
      /// update the stripeCustomerId to null for those users
      let count = 0;
      const users = await User.find(
        {
          stripeCustomerId: { $exists: true, $ne: null },
          stripeCustomerIdMxn: { $exists: true, $ne: null },
          $expr: {
            $eq: ["$stripeCustomerId", "$stripeCustomerIdMxn"],
          },
        },
        { stripeCustomerId: 1, stripeCustomerIdMxn: 1, _id: 1 }
      );
      for (const user of users) {
        count++;
        await User.findByIdAndUpdate(
          user._id,
          {
            stripeCustomerId: null,
            stripeCustomerIdMxn: null,
          },
          { new: true }
        );
      }

      res.status(200).json({
        success: true,
        users,
        message: `${count} users had duplicate stripe customerid and they have been updated successfully`,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in scripts/stripe-customer-id due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
