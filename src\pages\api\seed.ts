import { NextApiRequest, NextApiResponse } from "next";
import { seedLanguages } from "@/api/seed/seedLanguages";
import { seedInterest } from "@/api/seed/seedInterest";
import { seedGoals } from "@/api/seed/seedGoals";
import { seedCountries } from "@/api/seed/seedCountries";
import { seedCategories } from "@/api/seed/seedCategories";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  seedLanguages(false);
  seedInterest(false);
  seedGoals(false);
  seedCountries(false);
  seedCategories(false);
  return res.status(200).json({ success: true });
}
