import { handleReplaceAllGoals } from "@/api/seed/seedGoals";
import { handleReplaceAllInterests } from "@/api/seed/seedInterest";
import { handleReplaceAllProficiencies } from "@/api/seed/seedLanguages";
import { SEEDING_ENUMS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const { name } = req.query;
      if (name === SEEDING_ENUMS.INTERESTS) {
        await handleReplaceAllInterests();
        return res.status(200).json({ success: true });
      }
      if (name === SEEDING_ENUMS.PROFICIENCIES) {
        await handleReplaceAllProficiencies();
        return res.status(200).json({ success: true });
      }
      if (name === SEEDING_ENUMS.GOALS) {
        await handleReplaceAllGoals();
        return res.status(200).json({ success: true });
      }
      return res.status(200).json({ success: false, message: "Invalid name" });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in seed due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
