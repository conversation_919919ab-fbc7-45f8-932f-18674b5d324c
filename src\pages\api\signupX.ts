// import { FieldError } from "@/api/errors";
// // import { createUser } from "@/api/mongoHelpers";
// import { emailValidator} from "@/api/validators";
// import { NextApiRequest, NextApiResponse } from "next";
// import zod from "zod";
// import { withSessionAPI } from "@/lib/auth/session";

// export default withSessionAPI(async function loginRoute(
//   req: NextApiRequest,
//   res: NextApiResponse
// ) {
//   if (req.method === "POST") {
//     // Process a POST request
//     const {
//       email,
//       password,
//       firstName,
//       lastName,
//       phone,
//       primaryLanguage,
//       proficiencies,
//     } = (await req.body) as {
//       email: string;
//       password: string;
//       firstName: string;
//       lastName: string;
//       primaryLanguage: string;
//       phone: string;
//       proficiencies: Record<string, string>;
//     };
//     try {
//       const user = await createUser(
//         email,
//         password,
//         firstName,
//         lastName,
//         primaryLanguage,
//         proficiencies,
//         phone
//       );
//       req.session.user = {
//         id: user.id,
//         roles: ["user"],
//       };
//       await req.session.save();
//       return res.status(200).json({ success: true, errors: null });
//     } catch (err) {
//       return res.status(400).json({ success: false, errors: err });
//     }
//   } else {
//     // Handle any other HTTP method
//   }
// });
