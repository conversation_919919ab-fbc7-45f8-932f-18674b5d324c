import { User } from "@/api/mongo";
import { FormatResponse, getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const user = await User.create(req.body);
      if (user) {
        res.status(201).json(
          FormatResponse({
            data: user,
            message: "user created successfully",
            success: true,
          })
        );
      } else {
        res.status(400).json(
          FormatResponse({
            data: null,
            message: "Failed to create user",
            success: false,
          })
        );
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in update-user due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
