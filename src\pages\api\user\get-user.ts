import { Role, User } from "@/api/mongo";
import { UserType } from "@/api/mongoTypes";
import { ROLE_TYPES } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { clerkId, populateAll = false } = req.body;

      const populate: any[] = (() => {
        const list: any[] = ["allowedRoles"];
        if (populateAll) {
          list.push(
            {
              path: "languages",
              populate: [{ path: "language" }, { path: "proficiency" }],
            },
            {
              path: "interest",
              populate: {
                path: "id",
              },
            },
            "countryOfResidence",
            "goals",
            "primaryLanguage",
            "languageOfInterest",
            "proficiencyOfLanguageOfInterest"
          );
        }
        return list;
      })();

      const user = await User.findOne({
        clerkId,
      }).populate(populate);

      const updatedUser = await handleUserRole({ user, populate });

      if (updatedUser) {
        res.status(200).json({
          message: "User fetched successfully",
          data: updatedUser,
          success: true,
        });
      } else {
        res.status(404).json({
          message: "User doesnt exists",
          data: null,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in get-user due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

type handleUserRoleProps = {
  user: UserType;
  populate: any[];
};
const handleUserRole = async ({ user, populate = [] }: handleUserRoleProps) => {
  try {
    const userId = user._id;
    const allowedRoles = user?.allowedRoles || [];
    const attachedStringRole = user?.role;
    if (allowedRoles?.length === 0) {
      const newRole = attachedStringRole ?? ROLE_TYPES.STUDENT;
      const createdRole = await Role.create({
        type: newRole,
        description: "",
        user: userId,
      });
      const createdRoleId = createdRole?._id;
      if (createdRole) {
        const updatedUser = await User.findOneAndUpdate(
          userId,
          { $push: { allowedRoles: createdRoleId } },
          { new: true }
        ).populate(populate);
        return updatedUser;
      }
    }

    return user;
  } catch (error) {
    console.error("Something went wrong in handleUserRole due to", error);
    return user;
  }
};
