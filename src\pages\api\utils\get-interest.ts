import { Interest } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const interests = await Interest.find({});
      res.status(200).json({
        data: interests,
        message: "Interests fetched successfully",
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(`Something went wrong in get-interests due to`, error);
    const { message, status } = getMessageAndStatusCode(error);

    res.status(status).json({
      data: null,
      message: message,
    });
  }
}
