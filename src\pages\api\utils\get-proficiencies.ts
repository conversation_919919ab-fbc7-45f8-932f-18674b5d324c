import { LanguageProficiency } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const languageProficiencies = await LanguageProficiency.find({});
      res.status(200).json({
        isError: false,
        success: true,
        data: languageProficiencies,
        message: "Proficiencies fetched successful",
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(`Something went wrong in get-interests due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
