import { Event, Club } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
    if (req.method === "GET") {
      const events = await Event.find({
        endDateTime: { $gte: new Date() },
      }).select("_id");

      const clubs = await Club.find().select("_id");

      const eventsWithType = events.map(
        (m) => `${baseUrl}classes/experiencias-libres/${m._id}`
      );

      const clubsWithType = clubs.map(
        (m) => `${baseUrl}classes/online-clubs/${m._id}`
      );

      return res.status(200).json({
        data: [...eventsWithType, ...clubsWithType],
        message: "Fetched succesfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(
      `Something went wrong in utils/get-sitemap-data due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
