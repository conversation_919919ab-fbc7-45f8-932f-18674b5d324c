import { sendEmail } from "@/api/sendEmail";
import { emailEnum } from "@/constant/email/emailEnum";
import { getWelcomeSubscriptionEmailBody } from "@/constant/email/SubscriptionEmail";
import { getWelcomeWaitlistEmailBody } from "@/constant/email/WelcomeWaitlistEmail";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { type, email } = req.body;
      let payload = {
        body: null,
        subject: "",
      };

      if (type === emailEnum.WAITLIST) {
        payload = {
          body: getWelcomeWaitlistEmailBody({}),
          subject: "Welcome to Patito Feo!",
        };
      }
      if (type === emailEnum.SUBSCRIBE) {
        payload = {
          body: getWelcomeSubscriptionEmailBody({}),
          subject: "Thank You For Subscribing to Patito Feo!",
        };
      }
      sendEmail({
        ...payload,
        to: [email],
      });
      res.status(201).json({
        data: null,
        message: "Sent email successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong utils/send-email due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
