// import { NextApiRequest, NextApiResponse } from "next";

// function isTokenExpired(expiryDate) {
//   return new Date(expiryDate) < new Date();
// }

// export default async function verifyRoute(
//   req: NextApiRequest,
//   res: NextApiResponse
// ) {
//   if (req.method === "GET") {
//     const { token } = req.query;

//     try {
//       // Validate token and update user's verification status
//       // Check token against database
//       // and mark the user's email as verified

//       return res
//         .status(200)
//         .json({ success: true, message: "Email verified successfully" });
//     } catch (error) {
//       return res
//         .status(400)
//         .json({ success: false, error: "Invalid or expired token" });
//     }
//   } else {
//     res.setHeader("Allow", ["GET"]);
//     res.status(405).end(`Method ${req.method} Not Allowed`);
//   }
// }
