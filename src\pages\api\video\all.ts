import { awsDownload } from "@/api/aws";
import { Video, VideoLike, VideoProgress } from "@/api/mongo";
import { getAllVideoDetails } from "@/api/mongoHelpers";
import { VIDEO_STATUS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const {
        skip = 0,
        limit = 10,
        needCreator = false,
        needLikeStatus = false,
        needProgress = false,
        isFeatured = false,
      } = req.query;
      const userId = req.headers.userid;

      const payload = {
        isFeatured,
      };

      if (!isFeatured) {
        delete payload.isFeatured;
      }

      let data = null;
      if (needCreator) {
        data = await Video.find({
          status: VIDEO_STATUS.READY,
          ...payload,
        })
          .skip(+skip)
          .limit(+limit)
          .populate({
            path: "creator",
            select: "firstName lastName profileImageKey",
          });
      } else {
        data = await Video.find({
          status: VIDEO_STATUS.READY,
          ...payload,
        })
          .skip(+skip)
          .limit(+limit);
      }

      data = await Promise.all(
        data.map(async (m) => {
          const data = await getAllVideoDetails({
            needProgress: Boolean(needProgress),
            needLikeStatus: Boolean(needLikeStatus),
            needCreator: Boolean(needCreator),
            userId: String(userId),
            data: m.toObject(),
          });
          return data;
        })
      );

      res.status(200).json({
        data,
        message: "Videos fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["PUT"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in update-user due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
