import { awsDownload } from "@/api/aws";
import { Video, VideoLike, VideoProgress } from "@/api/mongo";
import { getAllVideoDetails } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  const {
    id,
    needCreator = false,
    needLikeStatus = false,
    needProgress = false,
  } = req.query;
  const userId = req.headers.userid;

  if (!id) {
    res.status(400).json({
      data: null,
      message: "id cannot be empty",
      success: false,
    });
    return;
  }
  try {
    if (req.method === "GET") {
      let video = null;
      if (needCreator) {
        video = await Video.findById(id).populate("creator");
      } else {
        video = await Video.findById(id);
      }
      if (video) {
        const data = await getAllVideoDetails({
          needProgress: <PERSON><PERSON><PERSON>(needProgress),
          needLikeStatus: <PERSON><PERSON><PERSON>(needLikeStatus),
          needCreator: <PERSON><PERSON>an(needCreator),
          userId: String(userId),
          data: video.toObject(),
        });

        res.status(200).json({
          data,
          message: "fetched video successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: `Something went wrong while fetching video using id ${id}`,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in video/get/${id} due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message ?? "Something went wrong",
      success: false,
    });
  }
}
