import { Video, VideoLike } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { isLiked = false, id } = req.body;
      const likedBy = req.headers.userid;
      if (isLiked) {
        const likedVideo = await VideoLike.create({
          likedBy,
          videoId: id,
        });
        if (likedVideo) {
          handleLikedCount({
            videoId: id,
            isLiked,
          });
          res.status(200).json({
            data: likedVideo,
            message: "liked video successfully",
            success: true,
          });
        } else {
          res.status(400).json({
            data: null,
            message: "Something went wrong while liking video",
            success: false,
          });
        }
      } else {
        const likedVideo = await VideoLike.findOneAndDelete({
          likedBy,
          videoId: id,
        });
        if (likedVideo) {
          handleLikedCount({
            videoId: id,
            isLiked,
          });
          res.status(200).json({
            data: likedVideo,
            message: "disliked video successfully",
            success: true,
          });
        } else {
          res.status(400).json({
            data: null,
            message: "Something went wrong while disliking video",
            success: false,
          });
        }
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in /api/video/like-dislike due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

type handleLikedCountProps = {
  videoId: String;
  isLiked: Boolean;
};
const handleLikedCount = async ({
  videoId,
  isLiked = false,
}: handleLikedCountProps) => {
  const updatedCollection = await Video.findByIdAndUpdate(videoId, {
    $inc: { likedCount: isLiked ? 1 : -1 },
  });
  return updatedCollection;
};
