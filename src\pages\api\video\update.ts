import { awsDownload } from "@/api/aws";
import { Video } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "PUT") {
      const { getPopulated } = req.query;
      const {
        id,
        thumbnailKey,
        subtitleEnglishKey,
        subtitleSpanishKey,
        videoKey,
        status,
      } = req.body;
      const payload = {
        thumbnailKey,
        en_subtitleKey: subtitleEnglishKey,
        es_subtitleKey: subtitleSpanishKey,
        videoKey,
        status,
      };
      if (!thumbnailKey) {
        delete payload.thumbnailKey;
      }
      if (!subtitleEnglishKey) {
        delete payload.en_subtitleKey;
      }
      if (!subtitleSpanishKey) {
        delete payload.es_subtitleKey;
      }
      if (!videoKey) {
        delete payload.videoKey;
      }
      if (getPopulated) {
        const updatedVideo = await Video.findByIdAndUpdate(id, payload, {
          new: true,
        }).populate("creator");

        let profileImageUrl;

        if (
          updatedVideo?.creator &&
          typeof updatedVideo.creator !== "string" &&
          "profileImageKey" in updatedVideo.creator
        ) {
          const awsImgKey = updatedVideo.creator.profileImageKey;

          if (awsImgKey) {
            const { data } = await awsDownload(awsImgKey);
            profileImageUrl = data;
          }
        }

        const updatedVideoObject = updatedVideo?.toObject();
        const creatorObject =
          updatedVideo?.creator &&
          typeof updatedVideo.creator !== "string" &&
          "profileImageKey" in updatedVideo.creator
            ? updatedVideo.creator.toObject()
            : null;

        res.send({
          creator: creatorObject
            ? {
                profileImageUrl,
                ...creatorObject,
              }
            : null,
          ...updatedVideoObject,
        });
      } else {
        const updatedVideo = await Video.findByIdAndUpdate(id, payload);
        res.send(updatedVideo);
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in update-video due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
