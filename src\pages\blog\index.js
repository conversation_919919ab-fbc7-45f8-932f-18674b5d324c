import React, { useState, useMemo } from "react";
import Container from "@mui/material/Container";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import SearchBar from "../../components/SearchBar";
import BlogPostCard from "../../components/Blogs/blogPostCard";
import Link from "next/link";
import fs from "fs";
import path from "path";
import Head from "next/head";

const BlogPageLayout = ({ children }) => (
  <Container
    maxWidth="lg"
    sx={{
      padding: { xs: "1rem", sm: "2rem" },
      backgroundColor: "background.paper",
    }}
  >
    <Typography variant="h4" sx={{ marginBottom: "1rem" }}>
      The Patito Feo Blog
    </Typography>
    {children}
  </Container>
);

export default function BlogPage({ posts }) {
  const [searchTerm, setSearchTerm] = useState("");
  const filteredPosts = useMemo(() => {
    return posts.filter(
      (post) =>
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [posts, searchTerm]);

  const handleSearch = (value) => {
    setSearchTerm(value);
  };

  return (
    <>
      <Head>
        <title>Blog</title>
      </Head>
      <BlogPageLayout>
        {/* <SearchBar
        posts={posts}
        handleSearch={handleSearch}
        helperText="Search for topics"
      /> */}
        <Grid container spacing={4}>
          {filteredPosts.map((post) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={post.id}>
              <Link
                href={`/blog/${post.id}`}
                passHref
                style={{ textDecoration: "none" }}
              >
                <BlogPostCard post={post} />
              </Link>
            </Grid>
          ))}
        </Grid>
      </BlogPageLayout>
    </>
  );
}

export async function getStaticProps() {
  try {
    const filePath = path.resolve("src", "data", "blog", "blogPosts.json");
    const fileContent = fs.readFileSync(filePath, "utf8");
    const posts = JSON.parse(fileContent);
    return { props: { posts } };
  } catch (error) {
    console.error("Failed to read or parse blog posts:", error);
    return { props: { posts: [] } };
  }
}
