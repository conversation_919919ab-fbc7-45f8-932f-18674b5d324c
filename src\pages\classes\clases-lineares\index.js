import React, { useState } from "react";
import { useRouter } from "next/router";
import { Container, <PERSON>ton, Typography, Box } from "@mui/material";
import Banner from "@/components/classes/Banner";
import ImageSection from "@/components/classes/ImageSection";
import MissionSatement from "@/components/classes/MissionSatement";
import ClassesAboutFooter from "@/components/classes/ClassesAboutFooter";
import { ImageSectionData } from "@/constant/classes/ClasesLinearesData";
import { MissionStatementData } from "../../../constant/patitoBasics/patitoBasics";
import backgroundImage from "@/../public/images/classes/classdetailbanner.svg";
import { ImInButton } from "../../../components/classes/ImInButton";
import Head from "next/head";
import SectionHeader from "@/components/classes/SectionHeader";
import PricingTab from "@/components/PricingTab";
import PricingCard from "@/components/PricingCard";
import ClassesTopNav from "@/components/classes/ClassesTopNav";
import axios from "axios";
import {
  CLASSES_TYPE,
  IN_PERSON_CLASSES_TABS,
  IN_PERSON_TYPE,
  ONLINE_CLASSES_TABS,
} from "@/constant/Enums";
import { useUserContext } from "@/contexts/UserContext";
import { useSnackbar } from "@/hooks/useSnackbar";
import ViewMoreButton from "@/components/classes/ViewMoreButton";
import { GROUP_INPERSON, PRIVATE_INPERSON } from "data/pricing";
import SEO from "@/components/SEO";
import { Metadata } from "@/constant/seoData";
import CreateCommunity from "@/components/classes/CreateCommunity";
import ClassBanner from "@/components/classes/ClassBanner";

const tabs = IN_PERSON_CLASSES_TABS;

export async function getServerSideProps() {
  const res = await axios.get(
    `${process.env.NEXT_PUBLIC_BASE_URL}api/classes/get`,
    {
      params: {
        type: CLASSES_TYPE.IN_PERSON,
      },
    }
  );
  const data = res.data.data;
  const privateData =
    data.filter((m) => m.subType === IN_PERSON_TYPE.PRIVATE) ?? [];
  const groupData =
    data.filter((m) => m.subType === IN_PERSON_TYPE.GROUP) ?? [];

  return {
    props: {
      privateData,
      groupData,
    },
  };
}

const ClasesLineares = ({ privateData, groupData }) => {
  const router = useRouter();
  const { query } = router;
  const [activeTab, setActiveTab] = useState(tabs[0].id);
  const [data, setData] = useState(groupData);
  const { dbUser, user } = useUserContext();
  const { showSnackbar } = useSnackbar();
  const [isViewMore, setIsViewMore] = useState(false);
  const isLoggedIn = !!dbUser;

  const redirectToSignup = () => {
    const localKey = `REDIRECT`;
    localStorage.setItem(
      localKey,
      JSON.stringify({
        type: CLASSES_TYPE.IN_PERSON,
        id: null,
      })
    );
    router.push("/sign-up");
    showSnackbar("Please create new account or login with existing account", {
      type: "warning",
    });
  };

  return (
    <>
      <SEO
        title={Metadata.INPERSON_CLASSES_PAGE.title}
        description={Metadata.INPERSON_CLASSES_PAGE.description}
        ogTitle={Metadata.INPERSON_CLASSES_PAGE.og.title}
        ogDescription={Metadata.INPERSON_CLASSES_PAGE.og.description}
        twitterDescription={Metadata.INPERSON_CLASSES_PAGE.twitter.description}
        twitterTitle={Metadata.INPERSON_CLASSES_PAGE.twitter.title}
        url={Metadata.INPERSON_CLASSES_PAGE.url}
        keywords={Metadata.INPERSON_CLASSES_PAGE.keywords.join(",")}
      />
      <ClassBanner text="In-Person Classes" />
      <Container maxWidth="lg" sx={{ textAlign: "center" }}>
        <SectionHeader data={ImageSectionData[0]} />
        {isViewMore && (
          <>
            {ImageSectionData.map(
              (m, i) => i !== 0 && <SectionHeader key={i} data={m} index={i} />
            )}
            <MissionSatement data={MissionStatementData} />
          </>
        )}
        <ViewMoreButton
          isViewMore={isViewMore}
          toggle={() => {
            setIsViewMore((prev) => !prev);
          }}
        />
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="center"
          mt={6}
        >
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="center"
            sx={{
              width: {
                xs: "100%",
                sm: "70%",
                md: "50%",
              },
            }}
          >
            <PricingTab
              tabs={tabs}
              active={activeTab}
              onClick={(tabData) => {
                setActiveTab(tabData.id);
                if (tabData.id === 1) {
                  setData(groupData);
                } else {
                  setData(privateData);
                }
              }}
            />
          </Box>
        </Box>

        <Typography
          textAlign="center"
          mt={8}
          mb={10}
          sx={{ fontWeight: "500" }}
        >
          {tabs.find((f) => f.id === activeTab)?.description}
        </Typography>

        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="center"
          flexWrap="wrap"
          gap={10}
        >
          {data?.map((m, i) => (
            <PricingCard
              key={m?._id}
              data={m}
              isLoggedIn={isLoggedIn}
              redirectToSignup={redirectToSignup}
            />
          ))}
        </Box>

        <ClassesAboutFooter />
      </Container>
    </>
  );
};

export default ClasesLineares;
