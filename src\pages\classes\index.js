import React from "react";
import { Grid, Typography, Box, Container } from "@mui/material";
import ClassCard from "@/components/classes/ClassCard";
import { ClassesData } from "@/constant/classes/ClassesData";
import backgroundImage from "@/../public/images/classes/banner.svg";
import SEO from "@/components/SEO";
import { Metadata } from "@/constant/seoData";
import ClassBanner from "@/components/classes/ClassBanner";

// import { useWhatsApp } from "../../hooks/useWhatsapp";
// import ResponsiveCard from "@/components/Card";

// const defaultWhatsMessage =
//   "Hello! I'm interested in your classes. Can you provide me with more information?";

// const ClassesPageLayout = ({ children }) => (
//   <Container
//     maxWidth="lg"
//     sx={{
//       padding: { xs: "1rem", sm: "2rem" },
//       backgroundColor: "background.paper",
//       marginTop: "2rem",
//     }}
//   >
//     {children}
//   </Container>
// );

// export default function ClassesPage({ NEXT_PUBLIC_WHATSAPP_PHONE }) {
//   const { openWhatsAppChat, error } = useWhatsApp(
//     NEXT_PUBLIC_WHATSAPP_PHONE,
//     defaultWhatsMessage
//   );

//   return (
//     <ClassesPageLayout>
//       <CombinedClassesSection openWhatsAppChat={openWhatsAppChat} />
//     </ClassesPageLayout>
//   );
// }

//  const getServerSideProps = async ({ req }) => {
//   const NEXT_PUBLIC_WHATSAPP_PHONE =
//     process.env.NEXT_PUBLIC_WHATSAPP_PHONE || "";

//   return {
//     props: {
//       NEXT_PUBLIC_WHATSAPP_PHONE,
//     },
//   };
// };

export default function ClassesPage() {
  return (
    <>
      <SEO
        title={Metadata.CLASSES_PAGE.title}
        description={Metadata.CLASSES_PAGE.description}
        keywords={Metadata.CLASSES_PAGE.keywords.join(",")}
      />
      <ClassBanner text="Choose Your Pathway" />
      <Container>
        <hr
          style={{ marginBottom: "20px", color: "#E6E6E6", height: "3px" }}
        ></hr>
        <Grid container spacing={1} justifyContent="center">
          {ClassesData.map((classItem, index) => (
            <Grid
              item
              key={index}
              xs={12}
              sm={6}
              md={3}
              display="flex"
              justifyContent="center"
            >
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "space-between",
                  alignItems: "center",
                  width: "100%",
                }}
              >
                <ClassCard
                  title={classItem.title}
                  description={classItem.description}
                  image={classItem.image}
                  color={classItem.color}
                  buttonText={classItem.buttonText}
                  url={classItem.url}
                />
              </Box>
            </Grid>
          ))}
        </Grid>
      </Container>
    </>
  );
}
