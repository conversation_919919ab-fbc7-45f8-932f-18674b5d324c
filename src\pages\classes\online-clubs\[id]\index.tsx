import { ClubType, UserType } from "@/api/mongoTypes";
import CheckoutClubsModal from "@/components/CheckoutModal/CheckoutClub/CheckoutClubsModal";
import HeaderImages from "@/components/classes/HeaderImages";
import { useSnackbar } from "@/hooks/useSnackbar";
import { getLocalBuyClubDetails } from "@/utils/classes";
import axiosInstance from "@/utils/interceptor";
import {
  Avatar,
  Box,
  Button,
  Chip,
  Container,
  SxProps,
  Theme,
  Typography,
} from "@mui/material";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";

export async function getServerSideProps(context) {
  const { id } = context.params;
  const { data } = await axiosInstance.get(`club/get/${id}`, {
    params: {
      needClubImage: true,
      needCreators: true,
    },
  });
  return {
    props: data,
  };
}

type OnlineClubDetailProps = React.FC<{
  data: ClubType;
}>;
const OnlineClubDetail: OnlineClubDetailProps = ({ data }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const creatorDetails = data.teachers.map((m) => m as UserType);
  const router = useRouter();
  const { showSnackbar } = useSnackbar();

  useEffect(() => {
    if (data) {
      const buyData = getLocalBuyClubDetails();
      if (buyData?._id === data._id) {
        setIsModalOpen(true);
        localStorage.removeItem("BUY_CLUB");
      }
    }
  }, [data]);

  if (!data) {
    return <p>no online club found</p>;
  }

  const redirectToSignup = () => {
    router.push("/sign-up");
    showSnackbar("Please create new account or login with existing account", {
      type: "warning",
    });
  };

  return (
    <>
      <Container
        maxWidth="lg"
        sx={{
          marginTop: "30px",
          height: "100%",
        }}
      >
        <HeaderImages
          images={data.images.map((m) => m.url)}
          currency={data.currency}
          price={data.price}
        />
        <Details
          about={data.about}
          categories={data.categories}
          targetLanguage={data?.targetLanguage}
          highlights={data.highlights}
          theme={data.theme}
          title={data.title}
          creatorDetails={creatorDetails}
          handleAddToCart={() => {
            setIsModalOpen(true);
          }}
        />
      </Container>
      {isModalOpen && (
        <CheckoutClubsModal
          redirectToSignup={redirectToSignup}
          open={isModalOpen}
          setOpen={setIsModalOpen}
          data={data}
        />
      )}
    </>
  );
};

const Details = ({
  categories,
  highlights,
  about,
  theme,
  title,
  handleAddToCart,
  creatorDetails,
  targetLanguage,
}) => {
  const targetLanguageName = (() => {
    if (!targetLanguage) {
      return null;
    }
    return targetLanguage?.nameInEnglish ?? targetLanguage?.name;
  })();

  return (
    <Box
      sx={{
        borderRadius: 4,
        border: "1px solid rgba(230, 230, 230, 1)",
        p: 6,
        mt: 6,
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        sx={{
          borderBottom: "1px solid rgba(237, 237, 237, 1)",
        }}
        mb={5}
        pb={5}
      >
        <Typography fontWeight="600" fontSize={20}>
          {title}
        </Typography>
        <Button
          onClick={() => {
            handleAddToCart();
          }}
          sx={{ textWrap: "nowrap", minWidth: 200 }}
        >
          Subscribe
        </Button>
      </Box>

      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        mb={5}
      >
        <TitleAndValue
          title="Created by"
          sx={{
            mb: 5,
            width: targetLanguageName ? "50%" : "100%",
          }}
        >
          <Box display="flex" flexDirection="row" gap={2}>
            {creatorDetails.map((m) => (
              <CreatorUser
                key={m._id}
                imageUrl={m.profileImageUrl}
                name={`${m.firstName} ${m.lastName}`}
              />
            ))}
          </Box>
        </TitleAndValue>
        {targetLanguageName && (
          <TitleAndValue title="Target Language :" sx={{ width: "50%" }}>
            <Typography color="rgba(109, 109, 109, 1)" fontSize={14}>
              {targetLanguageName}
            </Typography>
          </TitleAndValue>
        )}
      </Box>

      {/* <TitleAndValue
        title="Created by"
        sx={{
          mb: 5,
        }}
      >
        <Box display="flex" flexDirection="row" gap={2}>
          {creatorDetails.map((m) => (
            <CreatorUser
              key={m._id}
              imageUrl={m.profileImageUrl}
              name={`${m.firstName} ${m.lastName}`}
            />
          ))}
        </Box>
      </TitleAndValue> */}

      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        mb={5}
      >
        <TitleAndValue title="Theme :" sx={{ width: "50%" }}>
          <Typography color="rgba(109, 109, 109, 1)" fontSize={14}>
            {theme}
          </Typography>
        </TitleAndValue>
        <TitleAndValue title="Mode of Course :" sx={{ width: "50%" }}>
          <Typography color="rgba(109, 109, 109, 1)" fontSize={14}>
            Online Club
          </Typography>
        </TitleAndValue>
      </Box>

      <TitleAndValue title="Categories :" sx={{ mb: 5 }}>
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="start"
          flexWrap="wrap"
          gap={2}
          mt={1}
        >
          {categories.map((m) => (
            <Chip
              sx={{
                background: "rgba(114, 202, 196, 1)",
                color: "#fff",
                p: 0,
                fontSize: 13,
              }}
              label={m}
              key={m}
            />
          ))}
        </Box>
      </TitleAndValue>
      <TitleAndValue title="Key Highlights" sx={{ mb: 5 }}>
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          flexWrap="wrap"
        >
          {highlights.map((m) => (
            <li
              key={m}
              style={{
                color: "rgba(109, 109, 109, 1)",
                fontSize: 14,
                marginBottom: 0,
                marginTop: 0,
                width: "40%",
              }}
            >
              {m}
            </li>
          ))}
        </Box>
      </TitleAndValue>
      <TitleAndValue title="About :" sx={{ mb: 5 }}>
        <Typography color="rgba(109, 109, 109, 1)" fontSize={14}>
          {about}
        </Typography>
      </TitleAndValue>
    </Box>
  );
};

type CreatorUserProps = React.FC<{
  imageUrl: string;
  name: string;
}>;
const CreatorUser: CreatorUserProps = ({ imageUrl, name }) => {
  return (
    <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
      <Avatar sx={{ width: 20, height: 20 }} alt={name} src={imageUrl} />
      <Typography color="rgba(109, 109, 109, 1)" fontSize={14}>
        {name}
      </Typography>
    </Box>
  );
};

type TitleAndValueProps = React.FC<{
  title: string;
  children: React.ReactNode;
  sx?: SxProps<Theme>;
}>;
const TitleAndValue: TitleAndValueProps = ({ title, children, sx = {} }) => {
  return (
    <Box sx={sx}>
      <Typography fontWeight="700">{title}</Typography>
      <Box>{children}</Box>
    </Box>
  );
};

export default OnlineClubDetail;
