import React, { useState } from "react";
import Banner from "../../../components/classes/Banner";
import { Box, Container, Typography } from "@mui/material";
import backgroundImage from "../../../../public/images/classes/classdetailbanner.svg";
import SectionHeader from "@/components/classes/SectionHeader";
import MissionStatement from "@/components/classes/MissionSatement";
import ViewMoreButton from "@/components/classes/ViewMoreButton";
import { MissionStatementData } from "@/constant/patitoBasics/patitoBasics";
import { OnlineClubsImagesData } from "@/constant/classes/onlineClubsData";
import dynamic from "next/dynamic";
import ClassBanner from "@/components/classes/ClassBanner";

const ClubList = dynamic(
  () => import("@/components/classes/OnlineClub/ClubList"),
  { loading: () => <p>Loading...</p>, ssr: false }
);

const OnlineClubs = () => {
  const [isViewMore, setIsViewMore] = useState(false);

  return (
    <>
      <ClassBanner text="Online Clubs" />
      <Container maxWidth="lg" sx={{ textAlign: "center" }}>
        <SectionHeader data={OnlineClubsImagesData[0]} />
        {isViewMore && (
          <>
            {OnlineClubsImagesData.map(
              (m, i) => i !== 0 && <SectionHeader key={i} data={m} index={i} />
            )}
            <MissionStatement data={MissionStatementData} />
          </>
        )}

        <ViewMoreButton
          isViewMore={isViewMore}
          toggle={() => {
            setIsViewMore((prev) => !prev);
          }}
        />

        <Typography fontWeight="700" fontSize={24}>
          Find Your Perfect Online Community
        </Typography>

        <ClubList />
      </Container>
    </>
  );
};

export default OnlineClubs;
