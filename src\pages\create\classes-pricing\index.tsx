import { ClassesPricingType } from "@/api/mongoTypes";
import EditPlanCard from "@/components/Create/ClassPricing/EditPlanCard";
import Loading from "@/components/Create/ClassPricing/Loading";
import TabBar from "@/components/Create/ClassPricing/TabBar";
import CreateHeader from "@/components/Create/CreateHeader";
import {
  CLASSES_TYPE,
  IN_PERSON_TYPE,
  ONLINE_CLASSES_TYPE,
} from "@/constant/Enums";
import axiosInstance from "@/utils/interceptor";
import { Box, Container, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";

const ClassesPricing = () => {
  const [classType, setClassType] = useState<CLASSES_TYPE>(
    CLASSES_TYPE.IN_PERSON
  );
  const [classSubType, setClassSubType] = useState<
    IN_PERSON_TYPE | ONLINE_CLASSES_TYPE
  >(IN_PERSON_TYPE.GROUP);
  const [classes, setClasses] = useState<ClassesPricingType[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      const handleError = () => {
        setIsLoading(false);
      };
      try {
        setIsLoading(true);
        const { data } = await axiosInstance.get("classes/get", {
          params: {
            type: classType,
            subType: classSubType,
          },
        });
        console.log("data", data);
        if (data.success) {
          setClasses(data.data);
          setIsLoading(false);
        } else {
          handleError();
        }
      } catch (error) {
        console.error("Something went wrong with fetchData due to ", error);
        handleError();
      }
    };

    fetchData();
  }, [classType, classSubType]);

  return (
    <Container
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: { xs: "1rem", sm: "2rem", md: "4rem" },
      }}
    >
      <CreateHeader text="Class Pricing" maxWidth="100%" bottomBorderNeeded />
      <TabBar
        classSubType={classSubType}
        classType={classType}
        setClassType={setClassType}
        setClassSubType={setClassSubType}
      />
      <Typography textAlign="start" width="100%" mt={6} mb={2} fontWeight={800}>
        English
      </Typography>
      <Box gap={4} display="flex" flexDirection="column" width="100%">
        {isLoading ? (
          <Loading />
        ) : (
          <>
            {classes.map((m, i) => (
              <EditPlanCard key={i} data={m} setClasses={setClasses} />
            ))}
          </>
        )}
      </Box>
    </Container>
  );
};

export default ClassesPricing;
