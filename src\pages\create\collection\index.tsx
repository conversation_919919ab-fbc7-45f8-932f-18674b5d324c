import { environment } from "@/api/aws";
import { useSnackbar } from "@/hooks/useSnackbar";
import React, { useState } from "react";
import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import ExistingVideosModal from "@/components/Create/modal/ExistingVideosModal";
import UploadVideosModal from "@/components/Create/modal/UploadVideosModal";
import CreateHeader from "@/components/Create/CreateHeader";
import SelectFileCard from "@/components/Create/SelectFileCard";
import CustomButton from "@/components/CustomButton";
import VideoCard from "@/components/Create/VideoCard";
import { LEVEL_TYPES } from "@/constant/Enums";
import { uploadFileOnAws } from "@/utils/uploadFileOnAws";
import Head from "next/head";

const CreateCollection = () => {
  const { showSnackbar } = useSnackbar();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [file, setFile] = useState(null);
  const [isCreating, setIsCreating] = useState(false);
  const [level, setLevel] = useState(LEVEL_TYPES.BEGINNER);

  const [isExistingOpen, setIsExistingOpen] = useState(false);
  const [isUVideoOpen, setUVideoOpen] = useState(false);
  const [existingCollection, setExistingCollection] = useState([]);

  const handleSelectFile = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    try {
      if (event.target.files && event.target.files.length > 0) {
        const selectedFile = event.target.files[0];
        setFile(selectedFile);
      }
    } catch (error) {
      console.error(`omething went wrong in handleSelectFile due to `, error);
      showSnackbar("Something went wrong in selecting file", {
        type: "error",
      });
    }
  };

  const uploadImageTos3 = async (id: string) => {
    const avatarFileName = file?.name;
    const data = await uploadFileOnAws({
      file,
      key: `${environment}/videocollection/${id}/collection_${avatarFileName}`,
    });
    return data;
  };

  const handleCreateCollection = async () => {
    if (!name) {
      showSnackbar("Please enter name", {
        type: "warning",
      });
      return;
    }
    if (!description) {
      showSnackbar("Please enter description", {
        type: "warning",
      });
      return;
    }
    if (!file) {
      showSnackbar("Please select collection image", {
        type: "warning",
      });
      return;
    }
    // if (existingCollection.length === 0) {
    //   showSnackbar(
    //     "Please select atleast one existing video or create a new one",
    //     {
    //       type: "warning",
    //     }
    //   );
    //   return;
    // }
    try {
      setIsCreating(true);
      const imageId = uuidv4();
      const uploadedImage = await uploadImageTos3(imageId);
      if (!uploadedImage.isError && uploadedImage.data.Key) {
        const { data } = await axios.post(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/collection/create`,
          {
            name,
            desc: description,
            imageId,
            imageKey: uploadedImage.data.Key,
            videoIds: existingCollection.map((m) => m._id),
            level,
          }
        );
        if (data.success) {
          setExistingCollection([]);
          setName("");
          setDescription("");
          setFile(null);
          showSnackbar("Created collection successfully", {
            type: "success",
          });
        } else {
          showSnackbar("Failed to create collection", {
            type: "error",
          });
        }
      }
      setIsCreating(false);
    } catch (error) {
      setIsCreating(false);
      showSnackbar("Something went wrong while creating collection", {
        type: "error",
      });
    }
  };

  return (
    <>
      <Head>
        <title>Create Collection</title>
      </Head>{" "}
      <Box
        display="flex"
        flexDirection="column"
        gap={5}
        mt={20}
        maxWidth="800px"
        alignItems="center"
        width="100%"
        mx="auto"
        p={10}
      >
        <CreateHeader
          maxWidth="800px"
          text="Video Collection Creation"
          bottomBorderNeeded
        />
        <TextField
          sx={{ marginTop: 10 }}
          placeholder="Enter collection name"
          variant="outlined"
          fullWidth
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
        />
        <TextField
          placeholder="Enter collection description"
          variant="outlined"
          fullWidth
          type="text"
          multiline
          maxRows={4}
          value={description}
          onChange={(e) => setDescription(e.target.value)}
        />
        <FormControl fullWidth>
          <InputLabel>
            <Typography color="#c2c2c2">Select Level</Typography>
          </InputLabel>
          <Select
            value={level}
            onChange={(e) => {
              const val = e.target.value;
              if (
                val === LEVEL_TYPES.BEGINNER ||
                val === LEVEL_TYPES.ADVANCED ||
                val === LEVEL_TYPES.INTERMEDIATE
              ) {
                setLevel(val);
              }
            }}
            label="Select Level"
          >
            <MenuItem value={LEVEL_TYPES.BEGINNER}>Beginner</MenuItem>
            <MenuItem value={LEVEL_TYPES.ADVANCED}>Advanced</MenuItem>
            <MenuItem value={LEVEL_TYPES.INTERMEDIATE}>Intermediate</MenuItem>
          </Select>
        </FormControl>
        <p style={{ textAlign: "center", fontWeight: "bold" }}>
          Collection Image
        </p>
        <SelectFileCard
          file={file}
          isImage
          onSelect={handleSelectFile}
          setFile={setFile}
          text="Upload Image"
        />
        <p style={{ textAlign: "center", fontWeight: "bold" }}>Add Videos</p>
        <Box
          alignItems="center"
          justifyContent="center"
          display="flex"
          gap={2}
          mb={10}
          sx={{
            width: "100%",
            flexDirection: {
              xs: "column",
              sm: "row",
            },
          }}
        >
          <CustomButton
            colortype="secondary"
            text="Add Existing Videos"
            sx={{
              width: {
                xs: "100%",
                sm: "50%",
                md: "33%",
              },
            }}
            onClick={() => {
              setIsExistingOpen(true);
            }}
          />
          <CustomButton
            colortype="secondary"
            text="Upload Video"
            bgtype="outline"
            sx={{
              width: {
                xs: "100%",
                sm: "50%",
                md: "33%",
              },
            }}
            onClick={() => {
              setUVideoOpen(true);
            }}
          />
        </Box>

        {existingCollection.map((m, i) => (
          <VideoCard
            key={i}
            isModal={false}
            data={m}
            onRemoveClicked={() => {
              setExistingCollection((prev) => {
                const removedList = prev.filter((f) => f._id !== m._id);
                return removedList;
              });
            }}
          />
        ))}
        <CustomButton
          text={isCreating ? "creating..." : "Create Collection"}
          sx={{
            width: {
              xs: "100%",
              sm: "50%",
              md: "40%",
            },
            fontSize: "1.2rem",
            marginTop: 15,
          }}
          disabled={isCreating}
          onClick={handleCreateCollection}
        />
      </Box>
      {isExistingOpen && (
        <ExistingVideosModal
          existingCollection={existingCollection}
          setExistingCollection={setExistingCollection}
          open={isExistingOpen}
          setOpen={setIsExistingOpen}
        />
      )}
      <UploadVideosModal
        existingCollection={existingCollection}
        setExistingCollection={setExistingCollection}
        open={isUVideoOpen}
        setOpen={setUVideoOpen}
      />
    </>
  );
};

export default CreateCollection;
