import { useSnackbar } from "@/hooks/useSnackbar";
import React, { useState } from "react";
import { Box } from "@mui/material";
import { environment } from "@/api/aws";
import CreateHeader from "@/components/Create/CreateHeader";
import CreatorCard from "@/components/Create/CreatorCard";
import CustomButton from "@/components/CustomButton";
import {
  createCreatorService,
  creatorUserValidator,
} from "@/utils/creatorUtils";
import Head from "next/head";
const CreateUser = () => {
  const { showSnackbar } = useSnackbar();
  const [avatarFile, setAvatarFile] = useState(null);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateUser = async () => {
    const inputError = creatorUserValidator({
      firstName,
      lastName,
      email,
      avatarFile,
    });
    if (inputError) {
      showSnackbar(inputError, {
        type: "error",
      });
      return;
    }
    try {
      setIsCreating(true);
      await createCreatorService({
        environment,
        avatarFile,
        firstName,
        lastName,
        email,
        onImageUploadFail: () => {
          showSnackbar(
            "Failed to upload image.Please try to create user again",
            {
              type: "error",
            }
          );
        },
        onCreateCreatorFail: () => {
          showSnackbar("Failed to create creator.Please try again", {
            type: "error",
          });
        },
        onCreateCreatorSuccess: () => {
          setAvatarFile(null);
          setFirstName("");
          setLastName("");
          setEmail("");
          showSnackbar("Created creator successfully", {
            type: "success",
          });
        },
      });
      setIsCreating(false);
    } catch (error) {
      setIsCreating(false);
    }
  };

  return (
    <><Head>
        <title>Add A Creator</title>
      </Head>{" "}
    <Box
      display="flex"
      flexDirection="column"
      gap={2}
      mt={20}
      p={10}
      maxWidth="600px"
      width="100%"
      mx="auto"
    >
      <CreateHeader text="Adding a New Creator" maxWidth="600px" />

      <CreatorCard
        firstName={firstName}
        setFirstName={setFirstName}
        lastName={lastName}
        setLastName={setLastName}
        email={email}
        setEmail={setEmail}
        setAvatarFile={setAvatarFile}
        avatarFile={avatarFile}
        style={{
          mt: 10,
        }}
      />

      <Box
        display="flex"
        width="100%"
        alignItems="center"
        justifyContent="center"
        mt={10}
      >
        <CustomButton
          disabled={isCreating}
          text={isCreating ? "creating..." : "Add Creator"}
          sx={{
            width: {
              xs: "100%",
              sm: "50%",
              md: "40%",
            },
            fontSize: "1.2rem",
          }}
          onClick={handleCreateUser}
        />
      </Box>
    </Box></>
  );
};

export default CreateUser;
