import { ScheduleType } from "@/api/mongoTypes";
import CreateListHeader from "@/components/Create/CreateListHeader";
import { useSnackbar } from "@/hooks/useSnackbar";
import { formatDate } from "@/utils/dateTime";
import axiosInstance from "@/utils/interceptor";
import { Box, Card, Container } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

const LIMIT = 10;

const Schedule = () => {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const [skip, setSkip] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);
  const [data, setData] = useState<ScheduleType[]>([]);

  const fetchData = async ({ skipCount = 0 }) => {
    const showLoading = +skipCount === 0;
    try {
      if (showLoading) {
        setIsLoading(true);
      }
      const { data } = await axiosInstance.get(`schedule/all`, {
        params: {
          skip: skipCount,
          limit: LIMIT,
        },
      });
      if (data.success && data?.data) {
        const length = data?.data?.length;
        setData((prev) => [...prev, ...data.data]);
        setHasMore(length >= LIMIT);
        setSkip(+skipCount + LIMIT);
      } else {
        showSnackbar("Failed to fetch schedules", {
          type: "error",
        });
      }
      if (showLoading) {
        setIsLoading(false);
      }
    } catch (error) {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchData({ skipCount: 0 });
  }, []);

  return (
    <Container
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: { xs: "1rem", sm: "2rem", md: "4rem" },
      }}
    >
      <CreateListHeader
        createButtonText="Schedule"
        createRoute="/create/schedule/create"
        title="Class Scheduler"
      />
      {isLoading ? (
        <p>fetching schedules</p>
      ) : (
        <InfiniteScroll
          dataLength={data.length}
          next={() => fetchData({ skipCount: skip })}
          hasMore={hasMore}
          loader={<p style={{ textAlign: "center" }}>Loading</p>}
          endMessage={
            <p style={{ textAlign: "center" }}>
              <b>Yay! You have seen it all</b>
            </p>
          }
        >
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            mt={5}
          >
            <Box width="25%">start date</Box>
            <Box width="25%">end date</Box>
            <Box width="25%">teachers</Box>
            <Box width="25%">students</Box>
          </Box>
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="center"
            flexWrap="wrap"
            mt={2}
          >
            {data.map((m, i) => (
              <Card
                key={m._id}
                sx={{
                  width: "100%",
                  p: 2,
                  mb: 1,
                }}
              >
                <Link
                  href={`/create/schedule/update/${m._id}`}
                  style={{
                    width: "100%",
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    textDecoration: "none",
                    color: "#000",
                  }}
                >
                  <Box width="25%">{formatDate({ date: m.startDate })}</Box>
                  <Box width="25%">{formatDate({ date: m.endDate })}</Box>
                  <Box width="25%">{m.teachers.length}</Box>
                  <Box width="25%">{m.students.length}</Box>
                </Link>
              </Card>
            ))}
          </Box>
        </InfiniteScroll>
      )}
    </Container>
  );
};

export default Schedule;
