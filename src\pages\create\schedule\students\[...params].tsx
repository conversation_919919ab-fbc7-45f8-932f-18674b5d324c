import { <PERSON><PERSON>, <PERSON>, Button, Container, Typography } from "@mui/material";
import React, { useMemo, useState } from "react";
import Person2OutlinedIcon from "@mui/icons-material/Person2Outlined";
import FmdGoodOutlinedIcon from "@mui/icons-material/FmdGoodOutlined";
import EditIcon from "@mui/icons-material/Edit";
import CreateHeader from "@/components/Create/CreateHeader";
import { useRouter } from "next/router";
import axiosInstance from "@/utils/interceptor";
import { getSubTypeName, getTypeName, getUserFullName } from "@/utils/format";
import { ScheduledStudentType, ScheduleType, UserType } from "@/api/mongoTypes";
import { formatDate, formatTime } from "@/utils/dateTime";
import { useSnackbar } from "@/hooks/useSnackbar";
import { LEVEL_TYPES, ROLE_TYPES } from "@/constant/Enums";
import ScheduleModal from "@/components/Create/schedule/ScheduleModal";

export async function getServerSideProps(context) {
  const params = context.params.params;
  const id = params[1];
  const level = params[0];
  const { data } = await axiosInstance.get(`schedule/get/${id}`, {
    params: {
      needTeacherDetails: true,
    },
  });
  const { data: respData } = await axiosInstance.post(
    `user/get-users`,
    {
      role: ROLE_TYPES.TEAHCER,
    },
    {
      params: {
        skip: 0,
        limit: 30,
      },
    }
  );
  const teachersList = respData.data;
  return {
    props: {
      ...data,
      level,
      teachersList,
    },
  };
}

const Students = ({ data, level, teachersList }) => {
  const [studentDetails, setStudentDetails] = useState(data);

  console.log("studentDetails", studentDetails);

  if (!studentDetails) {
    return <p>bruh..</p>;
  }

  return (
    <Container
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: { xs: "1rem", sm: "2rem", md: "4rem" },
      }}
    >
      <CreateHeader
        text={`${getTypeName(studentDetails.type)} Classes : ${getSubTypeName(
          studentDetails.subType
        )} Classes `}
        maxWidth="100%"
        backRoute=""
        sx={{ mb: 10 }}
      />
      <Box width="100%" mb={2}>
        <Typography fontWeight={700} textAlign="left">
          {level}
        </Typography>
      </Box>
      <TeacherAndLocation
        data={studentDetails}
        setData={setStudentDetails}
        level={level}
        teachersList={teachersList}
      />
      <StudentDetail
        data={studentDetails}
        setData={setStudentDetails}
        level={level}
      />
    </Container>
  );
};

type TeacherAndLocationProps = React.FC<{
  data: ScheduleType;
  level: LEVEL_TYPES;
  teachersList: UserType[];
  setData: React.Dispatch<ScheduleType>;
}>;
const TeacherAndLocation: TeacherAndLocationProps = ({
  data,
  level,
  teachersList,
  setData,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const teacherDetails = useMemo(() => {
    return data.teachers.find((f) => f.level === level);
  }, [data, level]);

  const teacherUserInfo = teacherDetails?.teacherId as UserType;

  return (
    <>
      <Box
        mb={5}
        sx={{
          border: "1px solid #D2D2D2",
          width: "100%",
          p: 3,
          borderRadius: 2,
          position: "relative",
        }}
      >
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          gap={2}
          sx={{
            position: "absolute",
            top: 10,
            right: 10,
            cursor: "pointer",
          }}
          onClick={() => {
            setIsOpen(true);
          }}
        >
          <Typography fontSize={14}>Edit</Typography>
          <EditIcon
            sx={{
              fontSize: 16,
            }}
          />
        </Box>
        {teacherUserInfo ? (
          <>
            <TitleAndValue
              title="Assigned Teacher"
              icon={
                <Person2OutlinedIcon
                  sx={{ color: "#6D6D6D", fontSize: "1rem" }}
                />
              }
              value={
                <Box
                  display="flex"
                  flexDirection="row"
                  gap={1}
                  alignItems="center"
                >
                  <Avatar alt="Remy Sharp" sx={{ width: 18, height: 18 }} />
                  <Typography fontSize={14} fontWeight={700}>
                    {teacherUserInfo.firstName} {teacherUserInfo.lastName}
                  </Typography>
                </Box>
              }
            />
            <TitleAndValue
              title="Location"
              icon={
                <FmdGoodOutlinedIcon
                  sx={{ color: "#6D6D6D", fontSize: "1rem" }}
                />
              }
              value={
                <Box
                  display="flex"
                  flexDirection="row"
                  gap={1}
                  alignItems="center"
                >
                  <Typography fontSize={14} fontWeight={700}>
                    {teacherDetails.location}
                  </Typography>
                </Box>
              }
            />
          </>
        ) : (
          <p>No Teacher and location selected</p>
        )}
      </Box>
      {isOpen && (
        <ScheduleModal
          endDate={data.endDate}
          classType={data.type}
          classSubType={data.subType}
          startDate={data.startDate}
          open={isOpen}
          setOpen={setIsOpen}
          data={data}
          setData={setData}
          teachersList={teachersList}
          level={level}
        />
      )}
    </>
  );
};

type TitleAndValueProps = React.FC<{
  title: string;
  value: React.ReactNode;
  icon: React.ReactNode;
}>;
const TitleAndValue: TitleAndValueProps = ({ title, value, icon }) => {
  return (
    <Box
      mb={2}
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: {
          xs: "column",
          sm: "row",
        },
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        gap={2}
        sx={{
          width: {
            xs: "100%",
            sm: "30%",
          },
        }}
      >
        {icon}
        <Typography fontSize="0.85rem" color="#6D6D6D">
          {title}
        </Typography>
      </Box>
      <Box
        sx={{
          width: {
            xs: "100%",
            sm: "70%",
          },
        }}
      >
        {value}
      </Box>
    </Box>
  );
};

const StudentDetail = ({ data, setData, level }) => {
  const students = data.students.filter((f) => f.level === level);
  const [isDeleting, setIsDeleting] = useState(false);
  const { showSnackbar } = useSnackbar();

  console.log("students", students);

  const handleDelete = async (id) => {
    const handleError = () => {
      showSnackbar("Something went wrong while removing student", {
        type: "error",
      });
    };

    try {
      setIsDeleting(true);
      const updatedStudents = students.filter((f) => f._id !== id);
      const payload = {
        id: data._id,
        students: updatedStudents,
        updateStudents: true,
      };
      const { data: respData } = await axiosInstance.put(
        `schedule/update`,
        payload
      );
      if (respData.success && respData?.data?._id) {
        // setData(respData.data);
        setData((prev) => {
          return {
            ...prev,
            students: updatedStudents,
          };
        });
        showSnackbar("Removed students successfully", {
          type: "success",
        });
      } else {
        handleError();
      }
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      handleError();
      console.error("Something went wrong in handleDelete in", error);
    }
  };

  return (
    <Box width="100%" aria-disabled={isDeleting}>
      <Typography fontSize={14} fontWeight={700} mb={4}>
        Student Details
      </Typography>

      {students.length > 0 ? (
        <Box display="flex" flexDirection="column" width="100%">
          <Box
            display="flex"
            flexDirection="row"
            width="100%"
            sx={{
              background: "#B1F3B1",
              borderTopLeftRadius: 10,
              borderTopRightRadius: 10,
              border: "1px solid #000",
              overflow: "hidden",
              fontWeight: "600",
            }}
          >
            <Cell>Name</Cell>
            <Cell>Email</Cell>
            <Cell>Plan Activated on</Cell>
            <Cell>Action</Cell>
          </Box>
          {students.map((m, i) => {
            const style = (() => {
              if (i === students.length - 1) {
                return {
                  borderBottomLeftRadius: 10,
                  borderBottomRightRadius: 10,
                };
              } else {
                return {};
              }
            })();
            const userDetails = m.userId as UserType;

            return (
              <Box
                key={i}
                display="flex"
                flexDirection="row"
                width="100%"
                sx={{
                  border: "1px solid #000",
                  borderTop: "none",
                  // borderBottom: "none",
                  overflow: "hidden",
                  ...style,
                }}
              >
                <Cell>{getUserFullName(userDetails)}</Cell>
                <Cell>{userDetails?.email}</Cell>
                <Cell>
                  {formatDate({
                    date: m.scheduledAt,
                  })}
                  &nbsp;
                  {formatTime({
                    date: m.scheduledAt,
                  })}
                </Cell>
                <Cell>
                  <Button
                    onClick={() => {
                      handleDelete(m._id);
                    }}
                  >
                    Remove
                  </Button>
                </Cell>
              </Box>
            );
          })}
        </Box>
      ) : (
        <Typography textAlign="center" fontSize="0.8rem">
          No Students schedules for this level
        </Typography>
      )}
    </Box>
  );
};

const Cell = ({ children }) => {
  return (
    <Box
      width="25%"
      sx={{
        border: "1px solid #000",
        borderTop: "none",
        borderBottom: "none",
        p: 1,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        fontSize: "0.95rem",
      }}
    >
      {children}
    </Box>
  );
};

export default Students;
