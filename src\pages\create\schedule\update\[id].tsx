import EventBodyLoading from "@/components/Create/event/Loading";
import CreateScheduleBody from "@/components/Create/schedule/CreateScheduleBody";
import { ROLE_TYPES } from "@/constant/Enums";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";

export async function getServerSideProps() {
  const { data } = await axiosInstance.post(
    `user/get-users`,
    {
      role: ROLE_TYPES.TEAHCER,
    },
    {
      params: {
        skip: 0,
        limit: 30,
      },
    }
  );
  const teachersList = data.data;
  return { props: { teachersList } };
}

const UpdateSchedule = ({ teachersList }) => {
  const { showSnackbar } = useSnackbar();
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState(null);
  const router = useRouter();
  const id = router?.query?.id;

  const fetchSchedule = async (scheduleId: string) => {
    try {
      setIsLoading(true);
      const { data } = await axiosInstance.get(`schedule/get/${scheduleId}`, {
        params: {
          needEventImage: true,
        },
      });
      if (data.isError) {
        router.back();
        showSnackbar("Failed to fetch schedule", {
          type: "error",
        });
      } else {
        setData(data.data);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchSchedule(String(id));
    }
  }, [id]);

  if (isLoading) {
    return <EventBodyLoading />;
  }

  return <CreateScheduleBody scheduleData={data} teachersList={teachersList} />;
};

export default UpdateSchedule;
