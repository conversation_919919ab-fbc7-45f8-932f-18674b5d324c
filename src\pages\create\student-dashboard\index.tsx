import DashboardHeader from "@/components/Create/student/DashboardHeader";
import StudentList from "@/components/Create/student/StudentList";
import SearchInput from "@/components/SearchInput";
import { Box, Card, Container, Typography } from "@mui/material";
import axios from "axios";
import React, { useState } from "react";

export async function getServerSideProps() {
  const { data } = await axios.get(
    `${process.env.NEXT_PUBLIC_BASE_URL}api/student/get-student-count`
  );
  const studentCount = data.data;
  return { props: { studentCount } };
}

const StudentDashboard = ({ studentCount }) => {
  const [isSearchLoading, setIsSearchLoading] = useState(true);
  const [tempSearch, setTempSearch] = useState("");
  const [search, setSearch] = useState("");

  const handleSearch = () => {
    setSearch(tempSearch);
  };

  return (
    <Container
      sx={{
        display: "flex",
        flexDirection: "column",
        // alignItems: "center",
        padding: { xs: "1rem", sm: "2rem", md: "4rem" },
      }}
    >
      <DashboardHeader />

      <Box
        display="flex"
        flexDirection="row"
        sx={{
          width: "100%",
        }}
      >
        <Card
          sx={{
            boxShadow: "0px 2px 21.5px 0px #0000001A",
            padding: "1rem 5rem",
          }}
        >
          <Typography textAlign="center" fontWeight={600}>
            Number of Students
          </Typography>
          <Typography textAlign="center" fontWeight={700} fontSize={50}>
            {studentCount}
          </Typography>
        </Card>
      </Box>

      <Typography
        mt={10}
        mb={2}
        textAlign="left"
        fontWeight={800}
        fontSize={20}
      >
        Student List
      </Typography>

      <SearchInput
        searchValue={tempSearch}
        setSearchValue={setTempSearch}
        handleSearch={handleSearch}
        handleClear={() => {
          setSearch("");
          setTempSearch("");
        }}
        isLoading={isSearchLoading}
        disabled={isSearchLoading}
        showLoadingProgress
        placeholder="Search by Student name or email id"
        style={{
          width: {
            xs: "100%",
          },
        }}
      />

      <StudentList
        search={search}
        isLoading={isSearchLoading}
        setIsLoading={setIsSearchLoading}
      />
    </Container>
  );
};

export default StudentDashboard;
