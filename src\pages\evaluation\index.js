import Link from "next/link";
import { Container, <PERSON>, Button, Typography } from "@mui/material";
import Head from "next/head";

const root = "/evaluation";
const evaluationRoutesEnglish = [
  { language: "/english", level: "/beginner" },
  { language: "/english", level: "/intermediate" },
  { language: "/english", level: "/advanced" },
];
const evaluationRoutesSpanish = [
  { language: "/spanish", level: "/beginner" },
  { language: "/spanish", level: "/intermediate" },
  { language: "/spanish", level: "/advanced" },
];

const EvaluationLevels = ({ language, routes }) => {
  return (
    <>
      <Head>
        <title>Evaluation</title>
      </Head>{" "}
      <Box
        sx={{
          m: 4,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <Typography
          variant="h3"
          gutterBottom
          sx={{ fontSize: "2.2em" }}
          color="primary"
        >
          {language.toUpperCase()}
        </Typography>
        <Box>
          {routes.map((route, index) => (
            <Box key={index} sx={{ my: 1 }}>
              <Link href={`${root}${route.language}${route.level}`}>
                <Button
                  variant="understated"
                  sx={{ borderRadius: 0, width: "200px" }}
                >
                  {`${
                    route.level.slice(1).charAt(0).toUpperCase() +
                    route.level.slice(2)
                  }`}
                </Button>
              </Link>
            </Box>
          ))}
        </Box>
      </Box>
    </>
  );
};

export default function EvaluationIndex() {
  return (
    <Container
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        mt: { xs: "40px", md: "50px" },
        gap: 2,
        p: 2,
      }}
    >
      <Box>
        <Typography variant="h2" sx={{ fontSize: "2.75rem" }}>
          Evaluations
        </Typography>
      </Box>
      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          justifyContent: "center",
          alignItems: "center",
          mt: { xs: "10px", md: "20px" },
        }}
      >
        <EvaluationLevels
          routes={evaluationRoutesEnglish}
          language={"English"}
        />
        <EvaluationLevels
          routes={evaluationRoutesSpanish}
          language={"Spanish"}
        />
      </Box>
    </Container>
  );
}
