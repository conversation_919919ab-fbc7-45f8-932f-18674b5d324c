import React, { useEffect, useState } from "react";
import Container from "@mui/material/Container";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import SearchBar from "../../components/events/eventSearchBar";
import { EventSchemaType } from "@/api/mongoTypes";
import { useRouter } from "next/router";
import { useSnackbar } from "@/hooks/useSnackbar";
import InfiniteScroll from "react-infinite-scroll-component";
import { Box, TextField } from "@mui/material";
import EventCard from "@/components/Create/event/EventCard";
import axios from "axios";
import useDebounce from "@/hooks/useDebounce";
import SEO from "@/components/SEO";
import { Metadata } from "@/constant/seoData";
import { EventSchemaWithSingleEvent } from "@/types";

const EventPageLayout = ({ children }) => (
  <Container
    maxWidth="lg"
    sx={{
      padding: { xs: "1rem", sm: "2rem" },
      backgroundColor: "background.paper",
    }}
  >
    <Typography variant="h4" sx={{ marginBottom: "1rem" }}>
      Upcoming Events
    </Typography>
    {children}
  </Container>
);

const LIMIT = 6;

export default function EventPage() {
  const { showSnackbar } = useSnackbar();
  const [skip, setSkip] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);
  const [data, setData] = useState<EventSchemaWithSingleEvent[]>([]);
  const [search, setSearch] = useState("");

  const debouncedSearchTerm = useDebounce(search, 1000);

  const fetchData = async ({ skipCount = 0, search = "" }) => {
    const showLoading = +skipCount === 0;
    if (skipCount === 0) {
      setData([]);
    }
    try {
      if (showLoading) {
        setIsLoading(true);
      }
      const params = {
        needEventImage: true,
        skip: skipCount,
        limit: LIMIT,
        search,
      };
      if (!search) {
        delete params.search;
      }
      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/event/all`,
        {
          params,
        }
      );
      if (data.success && data?.data) {
        const length = data?.data?.length;
        setData((prev) => [...prev, ...data.data]);
        setHasMore(length >= LIMIT);
        setSkip(+skipCount + LIMIT);
      } else {
        showSnackbar("Failed to fetch Events", {
          type: "error",
        });
      }
      if (showLoading) {
        setIsLoading(false);
      }
    } catch (error) {
      console.error(`Something went wrong in fetchData due to ${error}`);
      if (showLoading) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchData({ skipCount: 0, search: debouncedSearchTerm });
  }, [debouncedSearchTerm]);

  return (
    <>
      <SEO
        title={Metadata.CLASSES_PAGE.title}
        description={Metadata.CLASSES_PAGE.description}
        keywords={Metadata.CLASSES_PAGE.keywords.join(",")}
      />
      <EventPageLayout>
        <TextField
          fullWidth
          label="Search Events"
          id="fullWidth"
          value={search}
          onChange={(e) => {
            const value = e.target.value;
            setSearch(value);
          }}
        />
        <Container
          sx={{
            display: "flex",
            flexDirection: "row",
            flexWrap: "wrap",
            alignItems: "center",
          }}
        >
          {isLoading ? (
            <p>fetching events</p>
          ) : (
            <InfiniteScroll
              style={{
                width: "100%",
              }}
              dataLength={data.length}
              next={() => fetchData({ skipCount: skip, search: search })}
              hasMore={hasMore}
              loader={<p style={{ textAlign: "center" }}>Loading</p>}
              endMessage={
                <p style={{ textAlign: "center" }}>
                  {search.length > 0 ? (
                    <p>
                      No event with title <b>{search}</b>
                    </p>
                  ) : (
                    <b>Yay! You have seen it all</b>
                  )}
                </p>
              }
            >
              <Box
                display="flex"
                flexDirection="row"
                alignItems="center"
                justifyContent="center"
                flexWrap="wrap"
                gap={10}
                mt={10}
              >
                {data.map((m, i) => (
                  <EventCard key={m._id} data={m} />
                ))}
              </Box>
            </InfiniteScroll>
          )}
        </Container>
      </EventPageLayout>
    </>
  );
}
