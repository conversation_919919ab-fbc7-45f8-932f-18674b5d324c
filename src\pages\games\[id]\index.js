import Link from "next/link";

const CategoryPage = ({ videos }) => {
  return (
    <div>
      <h1>a singleee category</h1>;
      <div>
        <h3>
          {videos.map((video) => (
            <div key={`${video.id}-v`}>
              <Link key={video.id} href="videos/video1">
                {video.category}
              </Link>
            </div>
          ))}
        </h3>
      </div>
    </div>
  );
};

export default CategoryPage;

export async function getServerSideProps() {
  const res = await fetch("http://localhost:3000/api/getCategory");
  const all_videos = await res.json();

  return {
    props: {
      id: "an id",
      videos: all_videos.videos.all_videos || [],
    },
  };
}
