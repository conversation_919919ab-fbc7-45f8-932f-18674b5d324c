import { useEffect, useState, useCallback } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>t } from "../../../wordl/context/AlertContext";
import { ClockIcon } from "@heroicons/react/outline";
import { format } from "date-fns";
import { default as GraphemeSplitter } from "grapheme-splitter";
import Button from "@mui/material/Button";
import { AlertContainer } from "../../../components/wordl/alerts/AlertContainer";
import { Grid } from "../../../components/wordl/grid/Grid";
import { Keyboard } from "../../../components/wordl/keyboard/Keyboard";
import { InfoModal } from "@/components/wordl/modals/InfoModal";
import { StatsModal } from "@/components/wordl/modals/StatsModal";
import { Navbar } from "@/components/wordl/navbar/Navbar";
import Head from "next/head";
import {
  DATE_LOCALE,
  LONG_ALERT_TIME_MS,
  MAX_CHALLENGES,
  REVEAL_TIME_MS,
  WELCOME_INFO_MODAL_MS,
} from "../../../wordl/constants/settings";
import {
  CORRECT_WORD_MESSAGE,
  GAME_COPIED_MESSAGE,
  NOT_ENOUGH_LETTERS_MESSAGE,
  SHARE_FAILURE_TEXT,
  WIN_MESSAGES,
  WORD_NOT_FOUND_MESSAGE,
} from "../../../wordl/constants/strings";
import {
  getStoredIsHighContrastMode,
  loadGameStateFromLocalStorage,
  saveGameStateToLocalStorage,
} from "../../../wordl/lib/localStorage";
import { addStatsForCompletedGame, loadStats } from "../../../wordl/lib/stats";
import {
  getGameDate,
  getIsLatestGame,
  getSolution,
  isWinningWord,
  isWordInWordList,
  unicodeLength,
  unicodeSplit,
} from "../../../wordl/lib/words";

const GameComponentPure = () => {
  const isLatestGame = getIsLatestGame();
  const gameDate = getGameDate();
  const { showError: showErrorAlert, showSuccess: showSuccessAlert } =
    useAlert();
  const [reveal, setReveal] = useState(false);
  const [currentGuess, setCurrentGuess] = useState("");
  const [isGameWon, setIsGameWon] = useState(false);
  const [isInfoModalOpen, setIsInfoModalOpen] = useState(false);
  const [isStatsModalOpen, setIsStatsModalOpen] = useState(false);
  const [currentRowClass, setCurrentRowClass] = useState("");
  const [isGameLost, setIsGameLost] = useState(false);
  const [isShowSolution, setIsShowSolution] = useState(false);
  const [solution, setSolution] = useState("");
  const [isHighContrastMode, setIsHighContrastMode] = useState(
    getStoredIsHighContrastMode()
  );
  const [isRevealing, setIsRevealing] = useState(false);
  const [guesses, setGuesses] = useState(() => {
    const loaded = loadGameStateFromLocalStorage(isLatestGame);
    if (loaded?.solution !== solution) {
      return [];
    }
    const splitSolution = unicodeSplit(solution);
    const gameWasWon = loaded.guesses.includes(splitSolution.join(""));

    if (gameWasWon) {
      setIsGameWon(true);
    }
    if (loaded.guesses.length === MAX_CHALLENGES && !gameWasWon) {
      setIsGameLost(true);
      showErrorAlert(CORRECT_WORD_MESSAGE(solution), {
        durationMs: 6000,
      });
    }
    return loaded.guesses;
  });

  const [stats, setStats] = useState(() => loadStats());

  const [isHardMode, setIsHardMode] = useState(
    typeof window != "undefined" && localStorage.getItem("gameMode")
      ? localStorage.getItem("gameMode") === "hard"
      : false
  );

  useEffect(() => {
    if (!loadGameStateFromLocalStorage(true)) {
      setTimeout(() => {
        setIsInfoModalOpen(true);
      }, WELCOME_INFO_MODAL_MS);
    }
    setSolution(getSolution().solution);
  }, []);

  useEffect(() => {
    if (isHighContrastMode) {
      document.documentElement.classList.add("high-contrast");
    } else {
      document.documentElement.classList.remove("high-contrast");
    }
  }, [isHighContrastMode]);

  const clearCurrentRowClass = () => {
    setCurrentRowClass("");
  };

  const handleRestart = useCallback(() => {
    setGuesses([]);
    loadStats();
    setIsGameLost(false);
    setIsGameWon(false);
    setSolution(getSolution().solution);
    setIsShowSolution(false);
    setCurrentGuess("");
    setReveal(false);
    localStorage.clear();
  }, []);

  useEffect(() => {
    saveGameStateToLocalStorage(getIsLatestGame(), { guesses, solution });
  }, [guesses, solution]);

  useEffect(() => {
    if (isGameWon) {
      const winMessage =
        WIN_MESSAGES[Math.floor(Math.random() * WIN_MESSAGES.length)];
      const delayMs = REVEAL_TIME_MS * solution.length;

      showSuccessAlert(winMessage, {
        delayMs,
        onClose: () => setIsStatsModalOpen(true),
      });
    }

    if (isGameLost) {
      setTimeout(() => {
        setIsStatsModalOpen(true);
      }, (solution.length + 1) * REVEAL_TIME_MS);
    }
  }, [isGameWon, isGameLost, showSuccessAlert, solution.length, handleRestart]);

  const onChar = (value) => {
    if (
      unicodeLength(`${currentGuess}${value}`) <= solution.length &&
      guesses.length < MAX_CHALLENGES &&
      !isGameWon
    ) {
      setCurrentGuess(`${currentGuess}${value}`);
    }
  };

  const onDelete = () => {
    setCurrentGuess(
      new GraphemeSplitter().splitGraphemes(currentGuess).slice(0, -1).join("")
    );
  };

  const onShowSolution = () => {
    setReveal(true);
    setStats(addStatsForCompletedGame(stats, guesses.length + 1));
    setIsStatsModalOpen(true);
  };

  const onEnter = () => {
    if (isGameWon || isGameLost || reveal) {
      return;
    }

    if (!(unicodeLength(currentGuess) === solution.length)) {
      setCurrentRowClass("jiggle");
      return showErrorAlert(NOT_ENOUGH_LETTERS_MESSAGE, {
        onClose: clearCurrentRowClass,
      });
    }

    if (!isWordInWordList(currentGuess)) {
      setCurrentRowClass("jiggle");
      setCurrentGuess("");
      return showErrorAlert(WORD_NOT_FOUND_MESSAGE, {
        onClose: clearCurrentRowClass,
      });
    }

    setIsRevealing(true);
    setTimeout(() => {
      setIsRevealing(false);
    }, REVEAL_TIME_MS * solution.length);

    const winningWord = isWinningWord(currentGuess);
    if (
      unicodeLength(currentGuess) === solution.length &&
      guesses.length < MAX_CHALLENGES &&
      !isGameWon
    ) {
      setGuesses([...guesses, currentGuess]);
      setCurrentGuess("");

      if (winningWord) {
        if (isLatestGame) {
          setStats(addStatsForCompletedGame(stats, guesses.length));
        }
        return setIsGameWon(true);
      }

      if (guesses.length === MAX_CHALLENGES - 1) {
        if (isLatestGame) {
          setStats(addStatsForCompletedGame(stats, guesses.length + 1));
        }
        setIsGameLost(true);
        showErrorAlert(CORRECT_WORD_MESSAGE(solution), {
          durationMs: 6000,
        });
      }
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex h-full flex-col bg-game">
        <Navbar
          setIsInfoModalOpen={setIsInfoModalOpen}
          // setIsStatsModalOpen={setIsStatsModalOpen}
        />
        {!isLatestGame && (
          <div className="flex items-center justify-center">
            <ClockIcon className="h-6 w-6 stroke-gray-600 " />
            <p className="text-base text-gray-600 ">
              {format(gameDate, "d MMMM yyyy", { locale: DATE_LOCALE })}
            </p>
          </div>
        )}

        <div className="">
          <div className="flex grow flex-col justify-center items-center pb-6 short:pb-2">
            <Grid
              solution={solution}
              guesses={guesses}
              currentGuess={currentGuess}
              isRevealing={isRevealing}
              currentRowClassName={currentRowClass}
            />
            <div className="flex justify-center mt-4">
              <Button
                onClick={handleRestart}
                className="ml-2 text-white bg-[#F3B358] hover:bg-[#8FB583]"
                variant="contained"
                color="primary"
              >
                Restart
              </Button>
              <Button
                onClick={onShowSolution}
                className="ml-2 bg-[#F3B358] hover:bg-[#8FB583] text-white"
                variant="contained"
              >
                Show Answer
              </Button>
            </div>
            {isShowSolution && (
              <div className="flex flex-col item-center bg-[#ed7da0] rounded-xl text-white p-8 gap-1 mt-4">
                <div>The correct answer is</div>
                <div className="text-white text-4xl font-bold">{solution}</div>
              </div>
            )}
          </div>

          <InfoModal
            isOpen={isInfoModalOpen}
            handleClose={() => setIsInfoModalOpen(false)}
          />
          <StatsModal
            isOpen={isStatsModalOpen}
            handleClose={() => {
              setIsStatsModalOpen(false);
              setTimeout(() => {
                handleRestart();
              }, 1000);
            }}
            solution={solution}
            guesses={guesses}
            gameStats={stats}
            isLatestGame={isLatestGame}
            isGameLost={isGameLost}
            isGameWon={isGameWon}
            handleShareToClipboard={() => showSuccessAlert(GAME_COPIED_MESSAGE)}
            handleShareFailure={() =>
              showErrorAlert(SHARE_FAILURE_TEXT, {
                durationMs: LONG_ALERT_TIME_MS,
              })
            }
            handleMigrateStatsButton={() => {
              setIsStatsModalOpen(false);
            }}
            isHardMode={isHardMode}
            isHighContrastMode={isHighContrastMode}
            numberOfGuessesMade={guesses.length}
          />
          <AlertContainer />
        </div>
      </div>
      <Keyboard
        onChar={onChar}
        onDelete={onDelete}
        onEnter={onEnter}
        solution={solution}
        guesses={guesses}
        isRevealing={isRevealing}
      />
    </div>
  );
};

const GameComponent = () => {
  return (
    <>
      <Head>
        <title>Adivina Game</title>
      </Head>
      <AlertProvider>
        <GameComponentPure />
      </AlertProvider>
    </>
  );
};

export default GameComponent;
