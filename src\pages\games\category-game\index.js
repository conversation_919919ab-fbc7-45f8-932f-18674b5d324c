import React, { useState, useEffect } from "react";
import WordCategoryGrid from "@/components/wordCategoryGame/WordCategoryGrid";
import { WordTileButtons } from "@/components/wordTileGame/wordTileButtons";
import { SuccessModal } from "@/components/wordTileGame/wordTileSuccessModal";
import { RevealModal } from "@/components/wordTileGame/wordTileRevealModal";
import { Container, Box, Typography } from "@mui/material";
import Head from "next/head";

const wrapperHeight = 400;

export default function WordCategoryGame({ data }) {
  const [games, setGames] = useState(data.games);
  const [gamesIndex, setGamesIndex] = useState(0);
  const [curGameIx, setCurGameIx] = useState(0);
  const [curGameData, setCurGameData] = useState(null);
  const [tilesInRow, setTilesInRow] = useState(3);
  const [dropdownLevel, setDropdownLevel] = useState("beginner");
  const [success, setSuccess] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [isRevealModalOpen, setIsRevealModalOpen] = useState(false);

  const shuffleArray = (array) => {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  };

  const handleSuccess = () => {
    setSuccess(true);
    if (isSuccessModalOpen) {
      return;
    } else {
      setTimeout(() => {
        setIsSuccessModalOpen(true);
      }, 1000);
    }
  };

  const handleNext = () => {
    setSuccess(false);
    const gamesLength = games.length;
    const isLast = gamesLength === gamesIndex + 1;
    if (isLast) {
    } else {
      setGamesIndex((prevIndex) => (prevIndex + 1) % gamesLength);
    }
  };

  const handleReveal = () => {
    setIsRevealModalOpen(true);
  };

  useEffect(() => {
    const shuffleGameData = (newCurGame) => {
      const words = newCurGame.groups.flatMap((group) => group.words);
      const shuffledWords = shuffleArray(words);
      return shuffledWords;
    };
    const newCurGame = data.games[curGameIx];
    const shuffledWords = shuffleGameData(newCurGame);
    // setCurGameData(newCurGame);
    // shuffleGameData(newCurGame);
  }, [data]);

  return (
    <>
      <Head>
        <title>Category Game</title>
      </Head>
      <Container
        maxWidth="md"
        className="flex flex-col"
        style={{
          margin: "0 auto",
          padding: 0,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            width: "100%",
            height: "100%",
            margin: { xs: "15px 10px 15px 10px", sm: "25px 10px 20px 10px" },
          }}
        >
          <Typography
            component="span"
            sx={{
              fontWeight: 700,
              fontSize: { xs: "2.2rem", sm: "2.5rem", md: "2.7rem" },
              textAlign: "center",
              userSelect: "none",
            }}
          >
            Find the groups
          </Typography>
        </Box>
        <Box sx={{ marginTop: { xs: "7px", sm: "45px" } }}>
          <WordCategoryGrid
            key={`${gamesIndex}-${dropdownLevel}-${tilesInRow}`}
            gameData={curGameData}
            handleSuccess={handleSuccess}
          />
        </Box>
        <WordTileButtons handleNext={handleNext} handleReveal={handleReveal} />
        <SuccessModal
          isOpen={isSuccessModalOpen}
          handleClose={() => setIsSuccessModalOpen(false)}
          answer={curGameData}
          translation={curGameData}
        />
        <RevealModal
          isOpen={isRevealModalOpen}
          handleClose={() => setIsRevealModalOpen(false)}
          answer={curGameData}
          translation={curGameData}
        />
      </Container>
    </>
  );
}

export async function getStaticProps() {
  const fs = require("fs");
  const path = require("path");

  const filePath = path.resolve("./data/category_game_data.json");
  const fileContent = fs.readFileSync(filePath, "utf8");
  const data = JSON.parse(fileContent);

  return {
    props: {
      data,
    },
  };
}
