import Link from "next/link";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Ty<PERSON>graphy,
  Card,
  CardContent,
  CardActionArea,
  CardMedia,
} from "@mui/material";
import Head from "next/head";
import SEO from "@/components/SEO";
import { Metadata } from "@/constant/seoData";

const root = "/games";
const gameRoutes = [
  {
    game: "adivina-game",
    name: "Adivina La Palabra",
    src: "/images/gameImages/adivinaImage.jpg",
  },
  {
    game: "tile-game",
    name: "Mosiaca",
    src: "/images/gameImages/mosiacoImage.jpg",
  },
  {
    game: "3d-text",
    name: "Universo",
    src: "/images/gameImages/universeImage.jpg",
  },
  // Additional games can be added here
];

const GameOptions = ({ routes }) => {
  return (
    <Grid
      container
      spacing={2}
      sx={{
        marginTop: 2,
        justifyContent: "center",
      }}
    >
      {routes.map((route, index) => (
        <Grid item key={index} xs={12} sm={6} md={4} lg={3} m={2}>
          <Card
            sx={{
              height: 300,
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
              transition: "transform 0.3s, box-shadow 0.3s",
              "&:hover": {
                transform: "translateY(-5px)",
                boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.2)",
              },
            }}
          >
            <CardActionArea component={Link} href={`${root}/${route.game}`}>
              <CardMedia
                component="img"
                height="230"
                objectFit="cover"
                image={route.src}
                alt={route.name}
              />
              <CardContent>
                <Button variant="contained" sx={{ width: "100%" }}>
                  {route.name}
                </Button>
              </CardContent>
            </CardActionArea>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default function GameIndex() {
  return (
    <>
      <SEO
        title={Metadata.GAMES_PAGE.title}
        description={Metadata.GAMES_PAGE.description}
        keywords={Metadata.GAMES_PAGE.keywords.join(",")}
      />
      <Container
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          mt: { xs: "40px", md: "50px" },
          gap: 2,
          p: 2,
        }}
      >
        <Typography variant="h2" sx={{ fontSize: "2.75rem", marginBottom: 2 }}>
          Word Games
        </Typography>
        <GameOptions routes={gameRoutes} />
      </Container>
    </>
  );
}
