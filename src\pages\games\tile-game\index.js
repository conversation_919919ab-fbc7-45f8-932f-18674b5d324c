import React, { useState } from "react";
import WordTileGrid from "../../../components/wordTileGame/wordTileGrid";
import { WordTileButtons } from "../../../components/wordTileGame/wordTileButtons";
import { WordTileDrawer } from "../../../components/wordTileGame/wordTileDrawer";
import { WordTileDropdowns } from "../../../components/wordTileGame/wordTileDropdowns";
import { SuccessModal } from "../../../components/wordTileGame/wordTileSuccessModal";
import { RevealModal } from "../../../components/wordTileGame/wordTileRevealModal";
import {
  levelOptions,
  typeOptions,
} from "../../../components/wordTileGame/wordTileConstants/dropdownOptions";
import { Container, Box, Typography } from "@mui/material";
import Head from "next/head";

const wrapperHeight = 400;

export default function WordTileGame({ data }) {
  const [phrases, setPhrases] = useState(data.poems);
  const [phraseIndex, setPhraseIndex] = useState(0);
  const [tilesInRow, setTilesInRow] = useState(3);
  const [dropdownLevel, setDropdownLevel] = useState("beginner");
  const [dropdownType, setDropdownType] = useState("poems");
  const [success, setSuccess] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [isRevealModalOpen, setIsRevealModalOpen] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const handleSuccess = () => {
    setSuccess(true);
    if (isSuccessModalOpen) {
      return;
    } else {
      setTimeout(() => {
        setIsSuccessModalOpen(true);
      }, 1000);
    }
  };

  const handleNext = () => {
    setSuccess(false);
    const phrasesLength = phrases[dropdownLevel].length;
    const isLast = phrasesLength === phraseIndex + 1;
    if (isLast && dropdownLevel === "advanced") {
      graduateType();
    } else if (isLast) {
      graduateLevel();
    } else {
      setPhraseIndex((prevIndex) => (prevIndex + 1) % phrasesLength);
    }
  };

  const graduateType = () => {
    let newType;
    if (dropdownType === "sayings") {
      newType = "lyrics";
    } else if (dropdownType === "lyrics") {
      newType = "poems";
    } else {
      newType = "sayings";
    }
    setDropdownType(newType);
  };

  const graduateLevel = () => {
    setPhraseIndex(0);
    let newLevel;
    if (dropdownLevel === "beginner") {
      newLevel = "intermediate";
    } else if (dropdownLevel === "intermediate") {
      newLevel = "advanced";
    } else {
      graduateType();
    }
    setDropdownLevel(newLevel);
  };

  const handleReveal = () => {
    setIsRevealModalOpen(true);
  };

  const handleTilesInRowChange = (event) => {
    setTilesInRow(event.target.value);
  };

  const handleLevelChange = (event) => {
    setPhraseIndex(0);
    setDropdownLevel(event.target.value);
  };

  const handleTypeChange = (event) => {
    const newType = event.target.value;
    setPhraseIndex(0);
    setDropdownType(newType);
    setPhrases(data[newType]);
  };

  const handleLevelClick = (value) => {
    setPhraseIndex(0);
    setDropdownLevel(value);
  };

  const handleTypeClick = (value) => {
    setPhraseIndex(0);
    setDropdownType(value);
    setPhrases(data[value]);
  };

  const handleDrawerOpen = () => {
    setIsDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setIsDrawerOpen(false);
  };

  const curPhrase = phrases[dropdownLevel][phraseIndex];

  const calculateTileHeight = (phrase) => {
    const totalTiles = phrase.split(" ").length;
    const rows = Math.ceil(totalTiles / tilesInRow);
    const height = Math.floor(wrapperHeight / rows) - 6;
    return height < 90 ? height : 90;
  };

  return (
    <>
      <Head>
        <title>Tile Game</title>
      </Head>
      <Container
        maxWidth="md"
        className="flex flex-col"
        style={{
          margin: "0 auto",
          padding: 0,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            width: "100%",
            height: "100%",
            margin: { xs: "15px 10px 15px 10px", sm: "25px 10px 20px 10px" },
          }}
        >
          <Typography
            component="span"
            sx={{
              fontWeight: 700,
              fontSize: { xs: "2.2rem", sm: "2.5rem", md: "2.7rem" },
              textAlign: "center",
              userSelect: "none",
            }}
          >
            Mosaico de Palabras
          </Typography>
        </Box>
        <WordTileDropdowns
          tilesInRow={tilesInRow}
          handleTilesInRowChange={handleTilesInRowChange}
          dropdownLevel={dropdownLevel}
          handleLevelChange={handleLevelChange}
          dropdownType={dropdownType}
          handleTypeChange={handleTypeChange}
          levelOptions={levelOptions}
          typeOptions={typeOptions}
        />
        <WordTileDrawer
          isOpen={isDrawerOpen}
          onClose={handleDrawerClose}
          onOpen={handleDrawerOpen}
          levelOptions={levelOptions}
          typeOptions={typeOptions}
          handleLevelClick={handleLevelClick}
          handleTypeClick={handleTypeClick}
        />
        <Box sx={{ marginTop: { xs: "7px", sm: "45px" } }}>
          <WordTileGrid
            key={`${phraseIndex}-${dropdownLevel}-${dropdownType}-${tilesInRow}`}
            phrase={curPhrase.spanish}
            handleSuccess={handleSuccess}
            tilesInRow={tilesInRow}
            tileHeight={calculateTileHeight(curPhrase.spanish)}
            wrapperHeight={wrapperHeight}
          />
        </Box>
        <WordTileButtons
          handleNext={handleNext}
          handleReveal={handleReveal}
          handleDrawerOpen={handleDrawerOpen}
        />
        <SuccessModal
          isOpen={isSuccessModalOpen}
          handleClose={() => setIsSuccessModalOpen(false)}
          answer={curPhrase.spanish}
          translation={curPhrase.english}
        />
        <RevealModal
          isOpen={isRevealModalOpen}
          handleClose={() => setIsRevealModalOpen(false)}
          answer={curPhrase.spanish}
          translation={curPhrase.english}
        />
      </Container>
    </>
  );
}

export async function getStaticProps() {
  const fs = require("fs");
  const path = require("path");

  const filePath = path.resolve("./data/dichos.json");
  const fileContent = fs.readFileSync(filePath, "utf8");
  const data = JSON.parse(fileContent);

  return {
    props: {
      data,
    },
  };
}
