import CalenderClass from "@/components/Profile/dashboard/CalenderClass";
import PurchasesStats from "@/components/Profile/dashboard/PurchasesStats";
import RecentPurchases from "@/components/Profile/dashboard/RecentPurchases";
import Suggestions from "@/components/Profile/dashboard/Suggestions";
import UserDetails from "@/components/Profile/dashboard/UserDetails";
import YourClasses from "@/components/Profile/dashboard/YourClasses";
import axiosInstance from "@/utils/interceptor";
import { getAuth } from "@clerk/nextjs/server";
import { Box, Container } from "@mui/material";
import { GetServerSideProps } from "next";
import React, { useEffect, useState } from "react";

const containerStyles = { marginTop: "30px", height: "100%" };

export const getServerSideProps: GetServerSideProps = async (context) => {
  const response = {
    userDetails: null,
    stats: null,
    purchases: null,
  };

  try {
    const { userId } = getAuth(context.req);

    const { data: userData } = await axiosInstance.post("user/get-user", {
      clerkId: userId,
      populateAll: true,
    });

    const userDetails = userData.data;
    response.userDetails = userDetails;
    console.log("userDetails", userDetails);

    const headers = { headers: { userid: userDetails._id } };

    const [statsRes, purchasesRes] = await Promise.allSettled([
      axiosInstance.get("payment/purchases/stats", headers),
      axiosInstance.get("payment/purchases/bought-list", {
        headers: {
          userid: userDetails._id,
        },
        params: {
          isAscending: false,
          isMyClassesOnly: true,
          skip: 0,
          limit: 5,
        },
      }),
    ]);

    if (statsRes.status === "fulfilled") {
      response.stats = statsRes.value.data.data;
    }

    if (purchasesRes.status === "fulfilled") {
      response.purchases = purchasesRes.value.data.data;
    }
  } catch (error) {
    console.log("error in getServerSideProps", error);
  }

  return { props: response };
};

const ProfileDashboard = ({ userDetails, stats, purchases }) => {
  const [userStats, setUserStats] = useState(stats);

  useEffect(() => {
    setUserStats(stats);
  }, [stats]);

  if (!userDetails) {
    return null;
  }

  return (
    <Container maxWidth="lg" sx={containerStyles}>
      <Box
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: {
            xs: "column",
            md: "row",
          },
          gap: {
            xs: 5,
            md: 5,
          },
        }}
      >
        <Box
          sx={{
            width: {
              xs: "100%",
              md: "60%",
            },
          }}
        >
          <UserDetails userDetails={userDetails} />
          <PurchasesStats stats={userStats} />
        </Box>
        <Box
          sx={{
            width: {
              xs: "100%",
              md: "40%",
            },
          }}
        >
          <CalenderClass />
        </Box>
      </Box>
      <YourClasses />
      <RecentPurchases purchases={purchases} setUserStats={setUserStats} />
      <Suggestions />
    </Container>
  );
};

export default ProfileDashboard;
