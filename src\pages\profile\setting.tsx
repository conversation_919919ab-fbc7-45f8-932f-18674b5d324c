import { UserType } from "@/api/mongoTypes";
import { useUserContext } from "@/contexts/UserContext";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { getAuth } from "@clerk/nextjs/server";
import { Box, Button, Container, Typography } from "@mui/material";
import { GetServerSideProps } from "next";
import { useRouter } from "next/router";
import React, { useEffect } from "react";

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  try {
    const { userId } = getAuth(ctx.req);
    const { data } = await axiosInstance.post("user/get-user", {
      clerkId: userId,
    });
    let stripeUrl = "";
    let stripeMexicanUrl = "";
    const userDetaills = data.data as UserType;
    if (userDetaills) {
      const stripeCustomerId = userDetaills.stripeCustomerId;
      const stripeCustomerIdMxn = userDetaills.stripeCustomerIdMxn;

      const { data: stripeData } = await axiosInstance.get(
        `stripe/portal-link/${stripeCustomerId}`
      );
      stripeUrl = stripeData?.data ?? "";

      const { data: stripeResp } = await axiosInstance.get(
        `stripe/portal-link/${stripeCustomerIdMxn}`
      );
      stripeMexicanUrl = stripeResp?.data ?? "";
    }
    return {
      props: {
        stripeUrl,
        stripeMexicanUrl,
        isError: false,
      },
    };
  } catch (error) {
    return {
      props: {
        stripeUrl: "",
        stripeMexicanUrl: "",
        isError: true,
      },
    };
  }
};

const Setting = ({ stripeUrl, isError, stripeMexicanUrl }) => {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();

  useEffect(() => {
    if (isError || !stripeUrl) {
      showSnackbar("Something went wrong.Please reload the page", {
        type: "error",
      });
    }
  }, [isError, stripeUrl]);

  return (
    <Container
      maxWidth="lg"
      sx={{
        padding: { xs: "1rem", sm: "2rem" },
        backgroundColor: "background.paper",
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
      >
        <Typography variant="h4" sx={{ marginBottom: "1rem" }}>
          Billing
        </Typography>
        <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
          <Button
            disabled={!stripeUrl || isError}
            onClick={() => {
              if (stripeUrl) {
                router.push(stripeUrl);
              }
            }}
          >
            View Billing
          </Button>
          {stripeMexicanUrl && (
            <Button
              disabled={!stripeMexicanUrl || isError}
              onClick={() => {
                if (stripeMexicanUrl) {
                  router.push(stripeMexicanUrl);
                }
              }}
              sx={{
                textWrap: "nowrap",
                width: "fit-content",
              }}
            >
              View Billing (MXN)
            </Button>
          )}
        </Box>
      </Box>
    </Container>
  );
};

export default Setting;
