import React, { useEffect } from "react";
import ClerkSignIn from "@/components/Clerk/ClerkSignIn";
import { Container } from "@mui/material";
import SEO from "@/components/SEO";
import { Metadata } from "@/constant/seoData";
import { useUserContext } from "@/contexts/UserContext";
import { handleVisitor } from "@/utils/common";

export default function SignInPage() {
  const { dbUser } = useUserContext();
  const userId = dbUser?._id;

  useEffect(() => {
    if (!userId) {
      handleVisitor();
    }
  }, [userId]);

  return (
    <>
      <SEO
        title={Metadata.AUTH_PAGE.title}
        description={Metadata.AUTH_PAGE.description}
        keywords={Metadata.AUTH_PAGE.keywords.join(",")}
      />
      <Container
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "600px",
        }}
      >
        <div style={{}}>
          <ClerkSignIn />
        </div>
      </Container>
    </>
  );
}
