import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { VideoPlayer } from "../../../components/VideoPlayer";

const src = "https://media.w3.org/2010/05/sintel/trailer_hd.mp4";

const VideoPage = () => {
  const [videoPlayerState, setVideoPlayerState] = useState();
  const [videoPlayer, setVideoPlayer] = useState();
  const router = useRouter();
  const { query } = router;

  useEffect(() => {
    if (!src) {
      setVideoPlayerState(undefined);
    }
  }, []);

  return (
    <div>
      <h1>a single [category] page</h1>
      <div style={{ maxWidth: "1000px", margin: "auto" }}>
        <VideoPlayer
          src={src}
          onPlayerChange={(videoPlayer) => {
            setVideoPlayer(videoPlayer);
          }}
          onChange={(videoPlayerState) => {
            setVideoPlayerState(videoPlayerState);
          }}
        />
      </div>
    </div>
  );
};
export default VideoPage;
