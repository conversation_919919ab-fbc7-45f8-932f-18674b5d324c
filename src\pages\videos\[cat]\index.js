import React, { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import LikeComponent from "@/../../src/components/dashboard/LikeComponent";
import { VideoPlayer } from "../../../components/VideoPlayer";
import Image from "next/image";
import avatar from "@/../../public/images/profile/earth.webp";
import TagComponent from "@/../../src/components/dashboard/TagComponent";
import Head from "next/head";

const src = "https://media.w3.org/2010/05/sintel/trailer_hd.mp4";
// this is temporary - need to pass in image url from db
const tags = ["TECH", "POLITENESS", "ADVICE", "ART"];

const titleContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginTop: "30px",
  padding: { xs: "0px 10px", sm: "0px" },
};

const titleStyles = { fontWeight: "700", fontSize: "32px", color: "#3C3C3C" };

const avatarTagContainerStyles = {
  display: "flex",
  flexDirection: {
    xs: "column",
    sm: "row",
  },
  justifyContent: "space-between",
  alignItems: { xs: "start", sm: "center" },
  padding: { xs: "0px 10px", sm: "0px" },
};
const avatarContainerStyles = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "white",
  borderRadius: "30px",
  margin: "10px 0px",
  padding: "10px 10px",
  boxShadow: "0px 6px 48px 0px #0000000D",
};

const avatarTitleStyles = {
  fontSize: "16px",
  fontWeight: "500",
  color: "#717171",
  padding: "0px 15px",
  minWidth: "280px",
};

const tagStyles = {
  color: "white",
  backgroundColor: "#72CAC4",
  fontSize: "13px",
  fontWeight: "500",
  marginLeft: { xs: "0px", sm: "15px" },
  marginBottom: { xs: "10px", sm: "0px" },
};

const tagContainerStyles = {
  display: "flex",
  width: { xs: "100%", sm: "auto" },
  flexDirection: "row",
  justifyContent: "space-evenly",
};
const CategoryPage = ({ videos }) => {
  const [videoPlayerState, setVideoPlayerState] = useState();
  const [videoPlayer, setVideoPlayer] = useState();
  const router = useRouter();
  const { query } = router;

  useEffect(() => {
    if (!src) {
      setVideoPlayerState(undefined);
    }
  }, []);
  return (
    <div>
      <Head>
        <title>{query.cat}</title>
      </Head>
      <div style={{ maxWidth: "1000px", margin: "auto" }}>
        <Box sx={titleContainerStyles}>
          <Typography sx={titleStyles}>{query.cat}</Typography>
          <LikeComponent></LikeComponent>
        </Box>
        <Box sx={avatarTagContainerStyles}>
          <Box sx={avatarContainerStyles}>
            <Image
              src={avatar}
              width={40}
              height={40}
              responsive
              alt={`icon image of of ${query.cat}`}
            />{" "}
            <Typography sx={avatarTitleStyles}>John Doe</Typography>
          </Box>
          <Box sx={tagContainerStyles}>
            {tags.map((tag) => (
              <TagComponent key={tag} label={tag} tagStyles={tagStyles} />
            ))}
          </Box>
        </Box>
        <VideoPlayer
          src={src}
          onPlayerChange={(videoPlayer) => {
            setVideoPlayer(videoPlayer);
          }}
          onChange={(videoPlayerState) => {
            setVideoPlayerState(videoPlayerState);
          }}
        />
      </div>
    </div>
  );
};

export default CategoryPage;

export async function getServerSideProps() {
  const res = await fetch("http://localhost:3000/api/getCategory");
  const all_videos = await res.json(); //Will be only videos by category

  return {
    props: {
      id: "an id",
      videos: all_videos.videos.all_videos || [],
    },
  };
}
