import React, { useState } from "react";
import Container from "@mui/material/Container";
import FilterTabs from "../../components/FilterTabs";
import FilterSelect from "../../components/FilterSelect";
import VideoCard from "../../components/videoCard";
import Grid from "@mui/material/Grid";
import { ConstructionModal } from "@/components/Modals/ConstructionModal";

const auth = {
  suggestedVideos: [1, 2, 3],
  favoriteOrgs: [2, 4],
  isAuth: true,
};

const categories = [
  {
    value: "all",
    label: "All",
  },
  {
    value: "restaurants",
    label: "Restaurants",
  },
  {
    value: "cafes",
    label: "Cafes",
  },
  {
    value: "salons",
    label: "Salons",
  },
];

const VideosIxPage = ({ videos }) => {
  const [orgs, setOrgs] = useState(videos);
  const [filterType, setFilterType] = useState("All");
  const [showSelect, setShowSelect] = useState(false);
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [isConstructionModalOpen, setIsConstructionModalOpen] = useState(true);

  const selectFilterType = (type) => {
    setFilterType(type);
  };

  const filterOrgs = () => {
    if (filterType === "Suggested") {
      return filterBySuggested();
    }
    if (filterType === "Favorites") {
      return filterByFavorites();
    } else if (filterType === "All") {
      return filterByCategory();
    } else {
      return orgs || [];
    }
  };

  //SUGGESTED
  //user categories would be a field in user table
  const filterBySuggested = () => {
    return orgs.filter((org) => auth.suggestedVideos.includes(org.id));
  };

  //FAVORITES -- disabled if not logged in
  const filterByFavorites = () => {
    return orgs.filter((org) => auth.favoriteOrgs.includes(org.id));
  };

  //SEARCH TERMS
  const filterByCategory = () => {
    if (categoryFilter === "all") {
      return orgs;
    } else {
      return orgs.filter(
        (org) => org.category.toLowerCase() === categoryFilter.toLowerCase()
      );
    }
  };

  const selectFilterCategory = (category) => {
    setCategoryFilter(category);
  };

  const handleFavorite = async (orgId) => {
    console.log("favorited");
  };

  const handleShowSearch = () => setShowSelect(!showSelect);

  const checkFave = (org) => {
    if (auth && auth.favoriteOrgs) {
      return auth.favoriteOrgs.includes(org.id) || false;
    } else {
      return false;
    }
  };

  const renderAllOrgs = () => {
    const filteredOrgs = filterOrgs();

    return (
      <Grid container spacing={4}>
        {filteredOrgs.map((video) => {
          return (
            <Grid item xs={12} sm={6} md={4} lg={3} key={video.id}>
              <VideoCard
                key={video.id}
                href={`/videos/${video.category || ""}`}
                org={video}
                handleFavorite={handleFavorite}
                isFave={checkFave(video)}
                isAuth={auth.isAuth}
              ></VideoCard>
            </Grid>
          );
        })}
      </Grid>
    );
  };

  return (
    <>
      <Container>
        <div
          className="filterContainer"
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            margin: "20px auto",
          }}
        >
          <FilterTabs
            selectFilterType={selectFilterType}
            handleShowSearch={handleShowSearch}
            isAuth={auth.isAuth}
          />
          {showSelect && (
            <FilterSelect
              id="category"
              filterByCategory={selectFilterCategory}
              categories={categories}
              label="categories"
            />
          )}
        </div>
        {renderAllOrgs()}
        <ConstructionModal
          isOpen={isConstructionModalOpen}
          handleClose={() => setIsConstructionModalOpen(false)}
        />
      </Container>
    </>
  );
};

export default VideosIxPage;

export async function getServerSideProps() {
  const res = await fetch("http://localhost:3000/api/getCategory");
  const all_videos = await res.json();
  return {
    props: {
      id: "an id",
      videos: all_videos.videos.all_videos || [],
    },
  };
}
