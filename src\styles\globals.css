@tailwind base;
@tailwind components;
@tailwind utilities;

/* styles/globals.css */
/* Add your preferred fontawesome .css file */
@import "@fortawesome/fontawesome-svg-core/styles.css";

html,
body {
  overscroll-behavior-y: contain;
}

.container {
  /* height: 100vh; */
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.fade {
  display: inline-block;
  font-size: 2em;
  opacity: 0;
  animation: fade-in-out 2.5s forwards;
}

@keyframes fade-in-out {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  40%,
  60% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

.float {
  position: fixed;
  width: 60px;
  height: 60px;
  bottom: 40px;
  right: 40px;
  background-color: #25d366;
  color: #fff;
  border-radius: 50px;
  text-align: center;
  font-size: 30px;
  box-shadow: 2px 2px 3px #999;
  z-index: 100;
}

.my-float {
  margin-top: 16px;
}

.select-country:focus {
  outline: none;
}

/* remove anchor tag underline */
a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* remove anchor tag underline via tailwind */
@layer base {
  a {
    @apply no-underline;
  }

  a:hover {
    @apply no-underline;
  }
}

.scrollbar-hidden {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

.nowrap {
  text-wrap: nowrap;
}


[aria-disabled='true'] {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed !important;
}