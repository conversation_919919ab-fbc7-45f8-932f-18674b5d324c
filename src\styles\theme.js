import { createTheme } from "@mui/material/styles";
import { colors } from "./colors";
import { interFont } from "@/constant/font";

const blueishgreen = colors.blueishgreen;
const charcoal = colors.charcoal;
const moremagenta = colors.moremagenta;
const whiteish = colors.whiteish;
const yellow = colors.yellow;

export const theme = createTheme({
  breakpoints: {
    values: {
      xs: 0,
      xsm: 375,
      smd: 500,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
      xl2: 1330, // Custom breakpoint at 1300 for dadhboard
    },
  },
  spacing: 4,
  width: 1,
  fontFamily: [interFont, "sans-serif"].join(","),
  palette: {
    background: {
      default: "#fcfcfc",
    },
    primary: {
      // light: will be calculated from palette.primary.main,
      main: blueishgreen,
      // contrastText: will be calculated to contrast with palette.primary.main
    },
    secondary: {
      main: moremagenta,
      contrastText: charcoal,
    },
    error: {
      main: moremagenta,
    },
    // Used by `getContrastText()` to maximize the contrast between
    // the background and the text.
    contrastThreshold: 3,
    // Used by the functions below to shift a color's luminance by approximately
    // two indexes within its tonal palette.
    // E.g., shift from Red 500 to Red 300 or Red 700.
    tonalOffset: 0.2,
  },
  typography: {
    fontFamily: interFont,
    h3: {
      fontSize: "1.2em",
      lineHeight: 1.2,
    },
    allVariants: {
      a: {
        textDecoration: "none",
      },
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        html: {
          overscrollBehavior: "contain",
        },
        body: {
          display: "flex",
          flexDirection: "column",
          minHeight: "100vh",
          margin: 0,
          padding: 0,
          boxSizing: "border-box",
        },
        "#__next": {
          display: "flex",
          flexDirection: "column",
          flex: 1,
        },
        main: {
          display: "flex",
          flexDirection: "column",
          flex: 1,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          fontWeight: 600,
          fontSize: "1rem",
          color: "white",
          width: "160px",
          borderRadius: "10px",
          backgroundColor: "#F3B358",
          border: "none",
          "&:hover": {
            backgroundColor: "#02AC9F",
          },
        },
      },
      variants: [
        {
          props: { variant: "understated" },
          style: {
            width: { xs: "85px", md: "100px" },
            height: { xs: "35px", md: "45px" },
            color: "black",
            backgroundColor: "white",
            "&:hover": {
              backgroundColor: yellow, // Assuming you want the hover state to be charcoal
            },
          },
        },
      ],
    },
  },
});
