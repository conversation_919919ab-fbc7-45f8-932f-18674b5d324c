import { CURRENCY_ENUM } from "@/constant/Enums";

type validateClubProps = {
  title: string;
  about: string;
  price: number;
  theme: string;
  targetLanguage: string;
  currency: CURRENCY_ENUM;
  proficiencyLevel: string;
  categories: string[];
  highlights: string[];
  selectedCreators: string[];
};

const validateClub = ({
  title,
  about,
  price,
  theme,
  proficiencyLevel,
  categories,
  highlights,
  selectedCreators,
  currency,
  targetLanguage,
}: validateClubProps) => {
  if (!title) {
    return "Club title cannot be empty";
  }
  if (!about) {
    return "Club about cannot be empty";
  }
  if (!selectedCreators || selectedCreators.length === 0) {
    return "Club teachers cannot be empty";
  }
  if (!price) {
    return "Club price cannot be empty";
  }
  if (!targetLanguage) {
    return "Please select target language";
  }
  if (!theme) {
    return "Club theme cannot be empty";
  }
  if (!currency) {
    return "Please select currency";
  }
  if (!proficiencyLevel) {
    return "Club proficiencyLevel cannot be empty";
  }
  if (!categories || categories.length === 0) {
    return "Club categories cannot be empty";
  }
  if (!highlights || highlights.length === 0) {
    return "highlights cannot be empty";
  }
  return "";
};

export { validateClub };
