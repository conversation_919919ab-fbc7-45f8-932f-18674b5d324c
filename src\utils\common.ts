import { MaybeObjectId } from "@/types";
import {
  EventOnType,
  LanguageProficiencyType,
  LanguageType,
} from "@/api/mongoTypes";

const getLocalUser = () => {
  try {
    const user = localStorage.getItem("user");
    return JSON.parse(user);
  } catch (error) {
    return null;
  }
};

const getPlanString = (plan) => {
  return `${plan.planId}_${new Date(
    plan.startDate
  ).toLocaleString()}_${new Date(plan.endDate).toLocaleString()}_${
    plan.planFor
  }_${plan.isDateEnabled}_${plan.emailId}`;
};

const handleVisitor = (isStore = true) => {
  if (isStore) {
    localStorage.setItem("visitor", "STORE");
  } else {
    localStorage.removeItem("visitor");
  }
};

const maybeObjectId = (obj: any) => {
  if (typeof obj === "string") {
    return obj;
  }
  if (obj && obj?._id === "string") {
    return obj._id;
  }
  return null;
};

const getProficiencyEn = ({
  data,
  isEn = true,
}: {
  data: MaybeObjectId<LanguageProficiencyType>;
  isEn?: boolean;
}) => {
  if (typeof data === "string") {
    return null;
  }
  const info = data as LanguageProficiencyType;
  if (info && info?._id && info?.pfLevel) {
    if (isEn) {
      return info?.pfLevel?.en;
    }
    return info?.pfLevel?.es;
  }
  return null;
};

const getEnglishAndSpanish = (languages: LanguageType[]) => {
  return languages.filter((d) => RegExp("^(es|en)$").test(d.code));
};

const getLanguageName = (language: MaybeObjectId<LanguageType>) => {
  console.log("language", language);
  if (!language) {
    return null;
  }
  if (language && "_id" in language && "name" in language) {
    return language.nameInEnglish;
  }
  return null;
};

const getSingleEventOn = (eventDetails: EventOnType | EventOnType[]) => {
  if (Array.isArray(eventDetails)) {
    return null;
  }
  if (eventDetails && "_id" in eventDetails) {
    return eventDetails as EventOnType;
  }
  return null;
};

export {
  getLocalUser,
  getPlanString,
  handleVisitor,
  maybeObjectId,
  getProficiencyEn,
  getEnglishAndSpanish,
  getLanguageName,
  getSingleEventOn,
};
