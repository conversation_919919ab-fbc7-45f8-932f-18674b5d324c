/**
 * isInside determines whether a point or a rectangle defined by coordinates is inside the bounding box of a specified HTML element.
 *
 * @param {HTMLElement} element - The HTML element.
 * @param {Object} coordinate - An object representing the point or rectangle.
 * @param {number} coordinate.left - The x-coordinate of the point or the left side of the rectangle.
 * @param {number} coordinate.top - The y-coordinate of the point or the top side of the rectangle.
 * @param {number} [coordinate.right] - The right side of the rectangle (optional).
 * @param {number} [coordinate.bottom] - The bottom side of the rectangle (optional).
 *
 * @returns {boolean} - Returns true if the point or rectangle is inside the element's bounding box, false otherwise.
 */
export const isInside = (element, coordinate) => {
  // Get the bounding box of the element
  const { left, right, bottom, top } = element.getBoundingClientRect();

  // Check if the coordinate represents a point or a rectangle.
  // If coordinate.right and coordinate.bottom are not provided, it's a point.
  if (!coordinate.right || !coordinate.bottom) {
    // Case 1: Point
    // Check if the point is outside the bounding box of the element, horizontally.
    if (coordinate.left > right || coordinate.left < left) {
      return false;
    }
    // Check if the point is outside the bounding box of the element, vertically.
    if (coordinate.top > bottom || coordinate.top < top) {
      return false;
    }
  } else {
    // Case 2: Rectangle
    // Check if any part of the rectangle is outside the bounding box of the element.
    if (
      coordinate.left < left ||
      coordinate.top < top ||
      coordinate.right > right ||
      coordinate.bottom > bottom
    ) {
      return false;
    }
  }

  // If none of the above conditions are met, the point or rectangle is inside the bounding box of the element.
  return true;
};
