const getEmptyClassesTitleAndDesc = ({ type }) => {
  if (type === 1) {
    return {
      title: "No past classes / events yet. ",
      description:
        "You haven’t completed any classes so far—stay tuned and keep learning!",
    };
  }
  if (type === 2) {
    return {
      title: "No ongoing class / event right now. ",
      description:
        " Check your schedule for upcoming sessions or explore available classes.",
    };
  }
  return {
    title: "No upcoming classes / events scheduled.",
    description:
      "Looks like your calendar is clear. Time to book your next class!",
  };
};

const getEmptySuggestionsTitleAndDesc = ({ type }) => {
  if (type === 1) {
    return {
      title: "No past suggestions available.",
      description: "You haven’t missed anything — we’ll keep you posted.",
    };
  }
  if (type === 2) {
    return {
      title: "Nothing suggested at the moment.",
      description: "Check back soon for something just right for you.",
    };
  }
  return {
    title: "No upcoming suggestions right now.",
    description: "We’ll suggest something exciting as it comes up.",
  };
};

const getEmptyPurchaseTitleAndDes = () => {
  return {
    title: "You haven’t purchased anything yet.",
    description: "Browse our classes and events to get started..",
  };
};

export {
  getEmptyClassesTitleAndDesc,
  getEmptySuggestionsTitleAndDesc,
  getEmptyPurchaseTitleAndDes,
};
