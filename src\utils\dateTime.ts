import {
  format,
  addWeeks,
  startOfWeek,
  endOfWeek,
  isSameDay,
  addDays,
  startOfDay,
  parseISO,
  isEqual,
  isAfter,
  isBefore,
  isFriday,
  isSaturday,
  isSunday,
} from "date-fns";
import { zonedTimeToUtc, utcToZonedTime } from "date-fns-tz";

const USER_TIMEZONE = Intl.DateTimeFormat().resolvedOptions().timeZone;

type dateAsPerTimeZoneProps = {
  dateToChange: Date;
  timeZone?: string;
};
const dateAsPerTimeZone = ({
  dateToChange,
  timeZone = USER_TIMEZONE,
}: dateAsPerTimeZoneProps) => {
  //   if (typeof dateToChange === "string") {
  //     return new Date(
  //       new Date(dateToChange).toLocaleString("en-US", {
  //         timeZone,
  //       })
  //     );
  //   }

  return new Date(
    dateToChange.toLocaleString("en-US", {
      timeZone,
    })
  );
};

const getEndOfDayUTC = (date: Date, endTime = false) => {
  if (!date) return null;

  if (date?.toISOString) {
    return new Date(date.toISOString());
  }

  const formattedDate = new Date(date);
  const utcDate = new Date(
    Date.UTC(
      formattedDate.getFullYear(),
      formattedDate.getMonth(),
      formattedDate.getDate(),
      endTime ? 23 : formattedDate.getHours() || 0,
      endTime ? 59 : formattedDate.getMinutes() || 0,
      endTime ? 59 : formattedDate.getSeconds() || 0,
      endTime ? 999 : formattedDate.getMilliseconds() || 0
    )
  );
  return utcDate;
};

const getDateAsPerUTC = (date: Date, baseTime = false) => {
  if (!date) return null;

  if (date?.toISOString && !baseTime) {
    return new Date(date.toISOString());
  }

  const formattedDate = new Date(date);
  const utcDate = new Date(
    Date.UTC(
      formattedDate.getFullYear(),
      formattedDate.getMonth(),
      formattedDate.getDate(),
      baseTime ? 0 : formattedDate.getHours() || 0,
      baseTime ? 0 : formattedDate.getMinutes() || 0,
      baseTime ? 0 : formattedDate.getSeconds() || 0,
      baseTime ? 0 : formattedDate.getMilliseconds() || 0
    )
  );
  return utcDate;
};

type formatDateProps = {
  date: Date;
  returnDay?: boolean;
  inText?: boolean;
  returnWeekday?: boolean;
  timezone?: string;
};

const formatDate = ({
  date,
  returnDay = false,
  returnWeekday = false,
  inText = false,
  timezone = null,
}: formatDateProps) => {
  if (!date) {
    return;
  }
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const weekdays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  const newDate = dateAsPerTimeZone({
    dateToChange: date,
    timeZone: timezone ?? USER_TIMEZONE,
  });
  const day = newDate.getDate();
  const monthIdx = newDate.getMonth();
  const month = months[monthIdx];
  const year = newDate.getFullYear();
  const weekday = weekdays[newDate.getDay()];

  if (inText) {
    const today = dateAsPerTimeZone({
      dateToChange: new Date(),
      timeZone: timezone ?? USER_TIMEZONE,
    });
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    const isSameDay = (d1: Date, d2: Date) =>
      d1.getFullYear() === d2.getFullYear() &&
      d1.getMonth() === d2.getMonth() &&
      d1.getDate() === d2.getDate();

    if (isSameDay(newDate, today)) return "today";
    if (isSameDay(newDate, yesterday)) return "yesterday";
    if (isSameDay(newDate, tomorrow)) return "tomorrow";
  }

  const opDate = `${day} ${month} ${year}`;

  if (returnWeekday) {
    return weekday; // Return "Mon", "Tue", etc.
  }
  if (returnDay) {
    return day;
  }
  return opDate;
};

type formatTimeProps = {
  date: Date;
  returnDecimal?: boolean;
  timezone?: string;
};

const formatTime = ({
  date,
  returnDecimal = false,
  timezone = null,
}: formatTimeProps) => {
  if (!date) {
    return;
  }
  const newDate = dateAsPerTimeZone({
    dateToChange: date,
    timeZone: timezone ?? USER_TIMEZONE,
  });
  let hours = newDate.getHours();
  let minutes: any = newDate.getMinutes();

  if (returnDecimal) {
    const decimalTime = +hours + +minutes / 60;
    return decimalTime.toFixed(2);
  }

  const ampm = hours >= 12 ? "pm" : "am";
  hours %= 12;
  hours = hours || 12;

  minutes = minutes < 10 ? `0${minutes}` : minutes;
  const time = `${hours}:${minutes} ${ampm}`;
  return time;
};

function convertToTime(endTime: string) {
  const [hours, minutes, seconds] = endTime.split(":").map(Number);
  const date = new Date();
  date.setHours(hours, minutes, seconds, 0);
  return date;
}
const isFutureDate = (inputDate) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // remove time part
  const date = new Date(inputDate);
  return date > today;
};

type combineDateNTimeProps = {
  date: string;
  time: string;
  timezone: string;
};
const combineDateNTime = ({ date, time, timezone }: combineDateNTimeProps) => {
  try {
    const combined = `${date}T${time}`;
    const combinedDate = new Date(combined);
    const utcDate = zonedTimeToUtc(combinedDate, timezone);
    return utcDate;
  } catch (error) {
    return null;
  }
};

type returnGetWeekOptions = {
  startDate: Date;
  endDate: Date;
  label: string;
  value: number;
};
type getWeekOptionsProps = {
  showStartEndDate?: boolean;
};
const getWeekOptions = ({
  showStartEndDate = false,
}: getWeekOptionsProps): returnGetWeekOptions[] => {
  const today = new Date();
  const startWeekOffset =
    isFriday(today) || isSaturday(today) || isSunday(today) ? 1 : 0;

  let firstMonday = startOfWeek(addWeeks(today, startWeekOffset), {
    weekStartsOn: 1,
  });

  return Array.from({ length: 12 }, (_, i) => {
    const monday = addWeeks(firstMonday, i);
    const thursday = addDays(monday, 3);

    return {
      value: i,
      startDate: monday,
      endDate: thursday,
      label: showStartEndDate
        ? `Mon, ${format(monday, "MMM dd")}`
        : `${format(monday, "MMM dd, yyyy")} - ${format(
            thursday,
            "MMM dd, yyyy"
          )}`,
    };
  });
};

const getEndOfTheDay = (date: Date) => {
  const endOfDay = new Date(date);
  endOfDay.setUTCHours(23, 59, 59, 999);
  return endOfDay;
};

export {
  formatDate,
  formatTime,
  convertToTime,
  isFutureDate,
  combineDateNTime,
  getDateAsPerUTC,
  getWeekOptions,
  dateAsPerTimeZone,
  getEndOfTheDay,
  getEndOfDayUTC,
  USER_TIMEZONE,
};
