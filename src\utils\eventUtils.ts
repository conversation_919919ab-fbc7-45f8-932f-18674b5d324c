import { EVENT_MODE_TYPE } from "@/constant/Enums";
import { isBefore, isEqual } from "date-fns";

function isEndTimeBefore(startDate, endDate, startTime, endTime) {
  if (!startDate?.$d || !endDate?.$d || !startTime?.$d || !endTime?.$d) {
    return "All date and time parameters must be provided and valid";
  }

  const fStartDate = new Date(startDate.$d);
  const fEndDate = new Date(endDate.$d);
  const fStartTime = new Date(startTime.$d);
  const fEndTime = new Date(endTime.$d);

  const sameDay = fStartDate.toDateString() === fEndDate.toDateString();

  if (isEqual(fStartDate, fEndDate) && isEqual(fStartTime, fEndTime)) {
    return "Start date/time and end date/time cannot be the same";
  }

  if (isBefore(fEndDate, fStartDate)) {
    return "End date must be greater than or equal to start date";
  }

  if (sameDay) {
    if (isBefore(fEndTime, fStartTime) || isEqual(fEndTime, fStartTime)) {
      return "End time must be greater than start time on the same day";
    }
    return "";
  }

  return "";
}

const validateEvent = ({
  title,
  description,
  languageProficiency,
  category,
  mode,
  location,
  url,
  price,
  currency,
  maxNumberOfRegistrations,
  targetLanguage,
  eventOn,
}) => {
  if (!title) {
    return "Please enter an event title";
  }
  if (!description) {
    return "Please enter description";
  }
  if (!languageProficiency) {
    return "Please select language proficiency ";
  }
  if (!targetLanguage) {
    return "Please select target language";
  }
  if (maxNumberOfRegistrations < 1) {
    return "Please enter a valid number of registrations";
  }
  if (category.length == 0) {
    return "Please select a category ";
  }
  if (price == 0) {
    return "Price cannot be empty";
  }
  if (!mode) {
    return "Please select a mode";
  }
  if (!currency) {
    return "Please select a currency";
  }
  if (mode == EVENT_MODE_TYPE.ONLINE) {
    if (!url) {
      return "Please enter a valid url";
    }
  }
  if (mode == EVENT_MODE_TYPE.OFFLINE && !location) {
    return "Please enter an event location";
  }
  if (eventOn.length == 0) {
    return "Please add at least one date and time";
  }
  if (eventOn.some((s) => !s.startDate)) {
    return "Start date cannot be empty";
  }
  if (eventOn.some((s) => !s.startTime)) {
    return "Please select a start time";
  }
  if (eventOn.some((s) => !s.endTime)) {
    return "Please select a end time";
  }
  for (const m of eventOn) {
    const error = isEndTimeBefore(
      m.startDate,
      m.startDate,
      m.startTime,
      m.endTime
    );
    if (error) {
      return error;
    }
  }
  return "";
};

export { validateEvent };
