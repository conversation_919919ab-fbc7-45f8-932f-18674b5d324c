import {
  ClassesPricingType,
  LanguageProficiencyType,
  LanguageType,
  RoleType,
  UserLanguageType,
  UserType,
} from "@/api/mongoTypes";
import {
  CLASSES_TYPE,
  DURATION_TYPE,
  IN_PERSON_TYPE,
  ONLINE_CLASSES_TYPE,
} from "@/constant/Enums";
import { ValueOf } from "@/types";
import { getProficiencyEn } from "./common";

const getTypeName = (type: ValueOf<typeof CLASSES_TYPE>) => {
  if (type === CLASSES_TYPE.COMMUNITY) {
    return "Community";
  }
  if (type === CLASSES_TYPE.IN_PERSON) {
    return "In Person";
  }
  return "Online";
};

const getSubTypeName = (
  subType: ValueOf<typeof IN_PERSON_TYPE> | ValueOf<typeof ONLINE_CLASSES_TYPE>
) => {
  if (subType === IN_PERSON_TYPE.GROUP) {
    return "Group";
  }
  if (subType === IN_PERSON_TYPE.PRIVATE) {
    return "Private";
  }
  if (subType === ONLINE_CLASSES_TYPE.LONG_SESSIONS) {
    return "Long Session";
  }
  if (subType === ONLINE_CLASSES_TYPE.QUICK_SESSIONS) {
    return "Quick Session";
  }
  return "Regular Session";
};

type getPricingTitleProps = {
  data: ClassesPricingType;
  needSubtitle?: boolean;
};
const getPricingTitle = ({
  data,
  needSubtitle = true,
}: getPricingTitleProps) => {
  if (!needSubtitle) {
    return `${data?.title}`;
  }
  if (data?.title && needSubtitle) {
    const subtitle = data?.subtitle ? `(${data?.subtitle})` : "";
    return `${data?.title} ${subtitle}`;
  }
  return "";
};

const getDuration = (durationType: ValueOf<typeof DURATION_TYPE>) => {
  if (durationType === DURATION_TYPE.HOUR) {
    return "hour";
  }
  if (durationType === DURATION_TYPE.MINUTES) {
    return "Minutes";
  }
  if (durationType === DURATION_TYPE.THIRTY_MINUTES) {
    return "30 mins";
  }
  if (durationType === DURATION_TYPE.NINETY_MINUTES) {
    return "90 mins";
  }
  return "Week";
};

type getNumberOfDurationProps = {
  type: ValueOf<typeof CLASSES_TYPE>;
  subType: ValueOf<typeof IN_PERSON_TYPE> | ValueOf<typeof ONLINE_CLASSES_TYPE>;
};
const getNumberOfDuration = ({ type, subType }: getNumberOfDurationProps) => {
  if (type === CLASSES_TYPE.IN_PERSON && subType !== IN_PERSON_TYPE.PRIVATE) {
    return "No. of weeks";
  }
  return "No. of sessions";
};

type getPlanTitleProps = {
  duration: number;
  type: ValueOf<typeof CLASSES_TYPE>;
  subType: ValueOf<typeof IN_PERSON_TYPE> | ValueOf<typeof ONLINE_CLASSES_TYPE>;
  durationType?: ValueOf<typeof DURATION_TYPE>;
};

const getPlansTitle = ({ duration, type, subType }: getPlanTitleProps) => {
  if (type === CLASSES_TYPE.IN_PERSON) {
    if (subType === IN_PERSON_TYPE.GROUP) {
      if (duration === 1) {
        return `${duration} Week : `;
      }
      return `${duration} Weeks :`;
    }
    if (subType === IN_PERSON_TYPE.PRIVATE) {
      if (duration === 1) {
        return `${duration} Session :`;
      } else {
        return `${duration} Sessions`;
      }
    }
  }
  if (type === CLASSES_TYPE.ONLINE) {
    if (duration === 1) {
      return `${duration} session : `;
    }
    return `${duration} sessions :`;
  }
  return "";
};

const getDicountedPrice = ({ price, discount }) => {
  return price * (1 - discount / 100);
};

const nameFromEmail = (email: string) => {
  return email?.split("@")[0];
};

const getUserFullName = (user) => {
  return `${user?.firstName ?? ""} ${user?.lastName ?? ""}`;
};

const getUserRoles = (user: UserType) => {
  const allowedRoles = user?.allowedRoles ?? [];
  const typedAllowedRoles = allowedRoles.map((m) => m as RoleType);
  return typedAllowedRoles.map((m: RoleType) => m.type);
};

const getUserCountry = (user: UserType) => {
  if (
    user &&
    user?.countryOfResidence &&
    typeof user.countryOfResidence === "object" &&
    "name" in user.countryOfResidence
  ) {
    return user.countryOfResidence.name;
  }
  return "-";
};

const getUserLanguages = (user: UserType) => {
  const languagesArray = user.languages.filter(
    (f: any) => f?.language?._id
  ) as UserLanguageType[];
  if (languagesArray.length > 0) {
    const lnaguagesName = languagesArray
      .map((m: any) => m?.language?.name)
      .filter((f) => f);
    return lnaguagesName.join(",");
  }
  return "-";
};

const getUserStudyingLanguage = (user: UserType) => {
  if (
    user &&
    user?.languageOfInterest &&
    typeof user.languageOfInterest === "object" &&
    "name" in user.languageOfInterest
  ) {
    return user.languageOfInterest.name;
  }
  return "-";
};

const getUserStudyingLanguageProficiency = (user: UserType) => {
  if (
    user &&
    user?.proficiencyOfLanguageOfInterest &&
    typeof user.proficiencyOfLanguageOfInterest === "object"
  ) {
    return getProficiencyEn({
      data: user.proficiencyOfLanguageOfInterest,
    });
  }
  return "-";
};

const getProficiencyList = (languages: UserLanguageType[]) => {
  function groupLanguagesByProficiency(data) {
    const proficiencyMap = {};
    data.forEach((item) => {
      const { proficiency, language } = item;
      if (!proficiencyMap[proficiency]) {
        proficiencyMap[proficiency] = [];
      }
      proficiencyMap[proficiency].push(language);
    });
    const result = Object.entries(proficiencyMap).map(
      ([proficiency, languages]) => {
        const langs = languages as String[];
        return {
          proficiency,
          languages: langs.join(", "),
        };
      }
    );
    return result;
  }
  const arrayOfList = languages
    .map((m) => {
      if (!m) return null;

      function isLanguageProficiency(obj: any): obj is LanguageProficiencyType {
        return (
          obj &&
          typeof obj === "object" &&
          "pfLevel" in obj &&
          "en" in obj.pfLevel
        );
      }

      function isLanguageType(obj: any): obj is LanguageType {
        return obj && typeof obj === "object" && "name" in obj;
      }

      if (
        !isLanguageProficiency(m.proficiency) ||
        !isLanguageType(m.language)
      ) {
        return null;
      }
      return {
        proficiency: getProficiencyEn({
          data: m.proficiency,
        }),
        language: m.language.name,
      };
    })
    .filter(
      (item): item is { proficiency: string; language: string } => item !== null
    );
  return groupLanguagesByProficiency(arrayOfList);
};

export {
  getTypeName,
  getSubTypeName,
  getPricingTitle,
  getDuration,
  getPlansTitle,
  getDicountedPrice,
  nameFromEmail,
  getUserFullName,
  getUserRoles,
  getUserCountry,
  getUserLanguages,
  getUserStudyingLanguage,
  getUserStudyingLanguageProficiency,
  getProficiencyList,
  getNumberOfDuration,
};
