interface ErrorWithResponse extends Error {
  response?: {
    status?: number;
    data?: {
      message?: string;
    };
  };
}

const getMessageAndStatusCode = (error: unknown) => {
  const isErrorWithResponse = (err: unknown): err is ErrorWithResponse =>
    err instanceof Error && "response" in err;

  const statusCode =
    isErrorWithResponse(error) && error.response?.status
      ? error.response.status
      : 500;

  const errorMessage =
    isErrorWithResponse(error) && error.response?.data?.message
      ? error.response.data.message
      : "Internal server error";

  return {
    status: statusCode,
    message: errorMessage,
  };
};

const FormatResponse = ({ data, message, success }) => {
  return {
    data,
    message,
    success,
  };
};

export { getMessageAndStatusCode, FormatResponse };
