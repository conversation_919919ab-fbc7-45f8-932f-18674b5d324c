import { emailEnum } from "@/constant/email/emailEnum";
import axios from "axios";

export const sendWaitlistEmail = async (email) => {
  try {
    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}api/utils/send-email`,
      {
        type: emailEnum.SUBSCRIPTION,
        email,
      }
    );
    return data;
  } catch (error) {
    // console.log("Something went wrong in sendWaitlistEmail due to ", error);
    return null;
  }
};

export const sendSubscriptionEmail = async (email) => {
  try {
    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}api/utils/send-email`,
      {
        type: emailEnum.SUBSCRIBE,
        email,
      }
    );
    return data;
  } catch (error) {
    // console.log("Something went wrong in sendSubscriptionEmail due to ", error);
    return null;
  }
};

export const addUserSubmission = async (email, address) => {
  try {
    const response = await fetch(address, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email }),
    });
    if (response.ok) {
      const data = await response.json();
      return true;
    } else {
      const errorData = await response.json();
      return false;
    }
  } catch (error) {
    return false;
  }
};
