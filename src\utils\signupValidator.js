import FormValidator from "./formValidator";

const passwordValidation = (pwd) => {
  if (pwd.length >= 6 && pwd.length < 30) {
    const isnum = /[0-9]/.test(pwd);
    const isLower = /[a-z]/.test(pwd);
    const isUpper = /[A-Z]/.test(pwd);

    return isnum && isLower && isUpper;
  } else {
    return false;
  }
};

const passwordMatch = (confirmation, state) => {
  return state.password === confirmation;
};

export const signupValidator = new FormValidator([
  {
    field: "firstName",
    method: "isEmpty",
    validWhen: false,
    message: "First name is required.",
  },
  {
    field: "lastName",
    method: "isEmpty",
    validWhen: false,
    message: "Last name is required.",
  },
  // {
  //   field: "firstName",
  //   method: "isAlpha",
  //   validWhen: false,
  //   message: "First name must use only letters."
  // },
  // {
  //   field: "lastName",
  //   method: "isAlpha",//TODO: add allowable hyphenation
  //   validWhen: false,
  //   message: "Last name is required."
  // },
  {
    field: "email",
    method: "isEmpty",
    validWhen: false,
    message: "Email is required.",
  },
  {
    field: "email",
    method: "isEmail",
    validWhen: true,
    message: "That is not a valid email.",
  },
  {
    field: "phone",
    method: "matches",
    // args: [/^$|^(1?\(?\d\d\d\)? ?\d\d\d-?\d\d\d\d$)/], //TODO: intl numbers yikes
    args: [/^[0-9]*$/],
    validWhen: true,
    message: "That is not a valid phone number.",
  },
  {
    field: "password",
    method: "isEmpty",
    validWhen: false,
    message: "Password is required.",
  },
  {
    field: "password",
    method: passwordValidation,
    validWhen: true,
    message:
      "Password mush have at least 6 chars: uppercase, lowercase, and a number",
  },
  {
    field: "passwordConfirmation",
    method: "isEmpty",
    validWhen: false,
    message: "Password confirmation is required.",
  },
  {
    field: "passwordConfirmation",
    method: passwordMatch,
    validWhen: true,
    message: "Password confirmation did not match.",
  },
]);

export const firstLastValidator = new FormValidator([
  {
    field: "firstName",
    method: "isEmpty",
    validWhen: false,
    message: "First name is required.",
  },
  {
    field: "lastName",
    method: "isEmpty",
    validWhen: false,
    message: "Last name is required.",
  },
  {
    field: "primaryLanguage",
    method: "isEmpty",
    validWhen: false,
    message: "Primary language is required.",
  },
]);

export const phoneEmailValidator = new FormValidator([
  // {
  //   field: "phone",
  //   method: "matches",
  //   args: [/^$|^(1?\(?\d\d\d\)? ?\d\d\d-?\d\d\d\d$)/], //TODO: intl numbers yikes
  //   validWhen: true,
  //   message: "That is not a valid phone number."
  // },
  {
    field: "phone",
    method: "matches",
    args: [/^[0-9]*$/],
    validWhen: true,
    message: "That is not a valid phone number.",
  },
  {
    field: "email",
    method: "isEmpty",
    validWhen: false,
    message: "Email is required.",
  },
]);

export const passwordValidator = new FormValidator([
  {
    field: "password",
    method: "isEmpty",
    validWhen: false,
    message: "Password is required.",
  },
  {
    field: "password",
    method: passwordValidation,
    validWhen: true,
    message:
      "Password mush have at least 6 chars: uppercase, lowercase, and a number",
  },
  {
    field: "passwordConfirmation",
    method: "isEmpty",
    validWhen: false,
    message: "Password confirmation is required.",
  },
  {
    field: "passwordConfirmation",
    method: passwordMatch,
    validWhen: true,
    message: "Password confirmation did not match.",
  },
]);
