import axios from "axios";

type uploadFileOnAwsProps = {
  key: string;
  file: File;
};
const uploadFileOnAws = async ({ key, file }: uploadFileOnAwsProps) => {
  let response = {
    isError: false,
    message: "",
    data: null,
  };

  try {
    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}api/utils/generate-presigned-url`,
      {
        key,
        name: file.name,
      }
    );
    const url = data.data;
    if (url) {
      await axios.put(url, file, {
        headers: {
          "Content-Type": file.type,
        },
      });
      response.data = {
        Key: key,
      };
      response.message = "Uploaded file on AWS";
      return response;
    } else {
      response.isError = true;
      response.message = "Something went wrong while uploading file";
      return response;
    }
  } catch (error) {
    console.error("Something went wrong while uploading file due to ", error);
    response.isError = true;
    response.message = "Something went wrong while uploading file";
    return response;
  }
};

export { uploadFileOnAws };
