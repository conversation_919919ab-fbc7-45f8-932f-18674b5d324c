import { environment } from "@/api/aws";
import { ASSETS_STATUS, ASSETS_TYPES } from "@/constant/Enums";
import { ValueOf } from "@/types";
import axios from "axios";
import { uploadFileOnAws } from "./uploadFileOnAws";

const getCategoryNamesBasedOnId = (categories, selectedCategories) => {
  let data = [];
  categories.map((m) => {
    if (selectedCategories.includes(m._id)) {
      data.push(m.name);
    }
  });
  return data;
};

type validateUploadVideoFieldsProps = {
  title: string;
  description: string;
  language: string;
  languageProficiency: string;
  selectedCollection: string;
  selectedCreator: string;
  selectedCategories: string[];
  selectedThumbnail: File;
  selectedEnglishSubtitle: File;
  selectedSpanishSubtitle: File;

  ignoreCollection?: boolean;
};

const validateUploadVideoFields = ({
  title,
  description,
  language,
  languageProficiency,
  selectedCreator,
  selectedCategories,
  selectedThumbnail,
  selectedCollection,
  ignoreCollection = false,
  selectedEnglishSubtitle,
  selectedSpanishSubtitle,
}: validateUploadVideoFieldsProps) => {
  if (!title) {
    return "Please enter title";
  }
  if (!description) {
    return "Please enter description";
  }
  if (!language) {
    return "Please select language";
  }
  if (!languageProficiency) {
    return "Please select languageProficiency";
  }
  if (!selectedCollection && !ignoreCollection) {
    return "Please select Collection";
  }
  if (!selectedThumbnail) {
    return "Please select thumbnail";
  }
  if (!selectedEnglishSubtitle) {
    return "Please select English subtitle";
  }
  if (!selectedSpanishSubtitle) {
    return "Please select Spanish subtitle";
  }
  if (!selectedCreator) {
    return "Please select Creator";
  }
  if (selectedCategories.length === 0) {
    return "Please select categories";
  }
};

type handleCreateVideoProps = {
  title: string;
  description: string;
  language: string;
  proficiencyLevel: string;
  categoriesId: string[];
  categoriesName: string[];
  userId: string;
  level: string;
  duration: number;
  collectionId: string;
  file: File;
  isFeatured: Boolean;
  selectedThumbnail: File;
  selectedEnglishSubtitle: File;
  selectedSpanishSubtitle: File;
  onVideoCrateSuccess: () => void;
  onVideoUploadSuccess: ({ data }) => void;
  onVideoCreateError: () => void;
};
const handleCreateVideo = async ({
  title,
  description,
  language,
  proficiencyLevel,
  categoriesId,
  categoriesName,
  userId,
  duration,
  collectionId,
  file,
  selectedThumbnail,
  isFeatured,
  level,
  selectedEnglishSubtitle,
  selectedSpanishSubtitle,

  onVideoCrateSuccess = () => {},
  onVideoUploadSuccess = ({ data }) => {},
  onVideoCreateError = () => {},
}: handleCreateVideoProps) => {
  try {
    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}api/video/create`,
      {
        title,
        description,
        language,
        proficiencyLevel,
        categoriesId,
        categoriesName,
        userId,
        duration,
        collectionId,
        isFeatured,
        level,
      }
    );
    if (data.id && data._id) {
      const videoId = data.id;
      onVideoCrateSuccess();
      const mediaJobId = await createMediaJob({
        error: "",
        id: videoId,
        status: ASSETS_STATUS.STARTED,
        type: ASSETS_TYPES.VIDEO,
      });

      const videoKey = `${environment}/${videoId}/${file?.name}`;
      const thumbnailKey = `${environment}/${videoId}/thumbnail_${selectedThumbnail?.name}`;
      const subtitleEnglishKey = `${environment}/${videoId}/subtitle_${selectedEnglishSubtitle?.name}`;
      const subtitleSpanishKey = `${environment}/${videoId}/subtitle_${selectedSpanishSubtitle?.name}`;

      const uploadVideoData = await uploadFileOnAws({
        file,
        key: videoKey,
      });

      if (!uploadVideoData.isError && uploadVideoData?.data?.Key) {
        await updateMediaJob({
          error: "",
          id: mediaJobId,
          status: ASSETS_STATUS.READY,
        });

        const { isError: thumbnailError } = await handleUploadFileAndStatus({
          Key: thumbnailKey,
          file: selectedThumbnail,
          videoId,
          type: ASSETS_TYPES.THUMBNAIL,
        });

        const { isError: en_subtitleError } = await handleUploadFileAndStatus({
          Key: subtitleEnglishKey,
          file: selectedEnglishSubtitle,
          videoId,
          type: ASSETS_TYPES.CC,
        });
        const { isError: es_subtitleError } = await handleUploadFileAndStatus({
          Key: subtitleSpanishKey,
          file: selectedSpanishSubtitle,
          videoId,
          type: ASSETS_TYPES.CC,
        });

        const uploadedBothThumbAndSubtitle =
          !en_subtitleError && !es_subtitleError && !thumbnailError;

        const { data: updatedVideo } = await axios.put(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/video/update`,
          {
            id: data._id,
            thumbnailKey,
            subtitleEnglishKey,
            subtitleSpanishKey,
            videoKey,
            status: uploadedBothThumbAndSubtitle ? "ready" : "none",
          },
          {
            params: {
              getPopulated: true,
            },
          }
        );

        onVideoUploadSuccess({ data: updatedVideo });
      } else {
        updateMediaJob({
          error: uploadVideoData.message,
          id: mediaJobId,
          status: ASSETS_STATUS.FAILED,
        });
        onVideoCreateError();
      }
    } else {
      onVideoCreateError();
    }
  } catch (error) {
    onVideoCreateError();
  }
};

const handleUploadFileAndStatus = async ({ Key, file, videoId, type }) => {
  try {
    const mediaJobId = await createMediaJob({
      error: "",
      id: videoId,
      status: ASSETS_STATUS.STARTED,
      type,
    });

    const upload = await uploadFileOnAws({
      file,
      key: Key,
    });

    await updateMediaJob({
      error: upload.isError ? upload.message : "",
      id: mediaJobId,
      status: upload.isError ? ASSETS_STATUS.FAILED : ASSETS_STATUS.READY,
    });
    return {
      isError: upload.isError,
    };
  } catch (error) {
    const errorMessage = `Something went wrong in handleUploadFileAndStatus due to ${JSON.stringify(
      error
    )}`;
    console.error(errorMessage);
    return {
      isError: true,
    };
  }
};

type createMediaJobProps = {
  id: String;
  type: ValueOf<typeof ASSETS_TYPES>;
  status: ValueOf<typeof ASSETS_STATUS>;
  error: String;
};
const createMediaJob = async ({
  id,
  type,
  status,
  error,
}: createMediaJobProps) => {
  try {
    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}api/media-job/create`,
      {
        status,
        errorMsg: error,
        videoId: id,
        assetType: type,
      }
    );
    return data?.data?._id;
  } catch (error) {
    console.error(
      `Something went wrong in handleCreateMediaJob due to `,
      error
    );
  }
};

type updateMediaJobProps = {
  id: String;
  status: ValueOf<typeof ASSETS_STATUS>;
  error: String;
};
const updateMediaJob = async ({ id, status, error }: updateMediaJobProps) => {
  try {
    const { data } = await axios.put(
      `${process.env.NEXT_PUBLIC_BASE_URL}api/media-job/update`,
      {
        id,
        status,
        errorMsg: error,
      }
    );
    return data?._id;
  } catch (error) {
    console.error(
      `Something went wrong in handleCreateMediaJob due to `,
      error
    );
  }
};

export {
  validateUploadVideoFields,
  getCategoryNamesBasedOnId,
  handleCreateVideo,
};
