import fs, { promises as fsPromises } from "fs";
import path from "path";

const parentDir =
  "/Users/<USER>/Desktop/coding/patito/patito-feo-app/public/data/roots";
// const jsonFilePaths = [
//   `${parentDir}/poner.json`,
//   `${parentDir}/tener.json`,
//   `${parentDir}/hacer.json`,
//   `${parentDir}/decir.json`,
//   `${parentDir}/coger.json`,
//   `${parentDir}/ceder.json`,
//   `${parentDir}/venir.json`,
//   // Add more file paths as needed
// ];

const jsonFilePaths = [];

const createFilePaths = () => {
  return new Promise((resolve, reject) => {
    fs.readdir(`${parentDir}/individualRoots`, (err, files) => {
      if (err) {
        reject("Error getting directory information.");
      } else {
        files.forEach((file) => {
          jsonFilePaths.push(`${parentDir}/individualRoots/${file}`);
        });
        resolve();
      }
    });
  });
};

const styleData = {
  nodeStyles: [
    {
      group: 1,
      color: "#ff0000",
      textHeight: 6,
      fontFace: "Arial",
    },
    {
      group: 2,
      color: "#00FF00",
      textHeight: 6,
      fontFace: "Courier New",
    },
    {
      group: 3,
      color: "#0000FF",
      textHeight: 6,
      fontFace: "Times New Roman",
    },
    {
      group: 4,
      color: "#FFFF00",
      textHeight: 6,
      fontFace: "Verdana",
    },
    {
      group: 5,
      color: "#00FFFF",
      textHeight: 6,
      fontFace: "Georgia",
    },
    {
      group: 6,
      color: "#FF00FF",
      textHeight: 14,
      fontFace: "Comic Sans MS",
    },
    {
      group: 7,
      color: "#C0C0C0",
      textHeight: 6,
      fontFace: "Impact",
    },
  ],
  linkColor: "white",
  linkOpacity: 0.8,
  linkWidth: 0.2,
};

async function combineJSONFiles(filePaths) {
  await createFilePaths();

  const combinedData = {
    nodes: [],
    links: [],
  };

  for (const filePath of filePaths) {
    try {
      const fileContent = await fsPromises.readFile(filePath, "utf8");
      const jsonData = JSON.parse(fileContent);

      // Update group value for the root node only
      const rootNode = { ...jsonData.nodes[0], group: 1 };
      combinedData.nodes.push(rootNode);

      // Add other nodes and links as they are
      combinedData.nodes.push(...jsonData.nodes.slice(1));
      combinedData.links.push(...jsonData.links);
    } catch (err) {
      console.error(`Error reading or parsing file '${filePath}':`, err);
    }
  }

  return combinedData;
}

(async () => {
  try {
    const combinedData = await combineJSONFiles(jsonFilePaths);
    Object.assign(combinedData, styleData); // Merge the style data

    const combinedJsonOutput = JSON.stringify(combinedData, null, 2);
    const comboFile = path.join(parentDir, "roots.json");

    await fsPromises.writeFile(comboFile, combinedJsonOutput);
    console.log(`JSON file '${comboFile}' has been successfully created.`);
  } catch (err) {
    console.error("Error combining JSON files:", err);
  }
})();
