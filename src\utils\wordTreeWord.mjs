//Groups
// 1: parent root
// 2: verbs
// 3: nouns
// 4: adjectives
// 5: pastParticiples
// 6: gerunds
import { writeFile } from "fs/promises";
import { poner } from "../../public/data/roots/poner.mjs";
import { tener } from "../../public/data/roots/tener.mjs";

const parentDir =
  "/Users/<USER>/Desktop/coding/patito/patito-feo-app/public/data/roots/individualRoots";

const nodeStyles = [
  {
    group: 1,
    color: "#ff0000",
    textHeight: 6,
    fontFace: "Arial",
  },
  {
    group: 2,
    color: "#00FF00",
    textHeight: 6,
    fontFace: "Courier New",
  },
  {
    group: 3,
    color: "#0000FF",
    textHeight: 6,
    fontFace: "Times New Roman",
  },
  {
    group: 4,
    color: "#FFFF00",
    textHeight: 6,
    fontFace: "Verdana",
  },
  {
    group: 5,
    color: "#00FFFF",
    textHeight: 6,
    fontFace: "Georgia",
  },
  {
    group: 6,
    color: "#FF00FF",
    textHeight: 14,
    fontFace: "Comic Sans MS",
  },
  {
    group: 7,
    color: "#C0C0C0",
    textHeight: 6,
    fontFace: "Impact",
  },
];

function transformToJSONFormat(rootObj) {
  const nodes = [];
  const links = [];

  function addLinksForParent(parent, children, parentKey) {
    const linkedNodes = [];

    for (const child of children) {
      linkedNodes.push(child.id);
      nodes.push({ id: child.id, group: getGroupByKey(parentKey) });
    }

    links.push(
      ...linkedNodes.map((id) => ({ source: id, target: parent.id, value: 1 }))
    );
  }

  function getGroupByKey(key) {
    console.log(String(key));
    switch (String(key)) {
      case "verbs":
        return 2;
      case "nouns":
        return 3;
      case "adjectives":
        return 4;
      case "pastParticiples":
        return 5;
      case "gerunds":
        return 6;
      default:
        return 1;
    }
  }

  nodes.push({ id: rootObj.id, group: 1 });
  // nodes.push({ id: rootObj.id, group: getGroupByKey("verbs") });

  addLinksForParent(rootObj, rootObj.nodes.verbs, "verbs");

  for (const verb of rootObj.nodes.verbs) {
    nodes.push({ id: verb.id, group: getGroupByKey("verbs") });
    addLinksForParent(verb, verb.nodes.nouns, "nouns");
    addLinksForParent(verb, verb.nodes.adjectives, "adjectives");
    addLinksForParent(verb, verb.nodes.pastParticiples, "pastParticiples");
    addLinksForParent(verb, verb.nodes.gerunds, "gerunds");
  }

  const uniqueNodes = nodes.filter(
    (v, i, a) => a.findIndex((t) => t.id === v.id) === i
  );

  return {
    nodes: uniqueNodes,
    links,
    nodeStyles,
    linkColor: "white",
    linkOpacity: 0.8,
    linkWidth: 0.2,
  };
}

(async () => {
  const transformedJSON = transformToJSONFormat(tener); // Use "tener" object here

  const fileName = `${parentDir}/${tener.id}.json`; // Use "tener" ID here

  try {
    await writeFile(fileName, JSON.stringify(transformedJSON, null, 2));
    console.log(`JSON file '${fileName}' has been successfully created.`);
  } catch (err) {
    console.error("Error writing the JSON file:", err);
  }
})();
